name: 'Create Update Release'

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 1.0.1)'
        required: true
        type: string

jobs:
  create-update-bundle:
    name: 'Create Update Bundle and Release'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Set version from input or tag
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
            echo "Setting version to: $VERSION"
            npm version $VERSION --no-git-tag-version
          else
            VERSION=${GITHUB_REF#refs/tags/v}
            echo "Using tag version: $VERSION"
            # For tag pushes, the version should already be set in package.json
            # Just verify it matches and use it
            CURRENT_VERSION=$(node -p "require('./package.json').version")
            if [ "$CURRENT_VERSION" = "$VERSION" ]; then
              echo "Version already set correctly: $VERSION"
            else
              echo "Warning: package.json version ($CURRENT_VERSION) doesn't match tag ($VERSION)"
              echo "Using tag version and updating package.json"
              npm version $VERSION --no-git-tag-version
            fi
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Build application
        run: npm run build

      - name: Create update bundle
        run: |
          cd dist
          zip -r ../update-bundle-${{ steps.version.outputs.version }}.zip .
          cd ..

          # Create checksum
          sha256sum update-bundle-${{ steps.version.outputs.version }}.zip > update-bundle-${{ steps.version.outputs.version }}.zip.sha256

      - name: Create Release (Private Repo)
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.version.outputs.version }}
          name: Release v${{ steps.version.outputs.version }}
          body: |
            ## Changes in v${{ steps.version.outputs.version }}

            This release contains an over-the-air update bundle for the mobile app.

            ### Installation
            - For users: The app will automatically check for and install this update
            - For developers: Download the update bundle below for manual testing

            ### Files
            - `update-bundle-${{ steps.version.outputs.version }}.zip`: The update bundle
            - `update-bundle-${{ steps.version.outputs.version }}.zip.sha256`: Checksum for verification
          files: |
            update-bundle-${{ steps.version.outputs.version }}.zip
            update-bundle-${{ steps.version.outputs.version }}.zip.sha256
          draft: false
          prerelease: false

      - name: Create Release (Public Releases Repo)
        uses: softprops/action-gh-release@v1
        with:
          repository: mlolpet/syner-biz-releases
          tag_name: v${{ steps.version.outputs.version }}
          name: Release v${{ steps.version.outputs.version }}
          body: |
            ## Syner-Biz Mobile App Update v${{ steps.version.outputs.version }}

            This is an over-the-air update bundle for the Syner-Biz mobile application.

            ### Automatic Installation
            - Mobile app users will receive this update automatically
            - No action required from users

            ### Manual Installation (Developers)
            - Download the update bundle below for testing
            - Use the debug panel in the app to force update checks

            ### Files
            - `update-bundle-${{ steps.version.outputs.version }}.zip`: The update bundle
            - `update-bundle-${{ steps.version.outputs.version }}.zip.sha256`: Checksum for verification

            ### Release Date
            Released on: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          files: |
            update-bundle-${{ steps.version.outputs.version }}.zip
            update-bundle-${{ steps.version.outputs.version }}.zip.sha256
          draft: false
          prerelease: false
          token: ${{ secrets.PUBLIC_REPO_TOKEN }}

      - name: Deploy to Netlify (for web version)
        uses: jsmrcaga/action-netlify-deploy@master
        with:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
          NETLIFY_DEPLOY_MESSAGE: "Release deploy v${{ steps.version.outputs.version }}"
          NETLIFY_DEPLOY_TO_PROD: true
          build_directory: 'dist'
