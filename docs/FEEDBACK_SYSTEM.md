# 用戶反饋系統 (User Feedback System)

## 概述 (Overview)

用戶反饋系統允許用戶向開發團隊提交意見、建議和錯誤報告。系統支持匿名和實名反饋，並提供管理員界面來處理和回應用戶反饋。

## 功能特點 (Features)

### 用戶功能
- ✅ **提交反饋**: 用戶可以通過個人資料頁面的"意見反饋"按鈕提交反饋
- ✅ **反饋分類**: 支持多種反饋類型（錯誤回報、功能建議、改進建議、介面問題、效能問題、其他）
- ✅ **匿名選項**: 用戶可以選擇匿名提交反饋
- ✅ **聯絡信息**: 可選提供電郵地址以便回覆
- ✅ **查看反饋記錄**: 用戶可以查看自己提交的非匿名反饋記錄和狀態

### 管理員功能
- ✅ **反饋管理**: 管理員可以查看所有用戶反饋
- ✅ **狀態更新**: 可以更新反饋狀態（待處理、處理中、已解決、已關閉）
- ✅ **統計數據**: 顯示反饋總數、待處理數量等統計信息
- ✅ **內部備註**: 管理員可以添加內部備註
- ✅ **分類篩選**: 按狀態篩選反饋

## 系統架構 (Architecture)

### 數據庫表結構
```sql
user_feedback (
  id uuid PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id),
  category text NOT NULL,
  subject text NOT NULL,
  description text NOT NULL,
  contact_email text,
  is_anonymous boolean DEFAULT false,
  status text DEFAULT 'pending',
  admin_notes text,
  admin_response text,
  responded_by uuid REFERENCES auth.users(id),
  responded_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

### 前端組件
- **`FeedbackModal.vue`**: 反饋提交表單模態框
- **`AdminFeedbackPage.vue`**: 管理員反饋管理頁面
- **`MyFeedbackPage.vue`**: 用戶個人反饋記錄頁面

### 路由配置
- `/profile` - 個人資料頁面（包含反饋按鈕）
- `/my-feedback` - 我的反饋記錄
- `/admin/feedback` - 管理員反饋管理（僅管理員可訪問）

## 使用方法 (Usage)

### 用戶提交反饋
1. 登入應用
2. 進入個人資料頁面
3. 點擊"意見反饋"按鈕
4. 填寫反饋表單：
   - 選擇反饋類型
   - 輸入主題
   - 詳細描述問題或建議
   - 可選：提供聯絡電郵
   - 可選：選擇匿名提交
5. 點擊"提交反饋"

### 查看反饋記錄
1. 在個人資料頁面點擊"我的反饋"
2. 查看已提交的反饋列表
3. 點擊任意反饋查看詳情和狀態

### 管理員管理反饋
1. 以管理員身份登入
2. 在個人資料頁面點擊"用戶反饋管理"
3. 查看反饋統計和列表
4. 使用狀態篩選器篩選反饋
5. 點擊反饋查看詳情
6. 更新反饋狀態和添加備註

## 安全性 (Security)

### 行級安全 (RLS) 政策
- 用戶只能查看自己的非匿名反饋
- 支持匿名反饋提交
- 管理員可以查看和管理所有反饋
- 自動更新時間戳

### 數據驗證
- 必填字段驗證
- 反饋類型和狀態的枚舉約束
- 字符長度限制

## 反饋類型 (Feedback Categories)

| 類型 | 描述 |
|------|------|
| bug | 錯誤回報 - 應用程序錯誤或故障 |
| feature | 功能建議 - 新功能請求 |
| improvement | 改進建議 - 現有功能改進 |
| ui | 介面問題 - 用戶界面相關問題 |
| performance | 效能問題 - 性能相關問題 |
| other | 其他 - 不屬於上述類別的反饋 |

## 反饋狀態 (Feedback Status)

| 狀態 | 描述 |
|------|------|
| pending | 待處理 - 新提交的反饋 |
| in_progress | 處理中 - 正在處理的反饋 |
| resolved | 已解決 - 已處理完成的反饋 |
| closed | 已關閉 - 不需要進一步處理的反饋 |

## 技術實現 (Technical Implementation)

### 數據庫函數
- `get_feedback_stats()`: 獲取反饋統計數據
- `update_user_feedback_updated_at()`: 自動更新時間戳

### 前端技術
- Vue 3 Composition API
- Ionic Vue 組件
- Supabase 客戶端
- TypeScript 類型安全

## 未來改進 (Future Enhancements)

- [ ] 電郵通知系統
- [ ] 反饋投票功能
- [ ] 反饋標籤系統
- [ ] 批量操作功能
- [ ] 反饋搜索功能
- [ ] 導出反饋數據
- [ ] 反饋分析儀表板

## 維護說明 (Maintenance Notes)

- 定期檢查反饋統計數據
- 及時回應用戶反饋
- 監控數據庫性能
- 定期清理已關閉的舊反饋（可選）
