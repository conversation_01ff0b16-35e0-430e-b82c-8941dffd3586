# Capacitor Updater Implementation Guide

This document explains how to use the @capgo/capacitor-updater implementation for over-the-air updates using **manual setup**.

## Overview

The app supports over-the-air updates using GitHub Releases as the distribution method with manual update control. This allows you to push updates to users without going through the App Store or Play Store review process, while maintaining full control over when and how updates are applied.

## How It Works

1. **Manual Setup**: `autoUpdate` is set to `false` - we control the entire update process
2. **Version Management**: App versions are managed in `package.json`
3. **Build & Release**: GitHub Actions automatically create releases with update bundles
4. **Update Detection**: App manually checks for updates using GitHub API
5. **Download & Install**: Updates are downloaded and applied manually through our service
6. **Activation**: Updates take effect on next app restart after calling `notifyAppReady()`

## Usage

### Creating a New Release

#### Method 1: Using the Script (Recommended)
```bash
# Create a patch release (1.0.0 → 1.0.1)
./create-release.sh patch

# Create a minor release (1.0.1 → 1.1.0)
./create-release.sh minor

# Create a major release (1.1.0 → 2.0.0)
./create-release.sh major

# With custom commit message
./create-release.sh patch "Fix critical bug in payment system"
```

#### Method 2: Manual Process
```bash
# 1. Update version
npm run version:patch  # or version:minor, version:major

# 2. Build and create bundle
npm run build:update

# 3. Commit and tag
git add package.json package-lock.json
git commit -m "Release v1.0.1"
git tag v1.0.1

# 4. Push
git push origin main
git push origin v1.0.1
```

#### Method 3: GitHub Actions Manual Trigger
1. Go to GitHub Actions tab
2. Select "Create Update Release" workflow
3. Click "Run workflow"
4. Enter the version number (e.g., 1.0.1)
5. Click "Run workflow"

### Monitoring Updates

#### Debug Panel
- **Development**: Automatically visible in dev mode
- **Production**: Add `?debug=true` to URL to show debug panel
- **Features**:
  - View current version
  - Check for updates manually
  - View update logs
  - Force reload app

#### Console Logs
Check browser/device console for update-related logs:
```
UpdateService: Initialized with version 1.0.0
UpdateService: Checking for updates...
UpdateService: New version available: 1.0.1
UpdateService: Download complete, setting bundle
```

## Configuration

### Capacitor Config
The app uses manual setup configuration in `capacitor.config.ts`:

```typescript
CapacitorUpdater: {
  autoUpdate: false,          // Manual setup - we handle updates ourselves
  updateUrl: '',              // Not used in manual mode
  directUpdate: false,        // We control when updates are applied
  resetWhenUpdate: true,      // Reset app state on update
  updateAvailable: false,     // We don't use automatic update events
  checkDelay: 0,             // Not used in manual mode
  defaultChannel: 'production'
}
```

### Manual Update Process
1. **App Initialization**: Call `CapacitorUpdater.notifyAppReady()` to inform the plugin that the app loaded successfully
2. **Version Detection**: Use `CapacitorUpdater.current()` to get the current bundle version (not native app version)
3. **Update Checking**: Manually check GitHub API for new releases and compare versions
4. **Download**: Use `CapacitorUpdater.download()` to download update bundles
5. **Installation**: Use `CapacitorUpdater.set()` to set the new bundle
6. **Activation**: Update takes effect on next app restart

### Version Management
- **Current Bundle Version**: `CapacitorUpdater.current().bundle.version` - Version of currently running bundle
- **Native App Version**: `CapacitorUpdater.getBuiltinVersion().version` - Version from package.json at build time
- **Version Comparison**: Always compare against current bundle version, not native app version

### Manual vs Automatic Setup

**Why Manual Setup?**
- **Full Control**: We control exactly when updates are checked, downloaded, and applied
- **Better Error Handling**: Custom logic for handling update failures and retries
- **Rate Limiting**: Prevent excessive API calls with built-in rate limiting
- **Custom Logic**: Ability to add custom validation, user prompts, or conditional updates
- **No Capgo Cloud Dependency**: Works without requiring Capgo cloud service subscription

**Key Differences:**
- `autoUpdate: false` - We handle the entire update process
- `notifyAppReady()` - Required call to prevent rollback to previous version
- Manual GitHub API calls - We fetch release information directly
- Custom download logic - We control when and how updates are downloaded

### GitHub Repository
Make sure your repository is public or configure proper access tokens for private repos.

## File Structure

```
├── src/services/updateService.ts     # Main update service
├── src/components/UpdateDebugPanel.vue  # Debug component
├── create-release.sh         # Release automation script
├── .github/workflows/release-update.yml  # GitHub Actions workflow
└── docs/CAPACITOR_UPDATER.md        # This documentation
```

## Troubleshooting

### Common Issues

1. **Updates not detected**
   - Check GitHub repository URL in `capacitor.config.ts`
   - Verify release was created successfully
   - Check console logs for errors

2. **Download fails**
   - Ensure update bundle exists in release assets
   - Check network connectivity
   - Verify bundle format (should be .zip)

3. **Update doesn't apply**
   - Check if app restarted after download
   - Look for bundle setting errors in logs
   - Verify bundle integrity

### Debug Steps

1. **Enable debug panel**: Add `?debug=true` to URL
2. **Check console logs**: Look for UpdateService messages
3. **Verify release**: Check GitHub releases page
4. **Test manually**: Use "Check for Updates" button in debug panel

## Best Practices

### Version Management
- Use semantic versioning (MAJOR.MINOR.PATCH)
- Increment patch for bug fixes
- Increment minor for new features
- Increment major for breaking changes

### Release Process
- Test updates thoroughly before release
- Use descriptive commit messages
- Monitor update adoption rates
- Keep release notes updated

### Security
- Only include necessary files in update bundles
- Verify bundle integrity with checksums
- Monitor for unauthorized releases

## Limitations

### What CAN be updated:
- JavaScript/TypeScript code
- CSS/SCSS styles
- HTML templates
- Images and assets
- Configuration files

### What CANNOT be updated:
- Native plugins
- Capacitor configuration
- App permissions
- Native code changes

For native changes, you still need to release through app stores.

## Support

For issues related to:
- **Capacitor Updater**: Check [official documentation](https://github.com/Cap-go/capacitor-updater)
- **GitHub Actions**: Check workflow logs in Actions tab
- **App-specific issues**: Check console logs and debug panel
