# Private Repository Setup for Capacitor Updater

Since your main repository is private, we need to set up a public repository for hosting the update releases.

## 🎯 Setup Steps

### Step 1: Create Public Releases Repository

1. Go to GitHub and create a **new public repository**
2. Name it: `syner-biz-releases`
3. Description: "Public releases for Syner-Biz mobile app updates"
4. Make sure it's **PUBLIC**
5. Initialize with a README

### Step 2: Create Personal Access Token

1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Name: `Syner-Biz Releases Token`
4. Expiration: Choose appropriate duration (1 year recommended)
5. Scopes: Select **only** `public_repo` (not full repo access)
6. Click "Generate token"
7. **Copy the token immediately** (you won't see it again)

### Step 3: Add Token to Repository Secrets

1. Go to your **private** repository: `mlolpet/syner-biz`
2. Settings → Secrets and variables → Actions
3. Click "New repository secret"
4. Name: `PUBLIC_REPO_TOKEN`
5. Value: Paste the token you copied
6. Click "Add secret"

### Step 4: Test the Setup

Run a test release to verify everything works:

```bash
./scripts/create-release.sh patch "Test public releases setup"
```

This should:
1. Create a release in your private repo
2. Automatically create the same release in the public repo
3. Upload update bundles to both repositories

## 🔍 Verification

After running the test release, check:

1. **Private repo release**: https://github.com/mlolpet/syner-biz/releases
2. **Public repo release**: https://github.com/mlolpet/syner-biz-releases/releases
3. **GitHub Actions**: https://github.com/mlolpet/syner-biz/actions

Both repositories should have the same release with the same files.

## 📱 App Configuration

The app is now configured to check for updates from the **public** repository:
- Update URL: `https://api.github.com/repos/mlolpet/syner-biz-releases/releases/latest`
- This allows the app to access releases without authentication

## 🔒 Security Benefits

- **Private code**: Your source code remains private
- **Public releases**: Only compiled app bundles are public
- **Limited access**: Token only has access to create public releases
- **No source exposure**: No source code or sensitive data in public repo

## 🚀 Workflow

```
Private Repo (mlolpet/syner-biz)
├── Source code (private)
├── GitHub Actions
└── Creates releases in both repos
    ├── Private release (for team)
    └── Public release (for app updates)

Public Repo (mlolpet/syner-biz-releases)
├── Only releases (no source code)
├── Update bundles
└── Accessible by mobile app
```

## 🛠️ Alternative Options

If you prefer not to use a public repository, here are alternatives:

### Option A: GitHub Pages
- Host update bundles on GitHub Pages
- Requires setting up a separate build process

### Option B: CDN/Cloud Storage
- Upload bundles to AWS S3, Google Cloud, or similar
- Requires additional configuration and costs

### Option C: Capgo Cloud Service
- Use the official Capgo hosting service
- Paid service but handles everything automatically

### Option D: Make Repository Public
- Simplest solution but exposes source code
- Not recommended for commercial projects

## 📋 Troubleshooting

### Token Issues
- Ensure token has `public_repo` scope
- Check token hasn't expired
- Verify secret name is exactly `PUBLIC_REPO_TOKEN`

### Repository Issues
- Ensure public repo exists and is public
- Check repository name matches in workflow file
- Verify you have admin access to public repo

### Workflow Issues
- Check GitHub Actions logs for detailed errors
- Ensure both repositories exist before running workflow
- Verify token permissions

## 🔄 Maintenance

- **Token renewal**: Update token before expiration
- **Repository cleanup**: Optionally clean old releases
- **Monitoring**: Check both repositories for successful releases

The dual-repository setup provides the best balance of security and functionality for private projects with public app updates.
