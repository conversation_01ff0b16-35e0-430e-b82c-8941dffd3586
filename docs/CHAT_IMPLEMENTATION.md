# Chat Implementation with Supabase Realtime

This document outlines the implementation of a minimal chatroom functionality using Supabase realtime for user-to-user messaging.

## 🎯 Features Implemented

### Core Features
- ✅ Real-time messaging between users
- ✅ Text messages
- ✅ Image messages (with upload to Supabase storage)
- ✅ Message read status tracking
- ✅ Conversation list with last message preview
- ✅ Message timestamps with smart formatting
- ✅ Auto-scroll to bottom on new messages
- ✅ Integration with existing user profile system

### UI/UX Features
- ✅ WhatsApp-like message bubbles (different colors for sent/received)
- ✅ Image modal for viewing full-size images
- ✅ Loading states for message sending
- ✅ Empty state for new conversations
- ✅ Navigation from user profiles to start conversations
- ✅ Messages link in profile page

## 📊 Database Schema

### Tables Created

#### `conversations`
```sql
- id (uuid, primary key)
- participant_1_id (uuid, references auth.users)
- participant_2_id (uuid, references auth.users)
- last_message_id (uuid, references messages)
- last_message_at (timestamptz)
- created_at (timestamptz)
- updated_at (timestamptz)
```

#### `messages`
```sql
- id (uuid, primary key)
- conversation_id (uuid, references conversations)
- sender_id (uuid, references auth.users)
- content (text)
- message_type (text: 'text', 'image', 'voice', 'file')
- metadata (jsonb)
- created_at (timestamptz)
- updated_at (timestamptz)
```

#### `message_read_status`
```sql
- id (uuid, primary key)
- message_id (uuid, references messages)
- user_id (uuid, references auth.users)
- read_at (timestamptz)
```

### Security (RLS Policies)
- Users can only see conversations they participate in
- Users can only see messages in their conversations
- Users can only mark messages as read in their conversations
- Proper foreign key constraints and checks

### Database Triggers
- Auto-update conversation `last_message_at` and `last_message_id` on new messages
- Auto-update `updated_at` timestamps

## 🏗️ Architecture

### Frontend Components

#### Stores
- **`src/stores/chat.ts`** - Pinia store for chat state management
  - Conversation management
  - Message handling
  - Realtime subscriptions
  - Read status tracking

#### Pages
- **`src/views/ChatroomPage.vue`** - Main chat interface
  - Real-time messaging
  - Image upload and display
  - Message composition
  - Auto-scroll functionality

- **`src/views/ConversationListPage.vue`** - List of user conversations
  - Sorted by last message time
  - Last message preview
  - User avatars and names

#### Integration
- **`src/views/UserProfilePage.vue`** - Updated "發送訊息" button
  - Now navigates to chatroom instead of showing placeholder
  - Creates or finds existing conversation

- **`src/views/ProfilePage.vue`** - Added "訊息" link
  - Easy access to conversation list from profile

### Routes Added
```typescript
// Chat / Messages
{
  path: '/conversations',
  component: () => import('@/views/ConversationListPage.vue'),
  meta: { requiresAuth: true }
},
{
  path: '/chat/:conversationId',
  component: () => import('@/views/ChatroomPage.vue'),
  meta: { requiresAuth: true }
},
{
  path: '/chat/new/:otherUserId',
  component: () => import('@/views/ChatroomPage.vue'),
  meta: { requiresAuth: true }
}
```

## 🔄 Realtime Implementation

### Supabase Realtime
- Subscribes to new messages in current conversation
- Automatically updates UI when new messages arrive
- Handles message read status updates
- Cleans up subscriptions on component unmount

### Message Flow
1. User types message and clicks send
2. Message is inserted into database
3. Database trigger updates conversation metadata
4. Realtime subscription notifies other participant
5. Message appears in both users' chat interfaces
6. Read status is automatically marked for recipient

## 🖼️ Image Handling

### Upload Process
1. User selects image using Capacitor Camera plugin
2. Image is converted to base64 format
3. Uploaded to Supabase storage (`products` bucket, `chat-images` folder)
4. Public URL is stored as message content
5. Message type is set to 'image'

### Display
- Images are displayed inline in chat bubbles
- Click to open full-size modal
- Proper loading states during upload

## 🚀 Usage

### Starting a Conversation
1. Go to any user's profile page
2. Click "發送訊息" button
3. Automatically creates or finds existing conversation
4. Redirects to chatroom interface

### Viewing Conversations
1. Go to Profile page
2. Click "訊息" under "我的人脈" section
3. See list of all conversations
4. Click any conversation to open chatroom

### Sending Messages
- Type text and press Enter or click send button
- Click image icon to upload and send photos
- Messages appear immediately with loading states
- Auto-scroll to bottom on new messages

## 🔮 Future Enhancements

### Planned Features
- Voice messages (infrastructure already in place)
- File attachments
- Message reactions
- Typing indicators
- Push notifications for new messages
- Message search
- Conversation archiving
- Group chats

### Technical Improvements
- Message pagination for large conversations
- Offline message queuing
- Message encryption
- Better error handling
- Performance optimizations

## 🛠️ Development Notes

### Dependencies Added
- `date-fns` - For message timestamp formatting

### Existing Dependencies Used
- `@supabase/supabase-js` - Database and realtime
- `@ionic/vue` - UI components
- `@capacitor/camera` - Image capture
- `pinia` - State management

### Storage
- Uses existing Supabase storage bucket (`products`)
- Images stored in `chat-images` subfolder
- Leverages existing `uploadImageToSupabase` utility

## 🔒 Security Considerations

- All database operations protected by RLS policies
- Users can only access their own conversations
- Image uploads use secure Supabase storage
- No sensitive data exposed in client-side code
- Proper authentication checks throughout

## 📱 Mobile Compatibility

- Responsive design works on all screen sizes
- Uses Capacitor Camera for native photo access
- Touch-friendly interface
- Proper keyboard handling
- Auto-scroll behavior optimized for mobile
