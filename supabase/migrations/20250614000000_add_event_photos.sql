/*
  # Add Event Photos Table

  1. New Tables
    - `event_photos`
      - `id` (UUID, primary key) - Unique identifier for each photo
      - `event_id` (UUID) - References events(id)
      - `photo_url` (text) - URL of the photo
      - `caption` (text) - Optional caption for the photo
      - `order` (integer) - Order for display sorting
      - `photo_type` (text) - 'creation' or 'post_event' to distinguish when photo was added
      - `uploaded_by` (UUID) - References users(id) - who uploaded the photo

  2. Security
    - Enable RLS on event_photos table
    - Add policies to:
      - Allow anyone to view event photos
      - Allow event owners to manage creation photos
      - Allow admins to manage post-event photos
*/

-- Create event_photos table
CREATE TABLE event_photos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  photo_url text NOT NULL,
  caption text,
  "order" integer NOT NULL DEFAULT 0,
  photo_type text NOT NULL CHECK (photo_type IN ('creation', 'post_event')) DEFAULT 'creation',
  uploaded_by uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE event_photos ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX event_photos_event_id_idx ON event_photos(event_id);
CREATE INDEX event_photos_order_idx ON event_photos("order");
CREATE INDEX event_photos_photo_type_idx ON event_photos(photo_type);
CREATE INDEX event_photos_uploaded_by_idx ON event_photos(uploaded_by);

-- Create RLS Policies

-- Anyone can view event photos
CREATE POLICY "Anyone can view event photos"
  ON event_photos
  FOR SELECT
  TO public
  USING (true);

-- Event owners can manage creation photos for their events
CREATE POLICY "Event owners can manage creation photos"
  ON event_photos
  FOR ALL
  TO authenticated
  USING (
    photo_type = 'creation' AND
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_photos.event_id
      AND events.user_id = auth.uid()
    )
  )
  WITH CHECK (
    photo_type = 'creation' AND
    uploaded_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_photos.event_id
      AND events.user_id = auth.uid()
    )
  );

-- Admins can manage post-event photos for any event
CREATE POLICY "Admins can manage post-event photos"
  ON event_photos
  FOR ALL
  TO authenticated
  USING (
    photo_type = 'post_event' AND
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.is_admin = true
    )
  )
  WITH CHECK (
    photo_type = 'post_event' AND
    uploaded_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.is_admin = true
    )
  );

-- Event owners can also manage post-event photos for their own events
CREATE POLICY "Event owners can manage post-event photos for their events"
  ON event_photos
  FOR ALL
  TO authenticated
  USING (
    photo_type = 'post_event' AND
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_photos.event_id
      AND events.user_id = auth.uid()
    )
  )
  WITH CHECK (
    photo_type = 'post_event' AND
    uploaded_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_photos.event_id
      AND events.user_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER event_photos_updated_at
  BEFORE UPDATE ON event_photos
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Add Cloudflare image deletion trigger (similar to product_photos)
CREATE OR REPLACE FUNCTION delete_event_photo_from_cloudflare()
RETURNS TRIGGER AS $$
DECLARE
  config_record RECORD;
  image_id TEXT;
  response_status INTEGER;
BEGIN
  -- Extract image ID from URL (assuming Cloudflare Images URL format)
  IF OLD.photo_url LIKE '%imagedelivery.net%' THEN
    -- Extract the image ID from the URL
    image_id := substring(OLD.photo_url from '/([a-zA-Z0-9-]+)/public$');
    
    IF image_id IS NOT NULL THEN
      -- Get Cloudflare configuration
      SELECT account_id, api_key INTO config_record
      FROM cloudflare_config
      ORDER BY created_at DESC
      LIMIT 1;
      
      IF config_record IS NOT NULL THEN
        -- Delete image from Cloudflare
        SELECT status INTO response_status
        FROM http((
          'DELETE',
          'https://api.cloudflare.com/client/v4/accounts/' || config_record.account_id || '/images/v1/' || image_id,
          ARRAY[
            http_header('Authorization', 'Bearer ' || config_record.api_key),
            http_header('Content-Type', 'application/json')
          ],
          'application/json',
          ''
        )::http_request);
        
        -- Log the result (optional)
        RAISE NOTICE 'Cloudflare image deletion response: %', response_status;
      END IF;
    END IF;
  END IF;
  
  RETURN OLD;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the deletion
    RAISE NOTICE 'Error deleting image from Cloudflare: %', SQLERRM;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic Cloudflare cleanup
CREATE TRIGGER delete_event_photo_cloudflare_trigger
  AFTER DELETE ON event_photos
  FOR EACH ROW
  EXECUTE FUNCTION delete_event_photo_from_cloudflare();
