/*
  # Add Event Likes Functionality

  1. Changes
    - Add like_count column to events table
    - Create user_liked_events table for tracking user likes
    - Add triggers to automatically update like_count
    
  2. Security
    - Enable RLS on new table
    - Set up policies similar to user_liked_shops
*/

-- Add like_count column to events table if it doesn't exist
ALTER TABLE events
ADD COLUMN IF NOT EXISTS like_count integer DEFAULT 0;

-- Create user_liked_events table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_liked_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, event_id)
);

-- Enable Row Level Security if not already enabled
ALTER TABLE user_liked_events ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance if they don't exist
CREATE INDEX IF NOT EXISTS user_liked_events_user_id_idx ON user_liked_events(user_id);
CREATE INDEX IF NOT EXISTS user_liked_events_event_id_idx ON user_liked_events(event_id);

-- Create RLS Policies for user_liked_events if they don't exist

-- Users can view all liked events
CREATE POLICY "Anyone can view liked events"
  ON user_liked_events
  FOR SELECT
  TO public
  USING (true);

-- Users can manage their own liked events
CREATE POLICY "Users can manage their own liked events"
  ON user_liked_events
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create trigger function to update event like_count
CREATE OR REPLACE FUNCTION update_event_like_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Increment like_count
    UPDATE events
    SET like_count = like_count + 1
    WHERE id = NEW.event_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement like_count
    UPDATE events
    SET like_count = GREATEST(0, like_count - 1)
    WHERE id = OLD.event_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for like count updates
DROP TRIGGER IF EXISTS update_event_like_count_insert ON user_liked_events;
CREATE TRIGGER update_event_like_count_insert
  AFTER INSERT ON user_liked_events
  FOR EACH ROW
  EXECUTE FUNCTION update_event_like_count();

DROP TRIGGER IF EXISTS update_event_like_count_delete ON user_liked_events;
CREATE TRIGGER update_event_like_count_delete
  AFTER DELETE ON user_liked_events
  FOR EACH ROW
  EXECUTE FUNCTION update_event_like_count();
