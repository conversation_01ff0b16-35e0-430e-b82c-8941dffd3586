/*
  # Fix Branch Member Application Approval

  1. Changes
    - Fix the trigger that adds members when applications are approved
    - Ensure branch_members are properly created with active status
    
  2. Security
    - Maintain existing RLS policies
*/

-- Drop the existing trigger and function
DROP TRIGGER IF EXISTS handle_approved_application_trigger ON branch_member_applications;
DROP FUNCTION IF EXISTS handle_approved_application();

-- Create an improved function to handle approved applications
CREATE OR REPLACE FUNCTION handle_approved_application()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'approved' AND OLD.status = 'pending' THEN
    -- Add user to branch members with active status
    INSERT INTO branch_members (branch_id, user_id, status, joined_at)
    VALUES (NEW.branch_id, NEW.user_id, 'active', NOW())
    ON CONFLICT (branch_id, user_id) 
    DO UPDATE SET 
      status = 'active',
      joined_at = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to handle approved applications
CREATE TRIGGER handle_approved_application_trigger
  AFTER UPDATE ON branch_member_applications
  FOR EACH ROW
  EXECUTE FUNCTION handle_approved_application();
