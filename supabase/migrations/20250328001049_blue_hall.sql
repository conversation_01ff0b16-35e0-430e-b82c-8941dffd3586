/*
  # Update Products Table ID Column

  1. Changes
    - Rename product_id to id
    - Keep all other columns and constraints unchanged
    
  2. Security
    - Maintain existing RLS policies
    - No security changes needed
*/

-- Drop existing table and its dependencies
DROP TABLE IF EXISTS products CASCADE;

-- Create updated products table
CREATE TABLE products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shop_id uuid REFERENCES shops(id),
  title text NOT NULL,
  description text,
  price numeric NOT NULL CHECK (price >= 0),
  is_in_stock boolean DEFAULT true,
  profit_sharing_rate numeric NOT NULL CHECK (profit_sharing_rate >= 0 AND profit_sharing_rate <= 100),
  cover_image text,
  status text DEFAULT 'active' CHECK (status IN ('active', 'trashed')),
  created_by text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX products_shop_id_idx ON products(shop_id);
CREATE INDEX products_status_idx ON products(status);

-- Create RLS Policies

-- Anyone can view active products
CREATE POLICY "Anyone can view active products"
  ON products
  FOR SELECT
  TO authenticated
  USING (status = 'active');

-- Shop owners can view all their products
CREATE POLICY "Shop owners can view all their products"
  ON products
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM shops
      WHERE shops.id = products.shop_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Shop owners can manage their products
CREATE POLICY "Shop owners can manage their products"
  ON products
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM shops
      WHERE shops.id = products.shop_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();