/*
  # Add User Profile Creation Trigger

  1. Changes
    - Remove manual profile creation from frontend
    - Add trigger to automatically create user profile on auth.users insert
    - Simplify RLS policies
    
  2. Security
    - Maintain RLS for data access
    - Remove direct insert access to users table
    - Handle profile creation automatically
*/

-- Drop the registration policy as we'll handle it via trigger
DROP POLICY IF EXISTS "Enable insert for registration" ON users;

-- <PERSON>reate function to handle user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (
    id,
    username,
    full_name,
    email,
    phone,
    role,
    referrer_id,
    industry,
    company_name
  ) VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'username',
    NEW.raw_user_meta_data->>'full_name',
    NEW.email,
    NEW.raw_user_meta_data->>'phone',
    NEW.raw_user_meta_data->>'role',
    (NEW.raw_user_meta_data->>'referrer_id')::uuid,
    NEW.raw_user_meta_data->>'industry',
    NEW.raw_user_meta_data->>'company_name'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON><PERSON> trigger on auth.users
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Simplify RLS policies
CREATE POLICY "Enable read access to own profile"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Enable update access to own profile"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);