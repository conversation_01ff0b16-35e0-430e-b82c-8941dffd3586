/*
  # Add Referral Code System

  1. Changes
    - Add referral_code column to users table
    - Create function to generate unique referral codes
    - Add trigger to automatically generate codes for new users
    - Add function to verify referral codes
    - Update user creation trigger to handle referral codes
    
  2. Security
    - Ensure referral codes are unique
    - Validate code format
    - Add policy for referral code lookups
*/

-- Add referral_code column
ALTER TABLE users
ADD COLUMN referral_code text UNIQUE;

-- Create function to generate random referral code
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS text AS $$
DECLARE
  chars text := '123456789ABCDEFGHIJKLMNPQRSTUVWXYZ'; -- Excluding 0 and O
  code text := '';
  i integer;
BEGIN
  -- Generate 6 character code
  FOR i IN 1..6 LOOP
    code := code || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  
  -- Check if code exists and regenerate if needed
  WHILE EXISTS (SELECT 1 FROM users WHERE referral_code = code) LOOP
    code := '';
    FOR i IN 1..6 LOOP
      code := code || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
  END LOOP;
  
  RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Create function to verify referral code
CREATE OR REPLACE FUNCTION verify_referral_code(code text)
RETURNS TABLE (
  id uuid,
  username text,
  full_name text
) AS $$
BEGIN
  RETURN QUERY
  SELECT u.id, u.username, u.full_name
  FROM users u
  WHERE u.referral_code = code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update handle_new_user function to handle referral codes
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_role text;
  new_industry text;
  new_company_name text;
  new_username text;
  new_full_name text;
  new_phone text;
  referrer_id uuid;
  new_referral_code text;
BEGIN
  -- Extract all fields with proper null handling
  new_username := NEW.raw_user_meta_data->>'username';
  new_full_name := NEW.raw_user_meta_data->>'full_name';
  new_phone := NEW.raw_user_meta_data->>'phone';
  new_role := COALESCE(NEW.raw_user_meta_data->>'role', 'free');
  new_industry := NULLIF(TRIM(NEW.raw_user_meta_data->>'industry'), '');
  new_company_name := NULLIF(TRIM(NEW.raw_user_meta_data->>'company_name'), '');
  
  -- Validate referral code and get referrer_id
  IF NEW.raw_user_meta_data->>'referral_code' IS NOT NULL THEN
    SELECT id INTO referrer_id
    FROM users
    WHERE referral_code = NEW.raw_user_meta_data->>'referral_code';
    
    IF referrer_id IS NULL THEN
      RAISE EXCEPTION 'Invalid referral code';
    END IF;
  END IF;

  -- Generate unique referral code
  new_referral_code := generate_referral_code();

  -- Validate required fields
  IF new_username IS NULL OR TRIM(new_username) = '' THEN
    RAISE EXCEPTION 'Username is required';
  END IF;

  IF new_full_name IS NULL OR TRIM(new_full_name) = '' THEN
    RAISE EXCEPTION 'Full name is required';
  END IF;

  IF new_phone IS NULL OR TRIM(new_phone) = '' THEN
    RAISE EXCEPTION 'Phone number is required';
  END IF;

  -- Validate role
  IF new_role NOT IN ('free', 'merchant', 'president') THEN
    new_role := 'free';
  END IF;

  -- Validate business fields for merchant and president roles
  IF new_role IN ('merchant', 'president') THEN
    IF new_industry IS NULL THEN
      RAISE EXCEPTION 'Industry is required for % role', new_role;
    END IF;
    
    IF new_company_name IS NULL THEN
      RAISE EXCEPTION 'Company name is required for % role', new_role;
    END IF;
  END IF;

  -- Insert new user with validated fields
  INSERT INTO public.users (
    id,
    username,
    full_name,
    email,
    phone,
    role,
    referrer_id,
    industry,
    company_name,
    referral_code,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    new_username,
    new_full_name,
    NEW.email,
    new_phone,
    new_role,
    referrer_id,
    new_industry,
    new_company_name,
    new_referral_code,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  );

  RETURN NEW;
EXCEPTION WHEN OTHERS THEN
  -- Log the error details
  RAISE LOG 'Error in handle_new_user: %', SQLERRM;
  -- Re-raise the error with a user-friendly message
  RAISE EXCEPTION 'Failed to create user profile: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add policy to allow referral code lookups
CREATE POLICY "Allow referral code lookups"
  ON users
  FOR SELECT
  TO public
  USING (true);

-- Generate referral codes for existing users
DO $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN SELECT id FROM users WHERE referral_code IS NULL LOOP
    UPDATE users
    SET referral_code = generate_referral_code()
    WHERE id = user_record.id;
  END LOOP;
END $$;