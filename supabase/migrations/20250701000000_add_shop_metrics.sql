/*
  # Add Shop Metrics and Ratings

  1. Changes
    - Add product_count, like_count, rating, rating_count columns to shops table
    - Create shop_ratings table for user ratings
    - <PERSON><PERSON> triggers to automatically update these counts
    
  2. Security
    - Add RLS policies for shop_ratings table
    - Use SECURITY DEFINER for trigger functions to ensure they can update counts
*/

-- Add metrics columns to shops table
ALTER TABLE shops
ADD COLUMN IF NOT EXISTS product_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS like_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS rating numeric DEFAULT 0,
ADD COLUMN IF NOT EXISTS rating_count integer DEFAULT 0;

-- Create shop_ratings table
CREATE TABLE IF NOT EXISTS shop_ratings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  shop_id uuid NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
  rating integer NOT NULL CHECK (rating >= 1 AND rating <= 5),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, shop_id)
);

-- Enable Row Level Security
ALTER TABLE shop_ratings ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS shop_ratings_user_id_idx ON shop_ratings(user_id);
CREATE INDEX IF NOT EXISTS shop_ratings_shop_id_idx ON shop_ratings(shop_id);

-- Create RLS Policies for shop_ratings

-- Users can view all shop ratings
CREATE POLICY "Anyone can view shop ratings"
  ON shop_ratings
  FOR SELECT
  TO public
  USING (true);

-- Users can manage their own ratings
CREATE POLICY "Users can manage their own ratings"
  ON shop_ratings
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Add updated_at trigger
CREATE TRIGGER shop_ratings_updated_at
  BEFORE UPDATE ON shop_ratings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Create user_liked_shops table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_liked_shops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  shop_id uuid NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, shop_id)
);

-- Enable Row Level Security if not already enabled
ALTER TABLE user_liked_shops ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance if they don't exist
CREATE INDEX IF NOT EXISTS user_liked_shops_user_id_idx ON user_liked_shops(user_id);
CREATE INDEX IF NOT EXISTS user_liked_shops_shop_id_idx ON user_liked_shops(shop_id);

-- Create RLS Policies for user_liked_shops if they don't exist

-- Users can view all liked shops
CREATE POLICY "Anyone can view liked shops"
  ON user_liked_shops
  FOR SELECT
  TO public
  USING (true);

-- Users can manage their own liked shops
CREATE POLICY "Users can manage their own liked shops"
  ON user_liked_shops
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create function to update shop product count
CREATE OR REPLACE FUNCTION update_shop_product_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.status = 'active' THEN
    UPDATE shops
    SET product_count = product_count + 1
    WHERE id = NEW.shop_id;
  ELSIF TG_OP = 'UPDATE' THEN
    -- If status changed from active to trashed
    IF OLD.status = 'active' AND NEW.status = 'trashed' THEN
      UPDATE shops
      SET product_count = product_count - 1
      WHERE id = NEW.shop_id;
    -- If status changed from trashed to active
    ELSIF OLD.status = 'trashed' AND NEW.status = 'active' THEN
      UPDATE shops
      SET product_count = product_count + 1
      WHERE id = NEW.shop_id;
    END IF;
  ELSIF TG_OP = 'DELETE' AND OLD.status = 'active' THEN
    UPDATE shops
    SET product_count = product_count - 1
    WHERE id = OLD.shop_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update shop product count
DROP TRIGGER IF EXISTS update_shop_product_count_trigger ON products;
CREATE TRIGGER update_shop_product_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_shop_product_count();

-- Create function to update shop like count
CREATE OR REPLACE FUNCTION update_shop_like_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE shops
    SET like_count = like_count + 1
    WHERE id = NEW.shop_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE shops
    SET like_count = like_count - 1
    WHERE id = OLD.shop_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update shop like count
DROP TRIGGER IF EXISTS update_shop_like_count_trigger ON user_liked_shops;
CREATE TRIGGER update_shop_like_count_trigger
  AFTER INSERT OR DELETE ON user_liked_shops
  FOR EACH ROW
  EXECUTE FUNCTION update_shop_like_count();

-- Create function to update shop rating
CREATE OR REPLACE FUNCTION update_shop_rating()
RETURNS TRIGGER AS $$
DECLARE
  avg_rating numeric;
  rating_count integer;
BEGIN
  -- Calculate new average rating and count
  SELECT AVG(rating), COUNT(*)
  INTO avg_rating, rating_count
  FROM shop_ratings
  WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id);
  
  -- Update shop with new rating data
  UPDATE shops
  SET 
    rating = COALESCE(avg_rating, 0),
    rating_count = COALESCE(rating_count, 0)
  WHERE id = COALESCE(NEW.shop_id, OLD.shop_id);
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update shop rating
DROP TRIGGER IF EXISTS update_shop_rating_trigger ON shop_ratings;
CREATE TRIGGER update_shop_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON shop_ratings
  FOR EACH ROW
  EXECUTE FUNCTION update_shop_rating();

-- Update initial product counts for all shops
UPDATE shops
SET product_count = (
  SELECT COUNT(*)
  FROM products
  WHERE products.shop_id = shops.id
  AND products.status = 'active'
);

-- Update initial like counts for all shops
UPDATE shops
SET like_count = (
  SELECT COUNT(*)
  FROM user_liked_shops
  WHERE user_liked_shops.shop_id = shops.id
);

-- Update initial ratings for all shops
DO $$
DECLARE
  shop_record RECORD;
  avg_rating numeric;
  count_ratings integer;
BEGIN
  FOR shop_record IN SELECT id FROM shops LOOP
    SELECT AVG(rating), COUNT(*)
    INTO avg_rating, count_ratings
    FROM shop_ratings
    WHERE shop_id = shop_record.id;
    
    UPDATE shops
    SET 
      rating = COALESCE(avg_rating, 0),
      rating_count = COALESCE(count_ratings, 0)
    WHERE id = shop_record.id;
  END LOOP;
END $$;
