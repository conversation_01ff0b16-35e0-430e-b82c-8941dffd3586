/*
  # Create Event Applications System

  1. New Tables
    - `event_applications`
      - Core application data including applicant details
      - Links to events and optional user accounts
      - Tracks application status and QR code
    
  2. Security
    - Enable RLS on all tables
    - Allow public access for guest applications
    - Restrict management to event owners
*/

-- Create event_applications table
CREATE TABLE event_applications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id),
  full_name text NOT NULL,
  email text NOT NULL,
  phone text NOT NULL,
  qr_code text NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'attended')),
  guest_access_token uuid DEFAULT gen_random_uuid(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes
CREATE INDEX event_applications_event_id_idx ON event_applications(event_id);
CREATE INDEX event_applications_user_id_idx ON event_applications(user_id);
CREATE INDEX event_applications_guest_access_token_idx ON event_applications(guest_access_token);

-- Enable RLS
ALTER TABLE event_applications ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- Anyone can create applications
CREATE POLICY "Anyone can create applications"
  ON event_applications
  FOR INSERT
  TO public
  WITH CHECK (true);

-- Authenticated users can view their own applications
CREATE POLICY "Users can view their own applications"
  ON event_applications
  FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_applications.event_id
      AND events.user_id = auth.uid()
    )
  );

-- Event owners can manage applications
CREATE POLICY "Event owners can manage applications"
  ON event_applications
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_applications.event_id
      AND events.user_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER event_applications_updated_at
  BEFORE UPDATE ON event_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Create function to check event quota
CREATE OR REPLACE FUNCTION check_event_quota()
RETURNS trigger AS $$
DECLARE
  event_quota integer;
  current_applications integer;
BEGIN
  -- Get event quota
  SELECT max_participants INTO event_quota
  FROM events
  WHERE id = NEW.event_id;

  -- If no quota set, allow application
  IF event_quota IS NULL THEN
    RETURN NEW;
  END IF;

  -- Count current confirmed applications
  SELECT COUNT(*) INTO current_applications
  FROM event_applications
  WHERE event_id = NEW.event_id
  AND status IN ('confirmed', 'attended');

  -- Check if quota exceeded
  IF current_applications >= event_quota THEN
    RAISE EXCEPTION 'Event quota exceeded';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for quota check
CREATE TRIGGER check_event_quota_trigger
  BEFORE INSERT ON event_applications
  FOR EACH ROW
  EXECUTE FUNCTION check_event_quota();

-- Create function to generate QR code data
CREATE OR REPLACE FUNCTION generate_qr_code_data()
RETURNS trigger AS $$
BEGIN
  -- Generate QR code data in format: app_id|event_id|timestamp
  NEW.qr_code = NEW.id || '|' || NEW.event_id || '|' || extract(epoch from now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for QR code generation
CREATE TRIGGER generate_qr_code_trigger
  BEFORE INSERT ON event_applications
  FOR EACH ROW
  EXECUTE FUNCTION generate_qr_code_data();