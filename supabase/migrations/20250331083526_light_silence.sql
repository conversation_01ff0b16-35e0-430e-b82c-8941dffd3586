/*
  # Add Cloudflare Image Deletion Trigger

  1. Changes
    - Add configuration table for Cloudflare credentials
    - Add function to delete Cloudflare images
    - Create trigger for product_photos deletion
    - Create trigger for products deletion (for cover image)
    
  2. Security
    - Store credentials in secure table
    - Use service role for Cloudflare API calls
    - Handle errors gracefully
*/

-- Enable http extension if not already enabled
CREATE EXTENSION IF NOT EXISTS http;

-- Create configuration table for Cloudflare credentials
CREATE TABLE IF NOT EXISTS cloudflare_config (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id text NOT NULL,
  api_key text NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Add RLS to config table
ALTER TABLE cloudflare_config ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "Only superuser can manage config" ON cloudflare_config;

-- Create policy for superuser access
CREATE POLICY "Only superuser can manage config"
  ON cloudflare_config
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.is_super_admin = true
    )
  );

-- Create table for deletion logs
CREATE TABLE IF NOT EXISTS cloudflare_deletion_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name text NOT NULL,
  record_id uuid NOT NULL,
  image_url text,
  image_id text,
  success boolean NOT NULL,
  response jsonb,
  error text,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Create index for logs
CREATE INDEX IF NOT EXISTS cloudflare_deletion_logs_created_at_idx ON cloudflare_deletion_logs(created_at);

-- Function to get Cloudflare credentials
CREATE OR REPLACE FUNCTION get_cloudflare_credentials()
RETURNS TABLE (account_id text, api_key text) AS $$
BEGIN
  RETURN QUERY
  SELECT cf.account_id, cf.api_key
  FROM cloudflare_config cf
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to delete Cloudflare image
CREATE OR REPLACE FUNCTION delete_cloudflare_image()
RETURNS trigger AS $$
DECLARE
  image_id text;
  cloudflare_url text;
  cf_credentials record;
  response http_response;
BEGIN
  -- Extract image ID from URL
  IF TG_TABLE_NAME = 'product_photos' THEN
    cloudflare_url := OLD.photo_url;
  ELSE -- products table
    cloudflare_url := OLD.cover_image;
  END IF;

  -- Only proceed if URL exists
  IF cloudflare_url IS NOT NULL THEN
    -- Extract image ID from URL using regex
    image_id := (regexp_matches(cloudflare_url, '/([^/]+)/public$'))[1];
    
    IF image_id IS NOT NULL THEN
      -- Get Cloudflare credentials
      SELECT * INTO cf_credentials FROM get_cloudflare_credentials();
      
      IF cf_credentials IS NOT NULL THEN
        -- Make request to Cloudflare API
        SELECT * FROM http((
          'DELETE',
          format(
            'https://api.cloudflare.com/client/v4/accounts/%s/images/v1/%s',
            cf_credentials.account_id,
            image_id
          ),
          ARRAY[http_header('Authorization', format('Bearer %s', cf_credentials.api_key))],
          NULL,
          NULL
        )) INTO response;
        
        -- Log deletion attempt
        INSERT INTO cloudflare_deletion_logs (
          table_name,
          record_id,
          image_url,
          image_id,
          success,
          response,
          created_at
        ) VALUES (
          TG_TABLE_NAME,
          OLD.id,
          cloudflare_url,
          image_id,
          (response.content::jsonb->>'success')::boolean,
          response.content::jsonb,
          now()
        );
      END IF;
    END IF;
  END IF;
  
  RETURN OLD;
EXCEPTION WHEN OTHERS THEN
  -- Log error but don't prevent deletion
  INSERT INTO cloudflare_deletion_logs (
    table_name,
    record_id,
    image_url,
    image_id,
    success,
    error,
    created_at
  ) VALUES (
    TG_TABLE_NAME,
    OLD.id,
    cloudflare_url,
    image_id,
    false,
    SQLERRM,
    now()
  );
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for product_photos
DROP TRIGGER IF EXISTS delete_cloudflare_image_product_photos ON product_photos;
CREATE TRIGGER delete_cloudflare_image_product_photos
  BEFORE DELETE ON product_photos
  FOR EACH ROW
  EXECUTE FUNCTION delete_cloudflare_image();

-- Create trigger for products (cover image)
DROP TRIGGER IF EXISTS delete_cloudflare_image_products ON products;
CREATE TRIGGER delete_cloudflare_image_products
  BEFORE DELETE ON products
  FOR EACH ROW
  EXECUTE FUNCTION delete_cloudflare_image();

-- Delete existing credentials if any
DELETE FROM cloudflare_config;

-- Insert Cloudflare credentials
INSERT INTO cloudflare_config (account_id, api_key)
VALUES (
  '24af04381f468a29f6134fccdf151bd9',
  '5dz5OseCsLlWWQ_rbs-tsd7tPU6ZQES2GwTewTh5'
);