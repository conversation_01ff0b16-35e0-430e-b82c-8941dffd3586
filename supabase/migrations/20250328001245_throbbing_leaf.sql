/*
  # Create Product Photos Table

  1. New Tables
    - `product_photos`
      - `id` (UUID, primary key) - Unique identifier for each photo
      - `product_id` (UUID) - References products(id)
      - `photo_url` (text) - URL of the photo
      - `caption` (text) - Optional caption for the photo
      - `order` (integer) - Order for drag-and-drop sorting

  2. Security
    - Enable RLS on product_photos table
    - Add policies to:
      - Allow shop owners to manage their product photos
      - Allow anyone to view photos of active products
*/

-- Create product_photos table
CREATE TABLE product_photos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  photo_url text NOT NULL,
  caption text,
  "order" integer NOT NULL DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE product_photos ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX product_photos_product_id_idx ON product_photos(product_id);
CREATE INDEX product_photos_order_idx ON product_photos("order");

-- Create RLS Policies

-- Anyone can view photos of active products
CREATE POLICY "Anyone can view photos of active products"
  ON product_photos
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = product_photos.product_id
      AND products.status = 'active'
    )
  );

-- Shop owners can manage their product photos
CREATE POLICY "Shop owners can manage their product photos"
  ON product_photos
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM products
      JOIN shops ON shops.id = products.shop_id
      WHERE products.id = product_photos.product_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER product_photos_updated_at
  BEFORE UPDATE ON product_photos
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();