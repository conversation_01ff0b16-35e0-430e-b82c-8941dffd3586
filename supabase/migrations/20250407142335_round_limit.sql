/*
  # Fix Referral Code Function Accessibility

  1. Changes
    - Drop and recreate generate_referral_code function with proper permissions
    - Add SECURITY DEFINER to ensure proper execution context
    - Grant execute permissions to public
    
  2. Security
    - Maintain existing security measures
    - Ensure proper function visibility
*/

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS generate_referral_code;

-- Recreate the function with improved accessibility
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS text AS $$
DECLARE
  chars text := '123456789ABCDEFGHIJKLMNPQRSTUVWXYZ'; -- Excluding 0 and O
  code text := '';
  i integer;
BEGIN
  -- Generate 6 character code
  FOR i IN 1..6 LOOP
    code := code || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  
  -- Check if code exists and regenerate if needed
  WHILE EXISTS (SELECT 1 FROM users WHERE referral_code = code) LOOP
    code := '';
    FOR i IN 1..6 LOOP
      code := code || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
  END LOOP;
  
  RETURN code;
END;
$$ 
LANGUAGE plpgsql
SECURITY DEFINER
VOLATILE;

-- Grant execute permission to public
GRANT EXECUTE ON FUNCTION generate_referral_code() TO public;

-- Add comment for documentation
COMMENT ON FUNCTION generate_referral_code() IS 'Generates a unique 6-character referral code';

-- Commit any pending transactions
COMMIT;