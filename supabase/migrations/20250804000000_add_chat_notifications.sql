/*
  # Add Chat Notifications System

  1. Changes
    - Create function to send chat notifications via edge function
    - Create trigger on messages table to automatically send notifications
    - Only send notifications to recipients who aren't currently active in the conversation

  2. Security
    - Function runs with SECURITY DEFINER to access edge functions
    - Proper error handling to prevent message insertion failures
*/

-- Create a simple notification queue table
CREATE TABLE IF NOT EXISTS chat_notification_queue (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id uuid NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  conversation_id uuid NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id uuid NOT NULL REFERENCES auth.users(id),
  recipient_id uuid NOT NULL REFERENCES auth.users(id),
  content text NOT NULL,
  message_type text NOT NULL,
  processed boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on notification queue
ALTER TABLE chat_notification_queue ENABLE ROW LEVEL SECURITY;

-- Create policy for notification queue (only system can access)
CREATE POLICY "System only access to notification queue"
  ON chat_notification_queue
  FOR ALL
  TO authenticated
  USING (false);

-- Create function to queue chat notifications
CREATE OR REPLACE FUNCTION queue_chat_notification()
RETURNS TRIGGER AS $$
DECLARE
  conversation_record RECORD;
  recipient_id uuid;
BEGIN
  -- Only queue notifications for new messages (INSERT)
  IF TG_OP = 'INSERT' THEN
    -- Get conversation details
    SELECT * INTO conversation_record
    FROM conversations
    WHERE id = NEW.conversation_id;

    IF NOT FOUND THEN
      RAISE LOG 'Conversation not found for message %', NEW.id;
      RETURN NEW;
    END IF;

    -- Determine recipient (the participant who is not the sender)
    IF conversation_record.participant_1_id = NEW.sender_id THEN
      recipient_id := conversation_record.participant_2_id;
    ELSE
      recipient_id := conversation_record.participant_1_id;
    END IF;

    -- Insert into notification queue
    INSERT INTO chat_notification_queue (
      message_id,
      conversation_id,
      sender_id,
      recipient_id,
      content,
      message_type
    ) VALUES (
      NEW.id,
      NEW.conversation_id,
      NEW.sender_id,
      recipient_id,
      NEW.content,
      NEW.message_type
    );

    RAISE LOG 'Chat notification queued for message %', NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on messages table
DROP TRIGGER IF EXISTS queue_chat_notification_trigger ON messages;
CREATE TRIGGER queue_chat_notification_trigger
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION queue_chat_notification();

-- Create index for better performance
CREATE INDEX IF NOT EXISTS chat_notification_queue_processed_idx ON chat_notification_queue(processed, created_at);
CREATE INDEX IF NOT EXISTS chat_notification_queue_message_id_idx ON chat_notification_queue(message_id);

-- Add comment explaining the setup
COMMENT ON FUNCTION queue_chat_notification() IS
'Automatically queues push notifications when new chat messages are inserted.
Notifications are processed by a separate service or edge function.';

COMMENT ON TRIGGER queue_chat_notification_trigger ON messages IS
'Queues push notifications for new chat messages.';

COMMENT ON TABLE chat_notification_queue IS
'Queue table for chat notifications. Processed by edge functions or background services.';
