/*
  # Complete Schema Setup for 搵料 (Waste to Gold)
  
  This migration sets up all original tables from syner-biz to ensure compatibility
  while allowing us to focus on construction materials marketplace features.
  
  Tables included:
  - User management and profiles
  - Shops and products with categories
  - Branches and organizations (kept for compatibility)
  - Events system (kept for compatibility)
  - Chat and messaging
  - Material requests (construction-specific)
  - Hong Kong districts
  - All supporting tables and relationships
*/

-- =============================================
-- STEP 1: CORE USER MANAGEMENT
-- =============================================

-- Users table (enhanced from auth.users)
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  full_name text,
  username text UNIQUE,
  phone text,
  avatar_url text,
  bio text,
  date_of_birth date,
  gender text CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
  location text,
  website text,
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT true,
  referral_code text UNIQUE,
  referred_by uuid REFERENCES users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User roles and permissions
CREATE TABLE IF NOT EXISTS user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  role text NOT NULL CHECK (role IN ('admin', 'moderator', 'shop_owner', 'branch_president', 'organization_president', 'user')),
  granted_by uuid REFERENCES users(id),
  granted_at timestamptz DEFAULT now(),
  expires_at timestamptz,
  is_active boolean DEFAULT true,
  UNIQUE(user_id, role)
);

-- =============================================
-- STEP 2: HONG KONG DISTRICTS
-- =============================================

-- Districts table for Hong Kong
CREATE TABLE IF NOT EXISTS districts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  name_en text,
  region text NOT NULL CHECK (region IN ('Hong Kong Island', 'Kowloon', 'New Territories')),
  sort_order integer DEFAULT 999,
  created_at timestamptz DEFAULT now()
);

-- =============================================
-- STEP 3: SHOP AND PRODUCT SYSTEM
-- =============================================

-- Shop categories
CREATE TABLE IF NOT EXISTS shop_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL UNIQUE,
  description text,
  icon text,
  color text,
  sort_order integer DEFAULT 999,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Shops
CREATE TABLE IF NOT EXISTS shops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id uuid REFERENCES users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  logo text,
  banner_image text,
  phone text,
  email text,
  website text,
  address text,
  district_id uuid REFERENCES districts(id),
  shop_category_id uuid REFERENCES shop_categories(id),
  business_hours jsonb,
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT true,
  rating numeric(3,2) DEFAULT 0,
  total_reviews integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Product categories
CREATE TABLE IF NOT EXISTS product_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  icon text,
  parent_id uuid REFERENCES product_categories(id),
  sort_order integer DEFAULT 999,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Products
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shop_id uuid REFERENCES shops(id) ON DELETE CASCADE,
  category_id uuid REFERENCES product_categories(id),
  name text NOT NULL,
  description text,
  price numeric(10,2),
  original_price numeric(10,2),
  currency text DEFAULT 'HKD',
  condition text CHECK (condition IN ('new', 'like_new', 'good', 'fair', 'poor')),
  quantity integer DEFAULT 1,
  images text[],
  specifications jsonb,
  tags text[],
  is_featured boolean DEFAULT false,
  is_available boolean DEFAULT true,
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'sold', 'reserved')),
  views_count integer DEFAULT 0,
  likes_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =============================================
-- STEP 4: BRANCH SYSTEM
-- =============================================

-- Branch categories
CREATE TABLE IF NOT EXISTS branch_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  icon text,
  color text,
  sort_order integer DEFAULT 999,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Branches
CREATE TABLE IF NOT EXISTS branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id uuid REFERENCES users(id) ON DELETE CASCADE,
  category_id uuid REFERENCES branch_categories(id),
  name text NOT NULL,
  description text,
  logo text,
  banner_image text,
  district_id uuid REFERENCES districts(id),
  address text,
  phone text,
  email text,
  website text,
  is_featured boolean DEFAULT false,
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT true,
  member_count integer DEFAULT 1,
  max_members integer,
  join_policy text DEFAULT 'open' CHECK (join_policy IN ('open', 'approval', 'invite_only', 'closed')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Branch members
CREATE TABLE IF NOT EXISTS branch_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid REFERENCES branches(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  role text DEFAULT 'member' CHECK (role IN ('president', 'vice_president', 'secretary', 'treasurer', 'member')),
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  joined_at timestamptz DEFAULT now(),
  UNIQUE(branch_id, user_id)
);

-- Branch member applications
CREATE TABLE IF NOT EXISTS branch_member_applications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid REFERENCES branches(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  message text,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  reviewed_by uuid REFERENCES users(id),
  reviewed_at timestamptz,
  created_at timestamptz DEFAULT now(),
  UNIQUE(branch_id, user_id)
);

-- =============================================
-- STEP 5: ORGANIZATION SYSTEM
-- =============================================

-- Organization categories
CREATE TABLE IF NOT EXISTS organization_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  sort_order integer DEFAULT 999,
  is_predefined boolean DEFAULT false,
  is_approved boolean DEFAULT false,
  created_by uuid REFERENCES users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Organizations
CREATE TABLE IF NOT EXISTS organizations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id uuid REFERENCES users(id) ON DELETE CASCADE,
  category_id uuid REFERENCES organization_categories(id),
  name text NOT NULL,
  description text,
  logo text,
  banner_image text,
  website text,
  phone text,
  email text,
  address text,
  district_id uuid REFERENCES districts(id),
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT true,
  member_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Organization members (through branches)
CREATE TABLE IF NOT EXISTS organization_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
  branch_id uuid REFERENCES branches(id) ON DELETE CASCADE,
  status text DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  joined_at timestamptz DEFAULT now(),
  UNIQUE(organization_id, branch_id)
);

-- Organization member applications
CREATE TABLE IF NOT EXISTS organization_member_applications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
  branch_id uuid REFERENCES branches(id) ON DELETE CASCADE,
  message text,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  reviewed_by uuid REFERENCES users(id),
  reviewed_at timestamptz,
  created_at timestamptz DEFAULT now(),
  UNIQUE(organization_id, branch_id)
);

-- =============================================
-- STEP 6: EVENT SYSTEM
-- =============================================

-- Events
CREATE TABLE IF NOT EXISTS events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  organizer_id uuid REFERENCES users(id) ON DELETE CASCADE,
  branch_id uuid REFERENCES branches(id),
  organization_id uuid REFERENCES organizations(id),
  title text NOT NULL,
  description text,
  banner_photo text,
  start_datetime timestamptz NOT NULL,
  end_datetime timestamptz,
  address text,
  district_id uuid REFERENCES districts(id),
  venue_name text,
  max_participants integer,
  registration_deadline timestamptz,
  is_free boolean DEFAULT true,
  price numeric(10,2),
  currency text DEFAULT 'HKD',
  status text DEFAULT 'upcoming' CHECK (status IN ('draft', 'upcoming', 'ongoing', 'completed', 'cancelled')),
  is_featured boolean DEFAULT false,
  tags text[],
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Event registrations
CREATE TABLE IF NOT EXISTS event_registrations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid REFERENCES events(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  status text DEFAULT 'registered' CHECK (status IN ('registered', 'attended', 'no_show', 'cancelled')),
  registration_data jsonb,
  checked_in_at timestamptz,
  created_at timestamptz DEFAULT now(),
  UNIQUE(event_id, user_id)
);

-- Event photos
CREATE TABLE IF NOT EXISTS event_photos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid REFERENCES events(id) ON DELETE CASCADE,
  uploaded_by uuid REFERENCES users(id),
  photo_url text NOT NULL,
  caption text,
  is_featured boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- =============================================
-- STEP 7: CHAT AND MESSAGING SYSTEM
-- =============================================

-- Conversations
CREATE TABLE IF NOT EXISTS conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  type text DEFAULT 'direct' CHECK (type IN ('direct', 'group', 'support')),
  name text,
  description text,
  avatar_url text,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Conversation participants
CREATE TABLE IF NOT EXISTS conversation_participants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid REFERENCES conversations(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  role text DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member')),
  joined_at timestamptz DEFAULT now(),
  left_at timestamptz,
  last_read_at timestamptz DEFAULT now(),
  is_muted boolean DEFAULT false,
  UNIQUE(conversation_id, user_id)
);

-- Messages
CREATE TABLE IF NOT EXISTS messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id uuid REFERENCES users(id) ON DELETE CASCADE,
  content text,
  message_type text DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),
  attachments jsonb,
  reply_to uuid REFERENCES messages(id),
  is_edited boolean DEFAULT false,
  edited_at timestamptz,
  is_deleted boolean DEFAULT false,
  deleted_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- =============================================
-- STEP 8: MATERIAL REQUESTS (CONSTRUCTION-SPECIFIC)
-- =============================================

-- Material requests (吹雞 feature)
CREATE TABLE IF NOT EXISTS material_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  requester_id uuid REFERENCES users(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text NOT NULL,
  category text NOT NULL CHECK (category IN ('steel', 'concrete', 'wood', 'tools', 'urgent')),
  condition_preference text CHECK (condition_preference IN ('new', 'like_new', 'good', 'fair', 'any')),
  budget numeric(10,2),
  currency text DEFAULT 'HKD',
  district_id uuid REFERENCES districts(id),
  urgency text DEFAULT 'medium' CHECK (urgency IN ('low', 'medium', 'high')),
  status text DEFAULT 'active' CHECK (status IN ('active', 'fulfilled', 'cancelled', 'expired')),
  expires_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Material request responses
CREATE TABLE IF NOT EXISTS material_request_responses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id uuid REFERENCES material_requests(id) ON DELETE CASCADE,
  responder_id uuid REFERENCES users(id) ON DELETE CASCADE,
  message text NOT NULL,
  contact_info jsonb,
  is_selected boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  UNIQUE(request_id, responder_id)
);

-- =============================================
-- STEP 9: USER INTERACTIONS AND SOCIAL FEATURES
-- =============================================

-- User likes for products
CREATE TABLE IF NOT EXISTS user_liked_products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  product_id uuid REFERENCES products(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, product_id)
);

-- User likes for shops
CREATE TABLE IF NOT EXISTS user_liked_shops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  shop_id uuid REFERENCES shops(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, shop_id)
);

-- User likes for branches
CREATE TABLE IF NOT EXISTS user_liked_branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  branch_id uuid REFERENCES branches(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, branch_id)
);

-- User likes for organizations
CREATE TABLE IF NOT EXISTS user_liked_organizations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, organization_id)
);

-- User likes for events
CREATE TABLE IF NOT EXISTS user_liked_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  event_id uuid REFERENCES events(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, event_id)
);

-- User follows
CREATE TABLE IF NOT EXISTS user_follows (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  follower_id uuid REFERENCES users(id) ON DELETE CASCADE,
  following_id uuid REFERENCES users(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(follower_id, following_id),
  CHECK (follower_id != following_id)
);

-- =============================================
-- STEP 10: FEEDBACK AND SUPPORT SYSTEM
-- =============================================

-- Feedback
CREATE TABLE IF NOT EXISTS feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('bug', 'feature', 'improvement', 'complaint', 'compliment', 'other')),
  title text NOT NULL,
  description text NOT NULL,
  category text,
  priority text DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  status text DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  attachments text[],
  admin_notes text,
  resolved_by uuid REFERENCES users(id),
  resolved_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- =============================================
-- STEP 11: BONUS AND REFERRAL SYSTEM
-- =============================================

-- Bonus records
CREATE TABLE IF NOT EXISTS bonus_records (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('referral', 'signup', 'event_attendance', 'shop_creation', 'branch_creation', 'manual')),
  amount numeric(10,2) NOT NULL,
  currency text DEFAULT 'HKD',
  description text,
  reference_id uuid,
  reference_type text,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'paid')),
  processed_by uuid REFERENCES users(id),
  processed_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- =============================================
-- STEP 12: INDEXES FOR PERFORMANCE
-- =============================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
CREATE INDEX IF NOT EXISTS idx_users_referred_by ON users(referred_by);

-- Product indexes
CREATE INDEX IF NOT EXISTS idx_products_shop_id ON products(shop_id);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at DESC);

-- Shop indexes
CREATE INDEX IF NOT EXISTS idx_shops_owner_id ON shops(owner_id);
CREATE INDEX IF NOT EXISTS idx_shops_district_id ON shops(district_id);
CREATE INDEX IF NOT EXISTS idx_shops_category_id ON shops(shop_category_id);
CREATE INDEX IF NOT EXISTS idx_shops_is_active ON shops(is_active);

-- Branch indexes
CREATE INDEX IF NOT EXISTS idx_branches_owner_id ON branches(owner_id);
CREATE INDEX IF NOT EXISTS idx_branches_district_id ON branches(district_id);
CREATE INDEX IF NOT EXISTS idx_branches_category_id ON branches(category_id);
CREATE INDEX IF NOT EXISTS idx_branches_is_active ON branches(is_active);

-- Event indexes
CREATE INDEX IF NOT EXISTS idx_events_organizer_id ON events(organizer_id);
CREATE INDEX IF NOT EXISTS idx_events_branch_id ON events(branch_id);
CREATE INDEX IF NOT EXISTS idx_events_organization_id ON events(organization_id);
CREATE INDEX IF NOT EXISTS idx_events_start_datetime ON events(start_datetime);
CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);

-- Message indexes
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);

-- Material request indexes
CREATE INDEX IF NOT EXISTS idx_material_requests_requester_id ON material_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_material_requests_category ON material_requests(category);
CREATE INDEX IF NOT EXISTS idx_material_requests_district_id ON material_requests(district_id);
CREATE INDEX IF NOT EXISTS idx_material_requests_status ON material_requests(status);
CREATE INDEX IF NOT EXISTS idx_material_requests_created_at ON material_requests(created_at DESC);

-- =============================================
-- STEP 13: TRIGGERS FOR UPDATED_AT
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shops_updated_at BEFORE UPDATE ON shops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_material_requests_updated_at BEFORE UPDATE ON material_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feedback_updated_at BEFORE UPDATE ON feedback FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- STEP 14: INITIAL DATA - HONG KONG DISTRICTS
-- =============================================

-- Insert Hong Kong districts
INSERT INTO districts (name, name_en, region, sort_order) VALUES
-- Hong Kong Island
('中西區', 'Central and Western', 'Hong Kong Island', 1),
('灣仔區', 'Wan Chai', 'Hong Kong Island', 2),
('東區', 'Eastern', 'Hong Kong Island', 3),
('南區', 'Southern', 'Hong Kong Island', 4),

-- Kowloon
('油尖旺區', 'Yau Tsim Mong', 'Kowloon', 5),
('深水埗區', 'Sham Shui Po', 'Kowloon', 6),
('九龍城區', 'Kowloon City', 'Kowloon', 7),
('黃大仙區', 'Wong Tai Sin', 'Kowloon', 8),
('觀塘區', 'Kwun Tong', 'Kowloon', 9),

-- New Territories
('荃灣區', 'Tsuen Wan', 'New Territories', 10),
('屯門區', 'Tuen Mun', 'New Territories', 11),
('元朗區', 'Yuen Long', 'New Territories', 12),
('北區', 'North', 'New Territories', 13),
('大埔區', 'Tai Po', 'New Territories', 14),
('沙田區', 'Sha Tin', 'New Territories', 15),
('西貢區', 'Sai Kung', 'New Territories', 16),
('葵青區', 'Kwai Tsing', 'New Territories', 17),
('離島區', 'Islands', 'New Territories', 18)
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- STEP 15: CONSTRUCTION MATERIAL CATEGORIES
-- =============================================

-- Insert construction-focused product categories
INSERT INTO product_categories (name, description, icon, sort_order) VALUES
('鋼材', 'Steel materials including rebar, beams, pipes', 'construct-outline', 1),
('混凝土', 'Concrete, cement, and related materials', 'cube-outline', 2),
('木材', 'Wood, lumber, and timber products', 'leaf-outline', 3),
('工具', 'Construction tools and equipment', 'hammer-outline', 4),
('電器材料', 'Electrical materials and components', 'flash-outline', 5),
('水管材料', 'Plumbing materials and fittings', 'water-outline', 6),
('裝修材料', 'Renovation and finishing materials', 'color-palette-outline', 7),
('安全設備', 'Safety equipment and gear', 'shield-checkmark-outline', 8),
('其他', 'Other construction materials', 'ellipsis-horizontal-outline', 99)
ON CONFLICT (name) DO NOTHING;
