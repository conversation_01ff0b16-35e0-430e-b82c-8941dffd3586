/*
  # Improve User Registration Process

  1. Changes
    - Enhance user creation trigger with better validation
    - Add proper error handling for all fields
    - Improve null handling and data validation
    
  2. Security
    - Maintain existing RLS policies
    - Add better validation for all fields
*/

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- Create improved function to handle user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_role text;
  new_industry text;
  new_company_name text;
  new_referrer_id uuid;
  new_username text;
  new_full_name text;
  new_phone text;
BEGIN
  -- Extract all fields with proper null handling
  new_username := NEW.raw_user_meta_data->>'username';
  new_full_name := NEW.raw_user_meta_data->>'full_name';
  new_phone := NEW.raw_user_meta_data->>'phone';
  new_role := COALESCE(NEW.raw_user_meta_data->>'role', 'free');
  new_industry := NULLIF(TRIM(NEW.raw_user_meta_data->>'industry'), '');
  new_company_name := NULLIF(TRIM(NEW.raw_user_meta_data->>'company_name'), '');
  
  -- Try to convert referrer_id to UUID if present
  BEGIN
    IF NEW.raw_user_meta_data->>'referrer_id' IS NOT NULL AND NEW.raw_user_meta_data->>'referrer_id' != '' THEN
      new_referrer_id := (NEW.raw_user_meta_data->>'referrer_id')::uuid;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    new_referrer_id := NULL;
  END;

  -- Validate required fields
  IF new_username IS NULL OR TRIM(new_username) = '' THEN
    RAISE EXCEPTION 'Username is required';
  END IF;

  IF new_full_name IS NULL OR TRIM(new_full_name) = '' THEN
    RAISE EXCEPTION 'Full name is required';
  END IF;

  IF new_phone IS NULL OR TRIM(new_phone) = '' THEN
    RAISE EXCEPTION 'Phone number is required';
  END IF;

  -- Validate role
  IF new_role NOT IN ('free', 'merchant', 'president') THEN
    new_role := 'free';
  END IF;

  -- Validate business fields for merchant and president roles
  IF new_role IN ('merchant', 'president') THEN
    IF new_industry IS NULL THEN
      RAISE EXCEPTION 'Industry is required for % role', new_role;
    END IF;
    
    IF new_company_name IS NULL THEN
      RAISE EXCEPTION 'Company name is required for % role', new_role;
    END IF;
  END IF;

  -- Validate referrer exists if provided
  IF new_referrer_id IS NOT NULL THEN
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = new_referrer_id) THEN
      RAISE EXCEPTION 'Referrer with ID % does not exist', new_referrer_id;
    END IF;
  END IF;

  -- Insert new user with validated fields
  INSERT INTO public.users (
    id,
    username,
    full_name,
    email,
    phone,
    role,
    referrer_id,
    industry,
    company_name,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    new_username,
    new_full_name,
    NEW.email,
    new_phone,
    new_role,
    new_referrer_id,
    new_industry,
    new_company_name,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
  );

  RETURN NEW;
EXCEPTION WHEN OTHERS THEN
  -- Log the error details
  RAISE LOG 'Error in handle_new_user: %', SQLERRM;
  -- Re-raise the error with a user-friendly message
  RAISE EXCEPTION 'Failed to create user profile: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on auth.users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();