/*
  # Update Products Table Structure

  1. Changes
    - Replace merchant_id with shop_id
    - Update column names and types
    - Add new columns for product management
    - Update constraints and foreign keys
    
  2. Security
    - Maintain RLS policies with updated references
    - Add proper constraints for new columns

  3. Changes to Existing Data
    - Drop existing table and recreate
    - Remove merchant_id relationship
*/

-- Drop existing table and its dependencies
DROP TABLE IF EXISTS products CASCADE;

-- Create updated products table
CREATE TABLE products (
  product_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  shop_id uuid REFERENCES shops(id),
  title text NOT NULL,
  description text,
  price numeric NOT NULL CHECK (price >= 0),
  is_in_stock boolean DEFAULT true,
  profit_sharing_rate numeric NOT NULL CHECK (profit_sharing_rate >= 0 AND profit_sharing_rate <= 100),
  cover_image text,
  status text DEFAULT 'active' CHECK (status IN ('active', 'trashed')),
  created_by text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX products_shop_id_idx ON products(shop_id);
CREATE INDEX products_status_idx ON products(status);

-- Create RLS Policies

-- Anyone can view active products
CREATE POLICY "Anyone can view active products"
  ON products
  FOR SELECT
  TO authenticated
  USING (status = 'active');

-- Shop owners can view all their products
CREATE POLICY "Shop owners can view all their products"
  ON products
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM shops
      WHERE shops.id = products.shop_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Shop owners can manage their products
CREATE POLICY "Shop owners can manage their products"
  ON products
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM shops
      WHERE shops.id = products.shop_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();