/*
  # Fix Product-Based Conversations Implementation

  This migration fixes the constraint issues and implements a robust
  product-based conversation system similar to Carousell.

  1. Drop problematic unique constraints
  2. Create better unique constraint logic
  3. Add proper error handling
  4. Ensure data consistency
*/

-- First, drop the problematic unique indexes if they exist
DROP INDEX IF EXISTS idx_unique_product_conversation;
DROP INDEX IF EXISTS idx_unique_product_conversation_reverse;

-- Create a single, robust unique constraint for product conversations
-- This ensures one conversation per product per participant pair (regardless of order)
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_conversation_normalized
ON conversations(
  LEAST(participant_1_id, participant_2_id),
  GREATEST(participant_1_id, participant_2_id),
  product_id
) 
WHERE product_id IS NOT NULL;

-- For general conversations (no product), ensure one conversation per participant pair
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_general_conversation_normalized
ON conversations(
  LEAST(participant_1_id, participant_2_id),
  GREATEST(participant_1_id, participant_2_id)
) 
WHERE product_id IS NULL AND conversation_type = 'general';

-- Add a function to get or create product conversation safely
CREATE OR REPLACE FUNCTION get_or_create_product_conversation(
  p_user1_id uuid,
  p_user2_id uuid,
  p_product_id uuid DEFAULT NULL
) RETURNS uuid AS $$
DECLARE
  v_conversation_id uuid;
  v_participant_1 uuid;
  v_participant_2 uuid;
  v_conversation_type text;
BEGIN
  -- Normalize participant order (smaller UUID first)
  IF p_user1_id < p_user2_id THEN
    v_participant_1 := p_user1_id;
    v_participant_2 := p_user2_id;
  ELSE
    v_participant_1 := p_user2_id;
    v_participant_2 := p_user1_id;
  END IF;

  -- Set conversation type
  v_conversation_type := CASE WHEN p_product_id IS NOT NULL THEN 'product' ELSE 'general' END;

  -- Try to find existing conversation
  SELECT id INTO v_conversation_id
  FROM conversations
  WHERE participant_1_id = v_participant_1
    AND participant_2_id = v_participant_2
    AND (
      (p_product_id IS NULL AND product_id IS NULL AND conversation_type = 'general') OR
      (p_product_id IS NOT NULL AND product_id = p_product_id AND conversation_type = 'product')
    );

  -- If not found, create new conversation
  IF v_conversation_id IS NULL THEN
    INSERT INTO conversations (
      participant_1_id,
      participant_2_id,
      product_id,
      conversation_type,
      last_message_at
    ) VALUES (
      v_participant_1,
      v_participant_2,
      p_product_id,
      v_conversation_type,
      NOW()
    ) RETURNING id INTO v_conversation_id;
  END IF;

  RETURN v_conversation_id;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON FUNCTION get_or_create_product_conversation IS 'Safely creates or retrieves a conversation between two users, optionally for a specific product';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversations_participants_normalized ON conversations(
  LEAST(participant_1_id, participant_2_id),
  GREATEST(participant_1_id, participant_2_id)
);

CREATE INDEX IF NOT EXISTS idx_conversations_product_type ON conversations(product_id, conversation_type) WHERE product_id IS NOT NULL;
