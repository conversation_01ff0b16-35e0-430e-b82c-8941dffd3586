/*
  # Add End Date to Events Table

  1. Changes
    - Add end_date column to events table
    - Allow NULL for backward compatibility
    
  2. Security
    - No security changes needed
*/

-- Add end_date column to events table
ALTER TABLE events
ADD COLUMN IF NOT EXISTS end_date date;

-- Update triggers
DROP TRIGGER IF EXISTS events_updated_at ON events;
CREATE TRIGGER events_updated_at
  BEFORE UPDATE ON events
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();
