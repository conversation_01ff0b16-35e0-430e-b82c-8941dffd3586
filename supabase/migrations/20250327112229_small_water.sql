/*
  # Create Shops Table

  1. New Tables
    - `shops`
      - `shop_id` (UUID, primary key) - Unique identifier for the shop
      - `id` (serial, unique) - Sequential ID for display purposes
      - `name` (text) - Shop name
      - `logo` (text) - Logo image URL
      - `banner` (text) - Banner image URL
      - `description` (text) - Shop description
      - `is_featured` (boolean) - Whether shop is featured
      - `notification_emails` (text[]) - Array of notification email addresses
      - `notification_group` (text) - Notification group identifier
      - `owner_id` (UUID) - References auth.users(id)
      - `created_at` (timestamptz) - Creation timestamp
      - `updated_at` (timestamptz) - Last update timestamp

  2. Security
    - Enable RLS on shops table
    - Add policies for:
      - Public read access to all shops
      - Owner-only write access
      - Admin access for featuring shops
*/

-- Create Shops Table
CREATE TABLE IF NOT EXISTS shops (
  shop_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  id serial UNIQUE,
  name text NOT NULL,
  logo text,
  banner text,
  description text,
  is_featured boolean DEFAULT false,
  notification_emails text[],
  notification_group text,
  owner_id uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT one_shop_per_user UNIQUE (owner_id)
);

-- Enable Row Level Security
ALTER TABLE shops ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS shops_owner_id_idx ON shops(owner_id);
CREATE INDEX IF NOT EXISTS shops_is_featured_idx ON shops(is_featured);

-- Create RLS Policies

-- Anyone can view shops
CREATE POLICY "Anyone can view shops"
  ON shops
  FOR SELECT
  TO authenticated
  USING (true);

-- Users can create their own shop
CREATE POLICY "Users can create their own shop"
  ON shops
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = owner_id AND
    NOT EXISTS (
      SELECT 1 FROM shops
      WHERE owner_id = auth.uid()
    )
  );

-- Users can update their own shop
CREATE POLICY "Users can update their own shop"
  ON shops
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = owner_id);

-- Users can delete their own shop
CREATE POLICY "Users can delete their own shop"
  ON shops
  FOR DELETE
  TO authenticated
  USING (auth.uid() = owner_id);

-- Add updated_at trigger
CREATE TRIGGER shops_updated_at
  BEFORE UPDATE ON shops
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();