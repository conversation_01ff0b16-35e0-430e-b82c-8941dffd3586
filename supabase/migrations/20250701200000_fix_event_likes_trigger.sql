/*
  # Fix Event Likes Trigger

  1. Changes
    - Drop and recreate the trigger function with SECURITY DEFINER
    - Recreate the triggers
    - Update existing like counts to ensure they're accurate
    
  2. Security
    - No changes to RLS policies
*/

-- Drop existing triggers
DROP TRIGGER IF EXISTS update_event_like_count_insert ON user_liked_events;
DROP TRIGGER IF EXISTS update_event_like_count_delete ON user_liked_events;

-- Drop existing function
DROP FUNCTION IF EXISTS update_event_like_count();

-- <PERSON><PERSON> improved trigger function with SECURITY DEFINER
CREATE OR REPLACE FUNCTION update_event_like_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Increment like_count
    UPDATE events
    SET like_count = COALESCE(like_count, 0) + 1
    WHERE id = NEW.event_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement like_count
    UPDATE events
    SET like_count = GREATEST(0, COALESCE(like_count, 0) - 1)
    WHERE id = OLD.event_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for like count updates
CREATE TRIGGER update_event_like_count_insert
  AFTER INSERT ON user_liked_events
  FOR EACH ROW
  EXECUTE FUNCTION update_event_like_count();

CREATE TRIGGER update_event_like_count_delete
  AFTER DELETE ON user_liked_events
  FOR EACH ROW
  EXECUTE FUNCTION update_event_like_count();

-- Update all event like counts to ensure they're accurate
UPDATE events e
SET like_count = (
  SELECT COUNT(*)
  FROM user_liked_events ule
  WHERE ule.event_id = e.id
);
