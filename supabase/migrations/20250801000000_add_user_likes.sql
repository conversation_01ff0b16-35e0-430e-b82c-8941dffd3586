/*
  # Add User Likes Functionality

  1. Changes
    - Create user_liked_users table for tracking user bookmarks
    - Add RLS policies for security
    
  2. Security
    - Enable RLS on new table
    - Set up policies similar to other user_liked_* tables
*/

-- Create user_liked_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_liked_users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  liked_user_id uuid NOT NULL REFERENCES auth.users(id),
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, liked_user_id)
);

-- Enable Row Level Security
ALTER TABLE user_liked_users ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS user_liked_users_user_id_idx ON user_liked_users(user_id);
CREATE INDEX IF NOT EXISTS user_liked_users_liked_user_id_idx ON user_liked_users(liked_user_id);

-- Create RLS Policies for user_liked_users

-- Users can view all liked users
CREATE POLICY "Anyone can view liked users"
  ON user_liked_users
  FOR SELECT
  TO public
  USING (true);

-- Users can manage their own liked users
CREATE POLICY "Users can manage their own liked users"
  ON user_liked_users
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Add updated_at trigger
CREATE TRIGGER user_liked_users_updated_at
  BEFORE UPDATE ON user_liked_users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();
