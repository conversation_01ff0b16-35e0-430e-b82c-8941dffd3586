/*
  # Add User Interaction Logs

  1. Changes
    - Create user_interaction_logs table for tracking all user interactions
    - Add RLS policies for security
    - Create triggers to automatically log interactions from user_liked_users
    
  2. Security
    - Enable RLS on new table
    - Set up policies for viewing and managing logs
*/

-- Create user_interaction_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_interaction_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  target_user_id uuid NOT NULL REFERENCES auth.users(id),
  action_type text NOT NULL,
  notes text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT valid_action_type CHECK (action_type IN ('like', 'unlike', 'message', 'view'))
);

-- Enable Row Level Security
ALTER TABLE user_interaction_logs ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS user_interaction_logs_user_id_idx ON user_interaction_logs(user_id);
CREATE INDEX IF NOT EXISTS user_interaction_logs_target_user_id_idx ON user_interaction_logs(target_user_id);
CREATE INDEX IF NOT EXISTS user_interaction_logs_action_type_idx ON user_interaction_logs(action_type);
CREATE INDEX IF NOT EXISTS user_interaction_logs_created_at_idx ON user_interaction_logs(created_at);

-- Create RLS Policies for user_interaction_logs

-- Users can view logs where they are either the user or the target
CREATE POLICY "Users can view their own interaction logs"
  ON user_interaction_logs
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR target_user_id = auth.uid());

-- Users can insert their own logs
CREATE POLICY "Users can insert their own logs"
  ON user_interaction_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Create trigger function to log user likes
CREATE OR REPLACE FUNCTION log_user_like()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Log the like action
    INSERT INTO user_interaction_logs (
      user_id,
      target_user_id,
      action_type,
      notes,
      metadata
    ) VALUES (
      NEW.user_id,
      NEW.liked_user_id,
      'like',
      NEW.notes,
      jsonb_build_object('like_id', NEW.id)
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Only log if notes were changed
    IF OLD.notes IS DISTINCT FROM NEW.notes THEN
      INSERT INTO user_interaction_logs (
        user_id,
        target_user_id,
        action_type,
        notes,
        metadata
      ) VALUES (
        NEW.user_id,
        NEW.liked_user_id,
        'like',
        NEW.notes,
        jsonb_build_object('like_id', NEW.id, 'previous_notes', OLD.notes)
      );
    END IF;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Log the unlike action
    INSERT INTO user_interaction_logs (
      user_id,
      target_user_id,
      action_type,
      notes,
      metadata
    ) VALUES (
      OLD.user_id,
      OLD.liked_user_id,
      'unlike',
      NULL,
      jsonb_build_object('previous_notes', OLD.notes)
    );
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for user_liked_users
DROP TRIGGER IF EXISTS log_user_like_insert ON user_liked_users;
CREATE TRIGGER log_user_like_insert
  AFTER INSERT ON user_liked_users
  FOR EACH ROW
  EXECUTE FUNCTION log_user_like();

DROP TRIGGER IF EXISTS log_user_like_update ON user_liked_users;
CREATE TRIGGER log_user_like_update
  AFTER UPDATE ON user_liked_users
  FOR EACH ROW
  EXECUTE FUNCTION log_user_like();

DROP TRIGGER IF EXISTS log_user_like_delete ON user_liked_users;
CREATE TRIGGER log_user_like_delete
  AFTER DELETE ON user_liked_users
  FOR EACH ROW
  EXECUTE FUNCTION log_user_like();
