/*
  # Add Product Display Order

  1. Changes
    - Add display_order column to products table
    - Add index for efficient ordering
    - Update existing products with default order
    
  2. Security
    - No security changes needed
*/

-- Add display_order column
ALTER TABLE products
ADD COLUMN display_order integer DEFAULT 0;

-- Create index for efficient ordering
CREATE INDEX products_display_order_idx ON products(shop_id, display_order);

-- Update existing products with sequential order
DO $$
DECLARE
  shop record;
  product record;
  order_counter integer;
BEGIN
  FOR shop IN SELECT DISTINCT shop_id FROM products LOOP
    order_counter := 0;
    FOR product IN 
      SELECT id 
      FROM products 
      WHERE shop_id = shop.shop_id 
      ORDER BY created_at
    LOOP
      UPDATE products 
      SET display_order = order_counter 
      WHERE id = product.id;
      order_counter := order_counter + 1;
    END LOOP;
  END LOOP;
END $$;