/*
  # Update Events RLS Policies

  1. Changes
    - Drop existing RLS policies
    - Add new policies for:
      - Viewing events (all authenticated users)
      - Managing events (merchants and presidents)
      - Allow null user_id for demo events
    
  2. Security
    - Maintain data access security
    - Allow viewing of demo events
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view events" ON events;
DROP POLICY IF EXISTS "Users can manage their own events" ON events;

-- Create new policies
CREATE POLICY "Anyone can view events"
  ON events
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can manage their own events"
  ON events
  FOR INSERT
  TO authenticated
  WITH CHECK (
    (
      -- Allow users with appropriate roles to create events
      EXISTS (
        SELECT 1 FROM users
        WHERE id = auth.uid()
        AND role IN ('president', 'merchant')
      )
      AND
      user_id = auth.uid()
    )
    OR
    -- Allow system-generated events (demo events)
    user_id IS NULL
  );

CREATE POLICY "Users can update their own events"
  ON events
  FOR UPDATE
  TO authenticated
  USING (
    user_id = auth.uid()
    AND
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('president', 'merchant')
    )
  );

CREATE POLICY "Users can delete their own events"
  ON events
  FOR DELETE
  TO authenticated
  USING (
    user_id = auth.uid()
    AND
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('president', 'merchant')
    )
  );