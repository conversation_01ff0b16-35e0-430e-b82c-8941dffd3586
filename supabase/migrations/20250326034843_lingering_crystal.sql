/*
  # Fix User Number Sequence

  1. Changes
    - Ensure user_number_seq exists
    - Re-create sequence if missing
    - Update user number trigger to handle errors gracefully
    
  2. Security
    - Maintain existing RLS policies
    - No security changes needed
*/

-- Recreate sequence if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_sequences WHERE schemaname = 'public' AND sequencename = 'user_number_seq') THEN
    CREATE SEQUENCE IF NOT EXISTS user_number_seq;
  END IF;
END $$;

-- Update the assign_user_number function to be more resilient
CREATE OR REPLACE FUNCTION assign_user_number()
RETURNS TRIGGER AS $$
BEGIN
  -- Create sequence if it doesn't exist (extra safety)
  IF NOT EXISTS (SELECT 1 FROM pg_sequences WHERE schemaname = 'public' AND sequencename = 'user_number_seq') THEN
    CREATE SEQUENCE user_number_seq;
  END IF;

  -- Get next value from sequence
  BEGIN
    NEW.user_number := nextval('user_number_seq');
  EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error assigning user number: %', SQLERRM;
    -- Don't fail the entire transaction if sequence fails
    NEW.user_number := NULL;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;