/*
  # Fix Time Zone and Restore Original Columns

  1. Changes
    - Adjust datetime values to GMT+8 (add 8 hours)
    - Restore original date, start_time, end_time columns
    - Update triggers to maintain backward compatibility
    
  2. Security
    - No security changes needed
*/

-- Restore original columns
ALTER TABLE events
ADD COLUMN date date,
ADD COLUMN start_time time without time zone,
ADD COLUMN end_time time without time zone;

-- Update original columns with data from datetime columns
UPDATE events
SET 
  date = (start_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::date,
  start_time = (start_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::time,
  end_time = (end_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::time;

-- Create a trigger function to keep columns in sync
CREATE OR REPLACE FUNCTION sync_event_datetime_columns()
RETURNS TRIGGER AS $$
BEGIN
  -- If datetime columns are updated, update date and time columns
  IF TG_OP = 'UPDATE' AND (
     NEW.start_datetime IS DISTINCT FROM OLD.start_datetime OR 
     NEW.end_datetime IS DISTINCT FROM OLD.end_datetime
  ) THEN
    NEW.date = (NEW.start_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::date;
    NEW.start_time = (NEW.start_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::time;
    NEW.end_time = (NEW.end_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::time;
  
  -- If date or time columns are updated, update datetime columns
  ELSIF TG_OP = 'UPDATE' AND (
     NEW.date IS DISTINCT FROM OLD.date OR 
     NEW.start_time IS DISTINCT FROM OLD.start_time OR
     NEW.end_time IS DISTINCT FROM OLD.end_time
  ) THEN
    -- Construct datetime strings with Hong Kong timezone
    NEW.start_datetime = (NEW.date || ' ' || NEW.start_time)::timestamptz AT TIME ZONE 'Asia/Hong_Kong';
    NEW.end_datetime = (NEW.date || ' ' || NEW.end_time)::timestamptz AT TIME ZONE 'Asia/Hong_Kong';
  
  -- For new inserts, ensure all fields are populated
  ELSIF TG_OP = 'INSERT' THEN
    -- If datetime fields are provided but date/time fields are not
    IF NEW.start_datetime IS NOT NULL AND (NEW.date IS NULL OR NEW.start_time IS NULL) THEN
      NEW.date = (NEW.start_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::date;
      NEW.start_time = (NEW.start_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::time;
      NEW.end_time = (NEW.end_datetime AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Hong_Kong')::time;
    
    -- If date/time fields are provided but datetime fields are not
    ELSIF NEW.date IS NOT NULL AND NEW.start_time IS NOT NULL AND (NEW.start_datetime IS NULL) THEN
      NEW.start_datetime = (NEW.date || ' ' || NEW.start_time)::timestamptz AT TIME ZONE 'Asia/Hong_Kong';
      NEW.end_datetime = (NEW.date || ' ' || NEW.end_time)::timestamptz AT TIME ZONE 'Asia/Hong_Kong';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to keep columns in sync
DROP TRIGGER IF EXISTS sync_event_datetime_trigger ON events;
CREATE TRIGGER sync_event_datetime_trigger
BEFORE INSERT OR UPDATE ON events
FOR EACH ROW
EXECUTE FUNCTION sync_event_datetime_columns();

-- Update triggers
DROP TRIGGER IF EXISTS events_updated_at ON events;
CREATE TRIGGER events_updated_at
  BEFORE UPDATE ON events
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();
