/*
  # Add Branch Categories and Sources System

  1. Create new tables for dynamic categories and sources
  2. Add source column to branches table
  3. Update existing categories with new predefined list
  4. Insert predefined sources

  This allows users to:
  - Select from predefined categories/sources
  - Add custom categories/sources for admin review
  - Filter branches by category and source
*/

-- Create Branch Sources Table
CREATE TABLE IF NOT EXISTS branch_sources (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  sort_order integer DEFAULT 999,
  is_predefined boolean DEFAULT false,
  is_approved boolean DEFAULT false,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE branch_sources ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for branch_sources
CREATE POLICY "Anyone can view approved branch sources"
  ON branch_sources
  FOR SELECT
  TO authenticated
  USING (is_approved = true);

CREATE POLICY "Users can create new branch sources"
  ON branch_sources
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

-- Add updated_at trigger
CREATE TRIGGER branch_sources_updated_at
  BEFORE UPDATE ON branch_sources
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Add source_id column to branches table
ALTER TABLE branches ADD COLUMN IF NOT EXISTS source_id uuid REFERENCES branch_sources(id);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS branches_source_id_idx ON branches(source_id);

-- Update branch_categories to support custom categories
ALTER TABLE branch_categories ADD COLUMN IF NOT EXISTS sort_order integer DEFAULT 999;
ALTER TABLE branch_categories ADD COLUMN IF NOT EXISTS is_predefined boolean DEFAULT false;
ALTER TABLE branch_categories ADD COLUMN IF NOT EXISTS is_approved boolean DEFAULT false;
ALTER TABLE branch_categories ADD COLUMN IF NOT EXISTS created_by uuid REFERENCES auth.users(id);

-- Create RLS Policy for custom categories
DROP POLICY IF EXISTS "Anyone can view branch categories" ON branch_categories;

CREATE POLICY "Anyone can view approved branch categories"
  ON branch_categories
  FOR SELECT
  TO authenticated
  USING (is_approved = true);

CREATE POLICY "Users can create new branch categories"
  ON branch_categories
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

-- Clear existing categories and insert new predefined ones
DELETE FROM branch_categories;

-- Insert predefined branch categories with proper ordering
INSERT INTO branch_categories (title, description, sort_order, is_predefined, is_approved)
VALUES
  ('商務類', '商務相關的分會', 1, true, true),
  ('社交類', '社交相關的分會', 2, true, true),
  ('興趣類', '興趣相關的分會', 3, true, true),
  ('專業/產業類', '專業或產業相關的分會', 4, true, true),
  ('銷售類', '銷售相關的分會', 5, true, true),
  ('其他', '其他類型的分會', 999, true, true);

-- Insert predefined branch sources with proper ordering
INSERT INTO branch_sources (name, sort_order, is_predefined, is_approved)
VALUES
  ('BNI', 1, true, true),
  ('提多商業聯盟', 2, true, true),
  ('商龍會', 3, true, true),
  ('香港中小企協會', 4, true, true),
  ('工聯會', 5, true, true),
  ('其他', 999, true, true);
