/*
  # Add Product-Based Conversations

  This migration adds product-based conversation support to enable better tracking
  of product-specific discussions, similar to Carousell's approach.

  1. Changes to conversations table:
     - Add product_id column to link conversations to specific products
     - Add conversation_type to distinguish between product and general chats
     - Update indexes for better performance

  2. Benefits:
     - Each product gets its own conversation thread
     - Better tracking of product selling lifecycle
     - Cleaner conversation organization
     - Product context preserved in chat history

  3. Migration strategy:
     - Add new columns with defaults
     - Update existing conversations to be 'general' type
     - Create new indexes
     - Update RLS policies if needed
*/

-- Add product_id and conversation_type to conversations table
ALTER TABLE conversations 
ADD COLUMN IF NOT EXISTS product_id uuid REFERENCES products(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS conversation_type text DEFAULT 'general' CHECK (conversation_type IN ('general', 'product'));

-- Update existing conversations to be 'general' type
UPDATE conversations 
SET conversation_type = 'general' 
WHERE conversation_type IS NULL;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_conversations_product_id ON conversations(product_id);
CREATE INDEX IF NOT EXISTS idx_conversations_type ON conversations(conversation_type);
CREATE INDEX IF NOT EXISTS idx_conversations_participants_product ON conversations(participant_1_id, participant_2_id, product_id);

-- Create a unique constraint to ensure one conversation per product per participant pair
-- This prevents duplicate product conversations between the same users
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_conversation 
ON conversations(participant_1_id, participant_2_id, product_id) 
WHERE product_id IS NOT NULL AND participant_1_id < participant_2_id;

CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_product_conversation_reverse 
ON conversations(participant_2_id, participant_1_id, product_id) 
WHERE product_id IS NOT NULL AND participant_2_id < participant_1_id;

-- Add a comment to document the new structure
COMMENT ON COLUMN conversations.product_id IS 'Links conversation to a specific product for product-based chats';
COMMENT ON COLUMN conversations.conversation_type IS 'Type of conversation: general (user-to-user) or product (product-specific)';

-- Update the conversations table comment
COMMENT ON TABLE conversations IS 'Stores conversations between users, supporting both general chats and product-specific discussions';
