/*
  # Update Branch Members Table

  1. Changes
    - Modify branch_members table to include status field with values 'active', 'pending', 'rejected'
    - Update RLS policies to allow proper access to branch members with different statuses
    - Remove the trigger that automatically adds members when applications are approved
    - Keep branch_member_applications as a read-only log
    
  2. Security
    - Maintain existing RLS policies
    - Update constraints and indexes
*/

-- Drop the existing trigger that automatically adds members when applications are approved
DROP TRIGGER IF EXISTS handle_approved_application_trigger ON branch_member_applications;
DROP FUNCTION IF EXISTS handle_approved_application();

-- Update branch_members table to include status field
ALTER TABLE branch_members DROP CONSTRAINT IF EXISTS branch_members_status_check;
ALTER TABLE branch_members ALTER COLUMN status TYPE text;
ALTER TABLE branch_members ADD CONSTRAINT branch_members_status_check 
  CHECK (status IN ('active', 'pending', 'rejected', 'inactive'));

-- Create index for status field
CREATE INDEX IF NOT EXISTS branch_members_status_idx ON branch_members(status);

-- Update RLS policies for branch_members

-- Drop existing policies
DROP POLICY IF EXISTS "Branch owners can view their branch members" ON branch_members;
DROP POLICY IF EXISTS "Users can view branches they are members of" ON branch_members;
DROP POLICY IF EXISTS "Branch owners can manage their branch members" ON branch_members;

-- Create updated policies

-- Branch owners can view all members of their branch (including pending)
CREATE POLICY "Branch owners can view their branch members"
  ON branch_members
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_members.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Users can view branches they are active members of
CREATE POLICY "Users can view branches they are active members of"
  ON branch_members
  FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid() AND
    (status = 'active' OR status = 'pending')
  );

-- Branch owners can manage their branch members
CREATE POLICY "Branch owners can manage their branch members"
  ON branch_members
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_members.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Users can apply to join branches
CREATE POLICY "Users can apply to join branches"
  ON branch_members
  FOR INSERT
  TO authenticated
  WITH CHECK (
    user_id = auth.uid() AND
    NOT EXISTS (
      SELECT 1 FROM branch_members
      WHERE branch_members.branch_id = branch_members.branch_id
      AND branch_members.user_id = auth.uid()
      AND (branch_members.status = 'active' OR branch_members.status = 'pending')
    )
  );

-- Update branch_member_applications table policies to make it read-only

-- Drop existing policies
DROP POLICY IF EXISTS "Users can apply to join branches" ON branch_member_applications;

-- Create updated policies
CREATE POLICY "Users can only view their applications"
  ON branch_member_applications
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Create a function to update branch member count based on active members only
CREATE OR REPLACE FUNCTION update_branch_member_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.status = 'active' THEN
    UPDATE branches
    SET member_count = member_count + 1
    WHERE id = NEW.branch_id;
  ELSIF TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active' THEN
    UPDATE branches
    SET member_count = member_count + 1
    WHERE id = NEW.branch_id;
  ELSIF TG_OP = 'UPDATE' AND OLD.status = 'active' AND NEW.status != 'active' THEN
    UPDATE branches
    SET member_count = member_count - 1
    WHERE id = NEW.branch_id;
  ELSIF TG_OP = 'DELETE' AND OLD.status = 'active' THEN
    UPDATE branches
    SET member_count = member_count - 1
    WHERE id = OLD.branch_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger
DROP TRIGGER IF EXISTS update_branch_member_count_trigger ON branch_members;

-- Create updated trigger
CREATE TRIGGER update_branch_member_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON branch_members
  FOR EACH ROW
  EXECUTE FUNCTION update_branch_member_count();
