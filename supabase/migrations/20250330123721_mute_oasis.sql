/*
  # Update RLS Policies for Public Access

  1. Changes
    - Allow public access to products, shops and events
    - Remove authenticated requirement for read operations
    - Keep write operations restricted to authenticated users
    
  2. Security
    - Maintain existing write restrictions
    - Only allow reading active products
*/

-- Update Products Policies
DROP POLICY IF EXISTS "Anyone can view active products" ON products;
DROP POLICY IF EXISTS "Shop owners can view all their products" ON products;

CREATE POLICY "Anyone can view active products"
  ON products
  FOR SELECT
  TO public
  USING (status = 'active');

CREATE POLICY "Shop owners can view all their products"
  ON products
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM shops
      WHERE shops.id = products.shop_id
      AND shops.owner_id = auth.uid()
    )
  );

-- Update Shops Policies
DROP POLICY IF EXISTS "Anyone can view shops" ON shops;

CREATE POLICY "Anyone can view shops"
  ON shops
  FOR SELECT
  TO public
  USING (true);

-- Update Events Policies
DROP POLICY IF EXISTS "Anyone can view events" ON events;

CREATE POLICY "Anyone can view events"
  ON events
  FOR SELECT
  TO public
  USING (true);

-- Update Product Photos Policies
DROP POLICY IF EXISTS "Anyone can view photos of active products" ON product_photos;

CREATE POLICY "Anyone can view photos of active products"
  ON product_photos
  FOR SELECT
  TO public
  USING (
    EXISTS (
      SELECT 1 FROM products
      WHERE products.id = product_photos.product_id
      AND products.status = 'active'
    )
  );