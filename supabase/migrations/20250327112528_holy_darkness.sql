/*
  # Update Shops Table Structure

  1. Changes
    - Remove dual ID columns (shop_id and id)
    - Use single UUID column named 'id' as primary key
    - Maintain all other columns and constraints
    
  2. Security
    - Maintain existing RLS policies
    - Update constraints and indexes
*/

-- Drop existing table
DROP TABLE IF EXISTS shops;

-- Create updated shops table
CREATE TABLE IF NOT EXISTS shops (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  logo text,
  banner text,
  description text,
  is_featured boolean DEFAULT false,
  notification_emails text[],
  notification_group text,
  owner_id uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT one_shop_per_user UNIQUE (owner_id)
);

-- Enable Row Level Security
ALTER TABLE shops ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS shops_owner_id_idx ON shops(owner_id);
CREATE INDEX IF NOT EXISTS shops_is_featured_idx ON shops(is_featured);

-- Create RLS Policies

-- Anyone can view shops
CREATE POLICY "Anyone can view shops"
  ON shops
  FOR SELECT
  TO authenticated
  USING (true);

-- Users can create their own shop
CREATE POLICY "Users can create their own shop"
  ON shops
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = owner_id AND
    NOT EXISTS (
      SELECT 1 FROM shops
      WHERE owner_id = auth.uid()
    )
  );

-- Users can update their own shop
CREATE POLICY "Users can update their own shop"
  ON shops
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = owner_id);

-- Users can delete their own shop
CREATE POLICY "Users can delete their own shop"
  ON shops
  FOR DELETE
  TO authenticated
  USING (auth.uid() = owner_id);

-- Add updated_at trigger
CREATE TRIGGER shops_updated_at
  BEFORE UPDATE ON shops
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();