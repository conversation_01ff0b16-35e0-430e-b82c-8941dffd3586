/*
  # Add Branch-related Tables

  1. New Tables
    - `branches` - Main table for 分會 information
    - `branch_categories` - Categories for branches
    - `branch_members` - Junction table for branch membership
    - `branch_member_applications` - Table for membership applications
    - `user_liked_branches` - For users to like/favorite branches
    - `branch_activities` - For storing branch activities (recent and historical)
    
  2. Security
    - Enable RLS on all tables
    - Add policies for:
      - Public read access to branches
      - Owner-only write access to branches
      - Membership management
*/

-- Create update_updated_at function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create Branch Categories Table
CREATE TABLE IF NOT EXISTS branch_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE branch_categories ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for branch_categories
CREATE POLICY "Anyone can view branch categories"
  ON branch_categories
  FOR SELECT
  TO authenticated
  USING (true);

-- Create Branches Table
CREATE TABLE IF NOT EXISTS branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  logo text,
  banner text,
  description text,
  introduction text,
  philosophy text,
  is_featured boolean DEFAULT false,
  category_id uuid REFERENCES branch_categories(id),
  district text,
  member_count integer DEFAULT 0,
  owner_id uuid NOT NULL REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT one_branch_per_user UNIQUE (owner_id)
);

-- Enable Row Level Security
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS branches_owner_id_idx ON branches(owner_id);
CREATE INDEX IF NOT EXISTS branches_is_featured_idx ON branches(is_featured);
CREATE INDEX IF NOT EXISTS branches_category_id_idx ON branches(category_id);
CREATE INDEX IF NOT EXISTS branches_district_idx ON branches(district);

-- Create RLS Policies for branches

-- Anyone can view branches
CREATE POLICY "Anyone can view branches"
  ON branches
  FOR SELECT
  TO authenticated
  USING (true);

-- Users can create their own branch
CREATE POLICY "Users can create their own branch"
  ON branches
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = owner_id AND
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role = 'president'
    ) AND
    NOT EXISTS (
      SELECT 1 FROM branches
      WHERE owner_id = auth.uid()
    )
  );

-- Users can update their own branch
CREATE POLICY "Users can update their own branch"
  ON branches
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = owner_id);

-- Users can delete their own branch
CREATE POLICY "Users can delete their own branch"
  ON branches
  FOR DELETE
  TO authenticated
  USING (auth.uid() = owner_id);

-- Add updated_at trigger
CREATE TRIGGER branches_updated_at
  BEFORE UPDATE ON branches
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Create Branch Members Table
CREATE TABLE IF NOT EXISTS branch_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id),
  joined_at timestamptz DEFAULT now(),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(branch_id, user_id)
);

-- Enable Row Level Security
ALTER TABLE branch_members ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS branch_members_branch_id_idx ON branch_members(branch_id);
CREATE INDEX IF NOT EXISTS branch_members_user_id_idx ON branch_members(user_id);
CREATE INDEX IF NOT EXISTS branch_members_status_idx ON branch_members(status);

-- Create RLS Policies for branch_members

-- Branch owners can view their branch members
CREATE POLICY "Branch owners can view their branch members"
  ON branch_members
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_members.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Users can view branches they are members of
CREATE POLICY "Users can view branches they are members of"
  ON branch_members
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Branch owners can manage their branch members
CREATE POLICY "Branch owners can manage their branch members"
  ON branch_members
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_members.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER branch_members_updated_at
  BEFORE UPDATE ON branch_members
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Create Branch Member Applications Table
CREATE TABLE IF NOT EXISTS branch_member_applications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id),
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  message text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(branch_id, user_id)
);

-- Enable Row Level Security
ALTER TABLE branch_member_applications ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS branch_member_applications_branch_id_idx ON branch_member_applications(branch_id);
CREATE INDEX IF NOT EXISTS branch_member_applications_user_id_idx ON branch_member_applications(user_id);
CREATE INDEX IF NOT EXISTS branch_member_applications_status_idx ON branch_member_applications(status);

-- Create RLS Policies for branch_member_applications

-- Branch owners can view applications for their branch
CREATE POLICY "Branch owners can view applications for their branch"
  ON branch_member_applications
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_member_applications.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Users can view their own applications
CREATE POLICY "Users can view their own applications"
  ON branch_member_applications
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Users can apply to join branches
CREATE POLICY "Users can apply to join branches"
  ON branch_member_applications
  FOR INSERT
  TO authenticated
  WITH CHECK (
    user_id = auth.uid() AND
    NOT EXISTS (
      SELECT 1 FROM branch_members
      WHERE branch_members.branch_id = branch_member_applications.branch_id
      AND branch_members.user_id = auth.uid()
    ) AND
    NOT EXISTS (
      SELECT 1 FROM branch_member_applications
      WHERE branch_member_applications.branch_id = branch_member_applications.branch_id
      AND branch_member_applications.user_id = auth.uid()
      AND branch_member_applications.status = 'pending'
    )
  );

-- Branch owners can manage applications for their branch
CREATE POLICY "Branch owners can manage applications for their branch"
  ON branch_member_applications
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_member_applications.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER branch_member_applications_updated_at
  BEFORE UPDATE ON branch_member_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Create User Liked Branches Table
CREATE TABLE IF NOT EXISTS user_liked_branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, branch_id)
);

-- Enable Row Level Security
ALTER TABLE user_liked_branches ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS user_liked_branches_user_id_idx ON user_liked_branches(user_id);
CREATE INDEX IF NOT EXISTS user_liked_branches_branch_id_idx ON user_liked_branches(branch_id);

-- Create RLS Policies for user_liked_branches

-- Users can manage their own liked branches
CREATE POLICY "Users can manage their own liked branches"
  ON user_liked_branches
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create Branch Activities Table
CREATE TABLE IF NOT EXISTS branch_activities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  branch_id uuid NOT NULL REFERENCES branches(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  start_date date NOT NULL,
  end_date date,
  is_recent boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE branch_activities ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS branch_activities_branch_id_idx ON branch_activities(branch_id);
CREATE INDEX IF NOT EXISTS branch_activities_is_recent_idx ON branch_activities(is_recent);

-- Create RLS Policies for branch_activities

-- Anyone can view branch activities
CREATE POLICY "Anyone can view branch activities"
  ON branch_activities
  FOR SELECT
  TO authenticated
  USING (true);

-- Branch owners can manage their branch activities
CREATE POLICY "Branch owners can manage their branch activities"
  ON branch_activities
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM branches
      WHERE branches.id = branch_activities.branch_id
      AND branches.owner_id = auth.uid()
    )
  );

-- Add updated_at trigger
CREATE TRIGGER branch_activities_updated_at
  BEFORE UPDATE ON branch_activities
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Insert some initial branch categories
INSERT INTO branch_categories (title, description)
VALUES 
  ('商業', '商業相關的分會'),
  ('科技', '科技相關的分會'),
  ('教育', '教育相關的分會'),
  ('藝術', '藝術相關的分會'),
  ('健康', '健康相關的分會'),
  ('社交', '社交相關的分會'),
  ('其他', '其他類型的分會');

-- Create function to update branch member count
CREATE OR REPLACE FUNCTION update_branch_member_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE branches
    SET member_count = member_count + 1
    WHERE id = NEW.branch_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE branches
    SET member_count = member_count - 1
    WHERE id = OLD.branch_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update branch member count
CREATE TRIGGER update_branch_member_count_trigger
  AFTER INSERT OR DELETE ON branch_members
  FOR EACH ROW
  EXECUTE FUNCTION update_branch_member_count();

-- Create function to handle approved applications
CREATE OR REPLACE FUNCTION handle_approved_application()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'approved' AND OLD.status = 'pending' THEN
    -- Add user to branch members
    INSERT INTO branch_members (branch_id, user_id)
    VALUES (NEW.branch_id, NEW.user_id)
    ON CONFLICT (branch_id, user_id) DO NOTHING;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to handle approved applications
CREATE TRIGGER handle_approved_application_trigger
  AFTER UPDATE ON branch_member_applications
  FOR EACH ROW
  EXECUTE FUNCTION handle_approved_application();
