/*
  # Add Sequential User Number System

  1. Changes
    - Add user_number column to users table
    - Create sequence for user numbers
    - Add trigger to automatically assign numbers
    - Ensure numbers are sequential and unique
    
  2. Security
    - Maintain existing RLS policies
    - Add index for efficient lookups
*/

-- Create sequence for user numbers
CREATE SEQUENCE IF NOT EXISTS user_number_seq;

-- Add user_number column
ALTER TABLE users ADD COLUMN IF NOT EXISTS user_number bigint UNIQUE;

-- Function to assign user number
CREATE OR REPLACE FUNCTION assign_user_number()
RETURNS TRIGGER AS $$
BEGIN
  -- Get next value from sequence
  NEW.user_number := nextval('user_number_seq');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to automatically assign numbers
CREATE TRIGGER assign_user_number_trigger
  BEFORE INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION assign_user_number();

-- Create index for efficient lookups
CREATE INDEX IF NOT EXISTS users_user_number_idx ON users(user_number);

-- Update existing users if any (will be empty in new deployments)
DO $$
BEGIN
  UPDATE users SET user_number = nextval('user_number_seq')
  WHERE user_number IS NULL;
END $$;