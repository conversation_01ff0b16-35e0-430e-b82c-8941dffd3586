/*
  # Add Organization Creator Branch Auto-Membership Trigger

  1. Changes
    - Create a trigger to automatically add the creator's branch to the organization when an organization is created
    - Ensure the creator's branch is always the first member of the organization
    - Add a check to ensure the user has a branch before they can create an organization

  2. Security
    - No security changes needed
*/

-- <PERSON>reate function to handle organization creator branch auto-membership
CREATE OR REPLACE FUNCTION handle_organization_creator_branch_auto_membership()
RETURNS TRIGGER AS $$
DECLARE
  creator_branch_id uuid;
BEGIN
  -- Find the branch owned by the organization creator
  SELECT id INTO creator_branch_id
  FROM branches
  WHERE owner_id = NEW.owner_id
  LIMIT 1;

  -- If the creator has a branch, add it to the organization
  IF creator_branch_id IS NOT NULL THEN
    INSERT INTO organization_branches (organization_id, branch_id, status, joined_at)
    VALUES (NEW.id, creator_branch_id, 'active', NOW())
    ON CONFLICT (organization_id, branch_id)
    DO UPDATE SET
      status = 'active',
      joined_at = NOW();
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to handle organization creator branch auto-membership
CREATE TRIGGER organization_creator_branch_auto_membership_trigger
  AFTER INSERT ON organizations
  FOR EACH ROW
  EXECUTE FUNCTION handle_organization_creator_branch_auto_membership();

-- Check if the policy already exists and drop it if it does
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_policies WHERE policyname = 'Users must have a branch to create an organization'
  ) THEN
    DROP POLICY "Users must have a branch to create an organization" ON organizations;
  END IF;
END
$$;

-- Add RLS policy to ensure user has a branch before creating an organization
CREATE POLICY "Users must have a branch to create an organization"
  ON organizations
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = owner_id AND
    EXISTS (
      SELECT 1 FROM branches
      WHERE owner_id = auth.uid()
    )
  );
