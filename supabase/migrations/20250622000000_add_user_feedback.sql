/*
  # Add User Feedback System

  1. Changes
    - Create user_feedback table for storing user feedback and suggestions
    - Add RLS policies for security
    - Create indexes for better query performance
    
  2. Security
    - Enable RLS on new table
    - Set up policies for viewing and managing feedback
    - Allow anonymous feedback submissions
*/

-- Create user_feedback table
CREATE TABLE IF NOT EXISTS user_feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  category text NOT NULL CHECK (category IN ('bug', 'feature', 'improvement', 'ui', 'performance', 'other')),
  subject text NOT NULL,
  description text NOT NULL,
  contact_email text,
  is_anonymous boolean DEFAULT false,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'resolved', 'closed')),
  admin_notes text,
  admin_response text,
  responded_by uuid REFERENCES auth.users(id),
  responded_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS user_feedback_user_id_idx ON user_feedback(user_id);
CREATE INDEX IF NOT EXISTS user_feedback_category_idx ON user_feedback(category);
CREATE INDEX IF NOT EXISTS user_feedback_status_idx ON user_feedback(status);
CREATE INDEX IF NOT EXISTS user_feedback_created_at_idx ON user_feedback(created_at);

-- Create RLS Policies for user_feedback

-- Users can view their own feedback (non-anonymous only)
CREATE POLICY "Users can view their own feedback"
  ON user_feedback
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() = user_id AND is_anonymous = false
  );

-- Users can insert feedback (both authenticated and anonymous)
CREATE POLICY "Users can submit feedback"
  ON user_feedback
  FOR INSERT
  TO authenticated
  WITH CHECK (
    -- Allow if user is submitting their own feedback or anonymous feedback
    (auth.uid() = user_id AND is_anonymous = false) OR 
    (user_id IS NULL AND is_anonymous = true)
  );

-- Allow anonymous feedback submissions
CREATE POLICY "Allow anonymous feedback submissions"
  ON user_feedback
  FOR INSERT
  TO anon
  WITH CHECK (
    user_id IS NULL AND is_anonymous = true
  );

-- Admins can view and manage all feedback
CREATE POLICY "Admins can manage all feedback"
  ON user_feedback
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_admin = true
    )
  );

-- Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_feedback_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for updated_at
CREATE TRIGGER update_user_feedback_updated_at_trigger
  BEFORE UPDATE ON user_feedback
  FOR EACH ROW
  EXECUTE FUNCTION update_user_feedback_updated_at();

-- Create function to get feedback statistics (for admins)
CREATE OR REPLACE FUNCTION get_feedback_stats()
RETURNS TABLE (
  total_feedback bigint,
  pending_feedback bigint,
  resolved_feedback bigint,
  category_stats jsonb
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM user_feedback) as total_feedback,
    (SELECT COUNT(*) FROM user_feedback WHERE status = 'pending') as pending_feedback,
    (SELECT COUNT(*) FROM user_feedback WHERE status = 'resolved') as resolved_feedback,
    (
      SELECT jsonb_object_agg(category, count)
      FROM (
        SELECT category, COUNT(*) as count
        FROM user_feedback
        GROUP BY category
      ) category_counts
    ) as category_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION get_feedback_stats() TO authenticated;
