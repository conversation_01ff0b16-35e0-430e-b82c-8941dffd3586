/*
  # Fix Referral Code Verification

  1. Changes
    - Update verify_referral_code function to be more accessible
    - Add proper error handling
    - Make function stable and parallel safe
    
  2. Security
    - Keep function security definer
    - Add proper input validation
*/

-- Drop and recreate the function with improved accessibility
DROP FUNCTION IF EXISTS verify_referral_code;

CREATE OR REPLACE FUNCTION verify_referral_code(code text)
RETURNS TABLE (
  id uuid,
  username text,
  full_name text
) AS $$
BEGIN
  -- Validate input
  IF code IS NULL OR LENGTH(TRIM(code)) = 0 THEN
    RAISE EXCEPTION 'Referral code cannot be empty';
  END IF;

  RETURN QUERY
  SELECT u.id, u.username, u.full_name
  FROM users u
  WHERE u.referral_code = UPPER(TRIM(code));
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER
STABLE
PARALLEL SAFE;

-- Grant execute permission to public
GRANT EXECUTE ON FUNCTION verify_referral_code(text) TO public;

-- Add comment for documentation
COMMENT ON FUNCTION verify_referral_code(text) IS 'Verifies a referral code and returns the referrer''s basic information';