/*
  # Add Branch Owner Auto-Membership Trigger

  1. Changes
    - Create a trigger to automatically add branch owner as a member when a branch is created
    - Ensure branch owner is always the first member of their branch
    
  2. Security
    - No security changes needed
*/

-- <PERSON>reate function to handle branch owner auto-membership
CREATE OR REPLACE FUNCTION handle_branch_owner_auto_membership()
RETURNS TRIGGER AS $$
BEGIN
  -- Add branch owner as a member with active status
  INSERT INTO branch_members (branch_id, user_id, status, joined_at)
  VALUES (NEW.id, NEW.owner_id, 'active', NOW())
  ON CONFLICT (branch_id, user_id) 
  DO UPDATE SET 
    status = 'active',
    joined_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to handle branch owner auto-membership
CREATE TRIGGER branch_owner_auto_membership_trigger
  AFTER INSERT ON branches
  FOR EACH ROW
  EXECUTE FUNCTION handle_branch_owner_auto_membership();
