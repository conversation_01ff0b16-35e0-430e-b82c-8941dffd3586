/*
  # Update Existing Data for Auto-Membership

  1. Changes
    - Add branch owners as members for branches where they are not already members
    - Add organization creators' branches as members for organizations where they are not already members
    
  2. Security
    - No security changes needed
*/

-- Add branch owners as members for branches where they are not already members
DO $$
DECLARE
  branch_rec RECORD;
BEGIN
  FOR branch_rec IN 
    SELECT b.id AS branch_id, b.owner_id
    FROM branches b
    WHERE NOT EXISTS (SELECT 1 FROM branch_members bm WHERE bm.branch_id = b.id AND bm.user_id = b.owner_id)
  LOOP
    INSERT INTO branch_members (branch_id, user_id, status, joined_at)
    VALUES (branch_rec.branch_id, branch_rec.owner_id, 'active', NOW())
    ON CONFLICT (branch_id, user_id) DO NOTHING;
  END LOOP;
END;
$$;

-- Add organization creators' branches as members for organizations where they are not already members
DO $$
DECLARE
  org_rec RECORD;
BEGIN
  FOR org_rec IN 
    SELECT o.id AS org_id, b.id AS branch_id
    FROM organizations o
    JOIN branches b ON o.owner_id = b.owner_id
    WHERE NOT EXISTS (SELECT 1 FROM organization_branches ob WHERE ob.organization_id = o.id AND ob.branch_id = b.id)
  LOOP
    INSERT INTO organization_branches (organization_id, branch_id, status, joined_at)
    VALUES (org_rec.org_id, org_rec.branch_id, 'active', NOW())
    ON CONFLICT (organization_id, branch_id) DO NOTHING;
  END LOOP;
END;
$$;
