/*
  # Update Referral Code Generation

  1. Changes
    - Modify character set to prefer numbers
    - Remove potentially confusing characters
    - Update function to generate more numeric codes
    
  2. Security
    - No security changes
*/

-- Drop existing function
DROP FUNCTION IF EXISTS generate_referral_code;

-- Recreate with updated character set
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS text AS $$
DECLARE
  numbers text := '23456789';  -- Excluding 0 and 1
  letters text := 'ABCDEFGHJKLMNPQRSTUVWXYZ';  -- Excluding I and O
  code text := '';
  i integer;
  use_number boolean;
BEGIN
  -- Generate 6 character code with 70% chance of numbers
  FOR i IN 1..6 LOOP
    use_number := random() < 0.7;  -- 70% chance of using numbers
    
    IF use_number THEN
      code := code || substr(numbers, floor(random() * length(numbers) + 1)::integer, 1);
    ELSE
      code := code || substr(letters, floor(random() * length(letters) + 1)::integer, 1);
    END IF;
  END LOOP;
  
  -- Check if code exists and regenerate if needed
  WHILE EXISTS (SELECT 1 FROM public.users WHERE referral_code = code) LOOP
    code := '';
    FOR i IN 1..6 LOOP
      use_number := random() < 0.7;  -- Maintain 70% chance of numbers
      
      IF use_number THEN
        code := code || substr(numbers, floor(random() * length(numbers) + 1)::integer, 1);
      ELSE
        code := code || substr(letters, floor(random() * length(letters) + 1)::integer, 1);
      END IF;
    END LOOP;
  END LOOP;
  
  RETURN code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER VOLATILE;

-- Grant execute permission to public
GRANT EXECUTE ON FUNCTION generate_referral_code() TO public;

-- Add comment for documentation
COMMENT ON FUNCTION generate_referral_code() IS 'Generates a unique 6-character referral code with 70% chance of numbers';