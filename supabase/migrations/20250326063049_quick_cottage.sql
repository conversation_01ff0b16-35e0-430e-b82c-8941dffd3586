/*
  # Update Events Table Structure

  1. Changes
    - Rename president_id to user_id for more generic usage
    - Update foreign key and indexes
    - Update RLS policies to reflect the change
    
  2. Security
    - Maintain existing RLS policies with updated column name
    - Ensure data access security is preserved
*/

-- Rename president_id to user_id
ALTER TABLE events
RENAME COLUMN president_id TO user_id;

-- Update foreign key
ALTER TABLE events
DROP CONSTRAINT events_president_id_fkey,
ADD CONSTRAINT events_user_id_fkey
  FOREIGN KEY (user_id)
  REFERENCES users(id);

-- Update index
DROP INDEX IF EXISTS events_president_id_idx;
CREATE INDEX IF NOT EXISTS events_user_id_idx ON events(user_id);

-- Update RLS policies
DROP POLICY IF EXISTS "Presidents can manage their own events" ON events;

CREATE POLICY "Users can manage their own events"
  ON events
  FOR ALL
  TO authenticated
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('president', 'merchant')
    )
  );