/*
  # Add User Profile Creation Trigger

  1. Changes
    - Remove manual profile creation from frontend
    - Add trigger to automatically create user profile on auth.users insert
    - Simplify RLS policies
    
  2. Security
    - Maintain RLS for data access
    - Remove direct insert access to users table
    - Handle profile creation automatically
*/

-- Drop the registration policy as we'll handle it via trigger
DROP POLICY IF EXISTS "Enable insert for registration" ON users;

-- Create function to handle user profile creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_role text;
  new_industry text;
  new_company_name text;
BEGIN
  -- Extract and validate role
  new_role := COALESCE(NEW.raw_user_meta_data->>'role', 'free');
  IF new_role NOT IN ('free', 'merchant', 'president') THEN
    new_role := 'free';
  END IF;

  -- Extract optional fields
  new_industry := NEW.raw_user_meta_data->>'industry';
  new_company_name := NEW.raw_user_meta_data->>'company_name';

  -- Validate industry and company_name based on role
  IF new_role IN ('merchant', 'president') THEN
    IF new_industry IS NULL OR new_company_name IS NULL THEN
      RAISE EXCEPTION 'Industry and company name are required for merchant and president roles';
    END IF;
  END IF;

  -- Insert new user
  INSERT INTO public.users (
    id,
    username,
    full_name,
    email,
    phone,
    role,
    referrer_id,
    industry,
    company_name
  ) VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'username',
    NEW.raw_user_meta_data->>'full_name',
    NEW.email,
    NEW.raw_user_meta_data->>'phone',
    new_role,
    (NEW.raw_user_meta_data->>'referrer_id')::uuid,
    new_industry,
    new_company_name
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger on auth.users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Update RLS policies
DROP POLICY IF EXISTS "Enable read access to own profile" ON users;
DROP POLICY IF EXISTS "Enable update access to own profile" ON users;

CREATE POLICY "Enable read access to own profile"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Enable update access to own profile"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Add policy to allow reading user emails for registration validation
CREATE POLICY "Allow email checks during registration"
  ON users
  FOR SELECT
  TO anon, authenticated
  USING (true);