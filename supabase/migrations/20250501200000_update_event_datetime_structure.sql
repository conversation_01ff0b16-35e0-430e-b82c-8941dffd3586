/*
  # Update Events Table Structure for DateTime

  1. Changes
    - Add new timestamp columns for start_datetime and end_datetime
    - Migrate existing data to new columns
    - Drop old date, start_time, end_time columns
    
  2. Security
    - No security changes needed
*/

-- Add new timestamp columns
ALTER TABLE events
ADD COLUMN start_datetime timestamp with time zone,
ADD COLUMN end_datetime timestamp with time zone;

-- Migrate existing data to new columns
UPDATE events
SET 
  start_datetime = (date || ' ' || start_time)::timestamp with time zone,
  end_datetime = (date || ' ' || end_time)::timestamp with time zone;

-- Make the new columns NOT NULL after data migration
ALTER TABLE events
ALTER COLUMN start_datetime SET NOT NULL,
ALTER COLUMN end_datetime SET NOT NULL;

-- Drop old columns
ALTER TABLE events
DROP COLUMN date,
DROP COLUMN start_time,
DROP COLUMN end_time;

-- Update triggers
DROP TRIGGER IF EXISTS events_updated_at ON events;
CREATE TRIGGER events_updated_at
  BEFORE UPDATE ON events
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();
