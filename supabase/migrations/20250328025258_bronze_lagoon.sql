/*
  # Create Storage Bucket for Products

  1. Changes
    - Create storage bucket for product images
    - Set up RLS policies for bucket access
    
  2. Security
    - Allow authenticated users to upload files
    - Allow public read access to product images
*/

-- Enable storage by creating the bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('products', 'products', true);

-- Policy to allow authenticated users to upload files
CREATE POLICY "Allow authenticated uploads"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'products'
);

-- Policy to allow authenticated users to update their own files
CREATE POLICY "Allow authenticated updates"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
  bucket_id = 'products'
  AND auth.uid() = owner
);

-- Policy to allow authenticated users to delete their own files
CREATE POLICY "Allow authenticated deletes"
ON storage.objects
FOR DELETE
TO authenticated
USING (
  bucket_id = 'products'
  AND auth.uid() = owner
);

-- Policy to allow public read access to all files
CREATE POLICY "Allow public read access"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'products');