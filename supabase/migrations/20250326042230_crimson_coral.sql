/*
  # Add secure login lookup policy

  1. Changes
    - Add policy to allow email lookup during login
    - Create secure view for login info
    
  2. Security
    - Only expose necessary fields for login
    - Maintain data privacy
*/

-- Create a secure view for login lookups
CREATE OR REPLACE VIEW user_login_info AS
SELECT id, email, username
FROM users;

-- Add policy to allow reading usernames and emails for login
CREATE POLICY "Allow email lookup for login"
  ON users
  FOR SELECT
  TO anon, authenticated
  USING (true);