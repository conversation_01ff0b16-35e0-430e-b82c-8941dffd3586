/*
  # Update Users Table RLS Policy

  1. Changes
    - Add policy to allow inserting new users during registration
    - Keep existing policies for user profile access
    
  2. Security
    - Only allow inserts during registration process
    - Maintain existing RLS for data access
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can read their own profile" ON users;
DROP POLICY IF EXISTS "Users can update their own profile" ON users;

-- Create updated policies
CREATE POLICY "Users can read their own profile"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Add new policy for registration
CREATE POLICY "Enable insert for registration"
  ON users
  FOR INSERT
  TO anon, authenticated
  WITH CHECK (auth.uid() = id);