import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import type { MaterialRequest, CreateMaterialRequest, District } from '@/services/schema';
import { MaterialRequestSchema, DistrictSchema } from '@/services/schema';
import { useAuthStore } from './auth';

export const useMaterialRequestsStore = defineStore('materialRequests', () => {
  const materialRequests = ref<MaterialRequest[]>([]);
  const userRequests = ref<MaterialRequest[]>([]);
  const districts = ref<District[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const authStore = useAuthStore();

  // Get active material requests
  const activeRequests = computed(() => {
    return materialRequests.value.filter(request => request.status === 'active');
  });

  // Get requests by district
  const getRequestsByDistrict = computed(() => {
    return (districtId: string) => {
      return activeRequests.value.filter(request => request.district_id === districtId);
    };
  });

  // Get requests by category
  const getRequestsByCategory = computed(() => {
    return (category: string) => {
      return activeRequests.value.filter(request => request.category === category);
    };
  });

  // Fetch all active material requests
  const fetchMaterialRequests = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const { data, error: supabaseError } = await supabase
        .from('material_requests')
        .select(`
          *,
          user:user_id (
            id,
            full_name,
            phone
          ),
          district:district_id (
            id,
            name_en,
            name_zh,
            region
          )
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false });
      
      if (supabaseError) throw supabaseError;
      
      // Validate and transform the data
      const validatedData = data.map(request => MaterialRequestSchema.parse(request));
      materialRequests.value = validatedData;
    } catch (err: any) {
      console.error('Error fetching material requests:', err);
      error.value = err.message || 'Failed to fetch material requests';
    } finally {
      isLoading.value = false;
    }
  };

  // Fetch user's own material requests
  const fetchUserMaterialRequests = async () => {
    if (!authStore.isAuthenticated) return;
    
    isLoading.value = true;
    error.value = null;
    
    try {
      const { data, error: supabaseError } = await supabase
        .from('material_requests')
        .select(`
          *,
          district:district_id (
            id,
            name_en,
            name_zh,
            region
          )
        `)
        .eq('user_id', authStore.currentUser?.id)
        .order('created_at', { ascending: false });
      
      if (supabaseError) throw supabaseError;
      
      const validatedData = data.map(request => MaterialRequestSchema.parse(request));
      userRequests.value = validatedData;
    } catch (err: any) {
      console.error('Error fetching user material requests:', err);
      error.value = err.message || 'Failed to fetch user material requests';
    } finally {
      isLoading.value = false;
    }
  };

  // Create a new material request
  const createMaterialRequest = async (requestData: CreateMaterialRequest): Promise<MaterialRequest> => {
    if (!authStore.isAuthenticated) {
      throw new Error('User must be authenticated to create material requests');
    }

    try {
      const newRequest = {
        ...requestData,
        user_id: authStore.currentUser?.id,
      };

      const { data, error } = await supabase
        .from('material_requests')
        .insert(newRequest)
        .select()
        .single();

      if (error) throw error;
      
      const validatedRequest = MaterialRequestSchema.parse(data);
      
      // Add to local state
      materialRequests.value.unshift(validatedRequest);
      userRequests.value.unshift(validatedRequest);
      
      return validatedRequest;
    } catch (err: any) {
      console.error('Error creating material request:', err);
      throw err;
    }
  };

  // Update material request status
  const updateRequestStatus = async (requestId: string, status: 'active' | 'fulfilled' | 'cancelled') => {
    try {
      const { data, error } = await supabase
        .from('material_requests')
        .update({ status })
        .eq('id', requestId)
        .select()
        .single();

      if (error) throw error;
      
      const updatedRequest = MaterialRequestSchema.parse(data);
      
      // Update local state
      const index = materialRequests.value.findIndex(r => r.id === requestId);
      if (index !== -1) {
        materialRequests.value[index] = updatedRequest;
      }
      
      const userIndex = userRequests.value.findIndex(r => r.id === requestId);
      if (userIndex !== -1) {
        userRequests.value[userIndex] = updatedRequest;
      }
      
      return updatedRequest;
    } catch (err: any) {
      console.error('Error updating material request status:', err);
      throw err;
    }
  };

  // Fetch districts for location selection
  const fetchDistricts = async () => {
    try {
      const { data, error } = await supabase
        .from('districts')
        .select('*')
        .order('sort_order', { ascending: true });
      
      if (error) throw error;
      
      const validatedData = data.map(district => DistrictSchema.parse(district));
      districts.value = validatedData;
    } catch (err: any) {
      console.error('Error fetching districts:', err);
      error.value = err.message || 'Failed to fetch districts';
    }
  };

  // Get district by ID
  const getDistrictById = computed(() => {
    return (districtId: string) => {
      return districts.value.find(district => district.id === districtId);
    };
  });

  return {
    materialRequests,
    userRequests,
    districts,
    isLoading,
    error,
    activeRequests,
    getRequestsByDistrict,
    getRequestsByCategory,
    getDistrictById,
    fetchMaterialRequests,
    fetchUserMaterialRequests,
    createMaterialRequest,
    updateRequestStatus,
    fetchDistricts
  };
});
