import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from './auth';
import { useUserStore } from './user';

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'voice' | 'file';
  metadata?: any;
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    full_name: string;
    avatar?: string;
  };
  is_read_by_me?: boolean;
  is_read_by_other?: boolean;
  read_by?: Array<{
    user_id: string;
    read_at: string;
    user?: {
      id: string;
      full_name: string;
    };
  }>;
}

export interface Conversation {
  id: string;
  participant_1_id: string;
  participant_2_id: string;
  last_message_id?: string;
  last_message_at: string;
  created_at: string;
  updated_at: string;
  other_participant?: {
    id: string;
    full_name: string;
    avatar?: string;
  };
  last_message?: Message;
  unread_count?: number;
}

export const useChatStore = defineStore('chat', () => {
  const authStore = useAuthStore();
  const userStore = useUserStore();

  // State
  const conversations = ref<Conversation[]>([]);
  const currentConversation = ref<Conversation | null>(null);
  const messages = ref<Message[]>([]);
  const isLoading = ref(false);
  const isLoadingMessages = ref(false);
  const realtimeChannel = ref<any>(null);
  const connectionStatus = ref<'connected' | 'connecting' | 'disconnected'>('disconnected');
  const lastMessageId = ref<string | null>(null);

  // Computed
  const sortedConversations = computed(() => {
    return [...conversations.value].sort((a, b) =>
      new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime()
    );
  });

  // Get or create conversation between two users
  const getOrCreateConversation = async (otherUserId: string): Promise<Conversation | null> => {
    try {
      if (!authStore.currentUser?.id) return null;

      const currentUserId = authStore.currentUser.id;

      // First try to find existing conversation
      const { data: existingConversation, error: findError } = await supabase
        .from('conversations')
        .select(`
          *,
          participant_1:users!conversations_participant_1_id_fkey(id, full_name, avatar),
          participant_2:users!conversations_participant_2_id_fkey(id, full_name, avatar)
        `)
        .or(`and(participant_1_id.eq.${currentUserId},participant_2_id.eq.${otherUserId}),and(participant_1_id.eq.${otherUserId},participant_2_id.eq.${currentUserId})`)
        .single();

      if (findError && findError.code !== 'PGRST116') {
        throw findError;
      }

      if (existingConversation) {
        // Format the conversation
        const conversation: Conversation = {
          ...existingConversation,
          other_participant: existingConversation.participant_1_id === currentUserId
            ? existingConversation.participant_2
            : existingConversation.participant_1
        };
        return conversation;
      }

      // Create new conversation if it doesn't exist
      const { data: newConversation, error: createError } = await supabase
        .from('conversations')
        .insert({
          participant_1_id: currentUserId,
          participant_2_id: otherUserId
        })
        .select(`
          *,
          participant_1:users!conversations_participant_1_id_fkey(id, full_name, avatar),
          participant_2:users!conversations_participant_2_id_fkey(id, full_name, avatar)
        `)
        .single();

      if (createError) throw createError;

      const conversation: Conversation = {
        ...newConversation,
        other_participant: newConversation.participant_1_id === currentUserId
          ? newConversation.participant_2
          : newConversation.participant_1
      };

      return conversation;
    } catch (error) {
      console.error('Error getting or creating conversation:', error);
      return null;
    }
  };

  // Load user's conversations
  const loadConversations = async () => {
    try {
      if (!authStore.currentUser?.id) return;

      isLoading.value = true;
      const currentUserId = authStore.currentUser.id;

      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          participant_1:users!conversations_participant_1_id_fkey(id, full_name, avatar),
          participant_2:users!conversations_participant_2_id_fkey(id, full_name, avatar),
          last_message:messages!conversations_last_message_id_fkey(
            id, content, message_type, created_at, sender_id,
            read_status:message_read_status(
              user_id,
              read_at,
              user:users!message_read_status_user_id_fkey(id, full_name)
            )
          )
        `)
        .or(`participant_1_id.eq.${currentUserId},participant_2_id.eq.${currentUserId}`)
        .order('last_message_at', { ascending: false });

      if (error) throw error;

      conversations.value = data.map((conv: any) => ({
        ...conv,
        other_participant: conv.participant_1_id === currentUserId
          ? conv.participant_2
          : conv.participant_1,
        last_message: conv.last_message ? {
          ...conv.last_message,
          is_read_by_me: conv.last_message.read_status?.some((rs: any) => rs.user_id === currentUserId),
          is_read_by_other: conv.last_message.read_status?.some((rs: any) => rs.user_id !== currentUserId),
          read_by: conv.last_message.read_status || []
        } : null
      }));

    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // Load messages for a conversation with read status
  const loadMessages = async (conversationId: string) => {
    try {
      isLoadingMessages.value = true;

      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:users!messages_sender_id_fkey(id, full_name, avatar),
          read_status:message_read_status(
            user_id,
            read_at,
            user:users!message_read_status_user_id_fkey(id, full_name)
          )
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Process messages with read status
      messages.value = (data || []).map((msg: any) => ({
        ...msg,
        is_read_by_me: msg.read_status?.some((rs: any) => rs.user_id === authStore.currentUser?.id),
        is_read_by_other: msg.read_status?.some((rs: any) => rs.user_id !== authStore.currentUser?.id),
        read_by: msg.read_status || []
      }));

      // Mark unread messages from other users as read
      await markUnreadMessagesAsRead();

    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      isLoadingMessages.value = false;
    }
  };

  // Send a message with optimistic updates
  const sendMessage = async (conversationId: string, content: string, messageType: 'text' | 'image' | 'voice' | 'file' = 'text', metadata?: any): Promise<Message | null> => {
    try {
      if (!authStore.currentUser?.id) return null;

      // Create optimistic message for immediate UI update
      const optimisticMessage: Message = {
        id: `temp_${Date.now()}`,
        conversation_id: conversationId,
        sender_id: authStore.currentUser.id,
        content,
        message_type: messageType,
        metadata: metadata || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sender: {
          id: authStore.currentUser.id,
          full_name: userStore.currentUser?.full_name || 'You',
          avatar: userStore.currentUser?.avatar
        }
      };

      // Add optimistic message to UI immediately
      messages.value.push(optimisticMessage);
      console.log('📤 Sending message optimistically:', optimisticMessage);

      // Send to database
      const { data, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: authStore.currentUser.id,
          content,
          message_type: messageType,
          metadata: metadata || {}
        })
        .select(`
          *,
          sender:users!messages_sender_id_fkey(id, full_name, avatar)
        `)
        .single();

      if (error) {
        // Remove optimistic message on error
        const index = messages.value.findIndex(msg => msg.id === optimisticMessage.id);
        if (index > -1) {
          messages.value.splice(index, 1);
        }
        throw error;
      }

      // Replace optimistic message with real message
      const index = messages.value.findIndex(msg => msg.id === optimisticMessage.id);
      if (index > -1) {
        messages.value[index] = data;
        lastMessageId.value = data.id;
        console.log('✅ Message sent successfully, replaced optimistic message');
      }

      return data;
    } catch (error) {
      console.error('❌ Error sending message:', error);
      return null;
    }
  };

  // Mark unread messages from other users as read
  const markUnreadMessagesAsRead = async () => {
    try {
      if (!authStore.currentUser?.id) return;

      const currentUserId = authStore.currentUser.id;

      // Find messages from other users that I haven't read yet
      const unreadMessages = messages.value.filter(msg =>
        msg.sender_id !== currentUserId &&
        !msg.is_read_by_me
      );

      if (unreadMessages.length > 0) {
        const readStatusInserts = unreadMessages.map(msg => ({
          message_id: msg.id,
          user_id: currentUserId
        }));

        const { error: insertError } = await supabase
          .from('message_read_status')
          .insert(readStatusInserts);

        if (insertError) throw insertError;

        // Update local state to reflect read status
        unreadMessages.forEach(msg => {
          msg.is_read_by_me = true;
          msg.read_by = msg.read_by || [];
          msg.read_by.push({
            user_id: currentUserId,
            read_at: new Date().toISOString(),
            user: {
              id: currentUserId,
              full_name: userStore.currentUser?.full_name || 'You'
            }
          });
        });

        console.log(`✅ Marked ${unreadMessages.length} messages as read`);
      }
    } catch (error) {
      console.error('❌ Error marking messages as read:', error);
    }
  };

  // Set up realtime subscription for a conversation
  const subscribeToConversation = (conversationId: string) => {
    if (realtimeChannel.value) {
      supabase.removeChannel(realtimeChannel.value);
    }

    console.log(`🔄 Subscribing to conversation: ${conversationId}`);
    connectionStatus.value = 'connecting';

    realtimeChannel.value = supabase
      .channel(`conversation:${conversationId}`, {
        config: {
          broadcast: { self: true },
          presence: { key: authStore.currentUser?.id }
        }
      })
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        async (payload: any) => {
          try {
            console.log('📨 New message received:', payload.new);

            // Prevent duplicate messages
            if (lastMessageId.value === payload.new.id) {
              console.log('⚠️ Duplicate message detected, skipping');
              return;
            }

            // Check if message already exists in local state
            const existingMessage = messages.value.find(msg => msg.id === payload.new.id);
            if (existingMessage) {
              console.log('⚠️ Message already exists locally, skipping');
              return;
            }

            // Fetch the complete message with sender info
            const { data: newMessage, error } = await supabase
              .from('messages')
              .select(`
                *,
                sender:users!messages_sender_id_fkey(id, full_name, avatar)
              `)
              .eq('id', payload.new.id)
              .single();

            if (error) {
              console.error('❌ Error fetching new message:', error);
              return;
            }

            if (newMessage) {
              console.log('✅ Adding new message to chat:', newMessage);
              messages.value.push(newMessage);
              lastMessageId.value = newMessage.id;

              // Mark as read if not sent by current user
              if (newMessage.sender_id !== authStore.currentUser?.id) {
                await markUnreadMessagesAsRead();
              }
            }
          } catch (error) {
            console.error('❌ Error handling new message:', error);
          }
        }
      )
      .on('subscribe', (status: string) => {
        console.log(`🔗 Subscription status: ${status}`);
        if (status === 'SUBSCRIBED') {
          connectionStatus.value = 'connected';
          console.log('✅ Successfully subscribed to conversation');
        }
      })
      .on('error', (error: any) => {
        console.error('❌ Realtime subscription error:', error);
        connectionStatus.value = 'disconnected';
      })
      .subscribe((status: string) => {
        if (status === 'CHANNEL_ERROR') {
          console.error('❌ Failed to subscribe to conversation');
          connectionStatus.value = 'disconnected';

          // Retry subscription after 3 seconds
          setTimeout(() => {
            console.log('🔄 Retrying subscription...');
            subscribeToConversation(conversationId);
          }, 3000);
        }
      });
  };

  // Clean up realtime subscription
  const unsubscribeFromConversation = () => {
    if (realtimeChannel.value) {
      console.log('🔌 Unsubscribing from conversation');
      supabase.removeChannel(realtimeChannel.value);
      realtimeChannel.value = null;
      connectionStatus.value = 'disconnected';
      lastMessageId.value = null;
    }
  };

  // Set current conversation
  const setCurrentConversation = (conversation: Conversation | null) => {
    currentConversation.value = conversation;
    if (conversation) {
      loadMessages(conversation.id);
      subscribeToConversation(conversation.id);
    } else {
      messages.value = [];
      unsubscribeFromConversation();
    }
  };

  return {
    // State
    conversations,
    currentConversation,
    messages,
    isLoading,
    isLoadingMessages,
    connectionStatus,

    // Computed
    sortedConversations,

    // Actions
    getOrCreateConversation,
    loadConversations,
    loadMessages,
    sendMessage,
    markUnreadMessagesAsRead,
    subscribeToConversation,
    unsubscribeFromConversation,
    setCurrentConversation
  };
});
