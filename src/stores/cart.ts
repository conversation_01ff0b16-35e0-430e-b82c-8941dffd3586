import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Product } from '@/services/schema';
import { ProductSchema } from '@/services/schema';

interface CartItem {
  product: Product;
  quantity: number;
}

export const useCartStore = defineStore('cart', () => {
  const items = ref<CartItem[]>([]);

  const total = computed(() => {
    return items.value.reduce((sum, item) => {
      return sum + item.product.price * item.quantity;
    }, 0);
  });

  const addItem = (productData: unknown, quantity = 1) => {
    try {
      // Validate product data against schema
      const validatedProduct = ProductSchema.parse(productData);
      const existingItem = items.value.find(item => item.product.id === validatedProduct.id);
      
      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        items.value.push({ product: validatedProduct, quantity });
      }
    } catch (error) {
      console.error('Invalid product data:', error);
    }
  };

  const removeItem = (productId: string) => {
    items.value = items.value.filter(item => item.product.id !== productId);
  };

  const clearCart = () => {
    items.value = [];
  };

  return {
    items,
    total,
    addItem,
    removeItem,
    clearCart,
  };
});