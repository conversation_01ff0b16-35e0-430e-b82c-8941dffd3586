import { supabase } from './supabase';

export interface SupabaseImageInput {
  file?: File;
  blob?: Blob;
  base64Data?: string;
  filename?: string;
  mimeType?: string;
}

/**
 * Converts base64 data to a File object
 */
async function base64ToFile(base64Data: string, filename: string = `image_${Date.now()}.jpg`, mimeType: string = 'image/jpeg'): Promise<File> {
  try {
    console.log('Converting base64 to file:', filename);
    // Remove data URL prefix if present
    const base64Content = base64Data.includes('base64,')
      ? base64Data.split('base64,')[1]
      : base64Data;

    // Convert base64 to blob
    const response = await fetch(`data:${mimeType};base64,${base64Content}`);
    const blob = await response.blob();

    // Create a file from the blob
    return new File([blob], filename, { type: mimeType });
  } catch (error) {
    console.error('Error converting base64 to file:', error);
    throw new Error('Failed to convert base64 to file');
  }
}

/**
 * Prepares an image input (File, Blob, or base64) for upload
 */
async function prepareImageForUpload(input: SupabaseImageInput): Promise<File> {
  try {
    // If a File is provided, use it directly
    if (input.file) {
      return input.file;
    }

    // If a Blob is provided, convert it to a File
    if (input.blob) {
      const filename = input.filename || `image_${Date.now()}.jpg`;
      const mimeType = input.mimeType || 'image/jpeg';
      return new File([input.blob], filename, { type: mimeType });
    }

    // If base64 data is provided, convert it to a File
    if (input.base64Data) {
      return await base64ToFile(
        input.base64Data,
        input.filename || `image_${Date.now()}.jpg`,
        input.mimeType || 'image/jpeg'
      );
    }

    throw new Error('No valid image input provided');
  } catch (error) {
    console.error('Error preparing image for upload:', error);
    throw error;
  }
}

/**
 * Uploads a single image to Supabase storage
 */
export async function uploadImageToSupabase(
  input: File | SupabaseImageInput,
  bucket: string = 'products',
  folder: string = ''
): Promise<string> {
  try {
    // Prepare the file for upload
    const file = input instanceof File ? input : await prepareImageForUpload(input);

    // Generate unique filename with prefix
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).slice(-8);
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const filename = `${folder ? folder + '/' : ''}${timestamp}_${randomId}.${fileExtension}`;

    console.log('Uploading file to Supabase:', filename, 'Size:', file.size, 'Type:', file.type);

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filename, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Supabase upload failed:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }

    console.log('Upload successful, data:', data);

    // Get the public URL
    const { data: publicUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);

    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('Error in uploadImageToSupabase function:', error);
    throw error;
  }
}

/**
 * Uploads multiple images to Supabase storage
 */
export async function uploadImagesToSupabase(
  inputs: (File | SupabaseImageInput)[],
  bucket: string = 'products',
  folder: string = ''
): Promise<string[]> {
  if (inputs.length === 0) return [];

  try {
    console.log(`Starting upload of ${inputs.length} images to Supabase`);

    // Upload all files in parallel
    const uploadPromises = inputs.map(input => uploadImageToSupabase(input, bucket, folder));
    const results = await Promise.all(uploadPromises);

    console.log(`Successfully uploaded ${results.length}/${inputs.length} images to Supabase`);
    return results;
  } catch (error) {
    console.error('Error in uploadImagesToSupabase function:', error);
    throw error;
  }
}

/**
 * Deletes an image from Supabase storage
 */
export async function deleteImageFromSupabase(
  url: string,
  bucket: string = 'products'
): Promise<void> {
  try {
    // Extract the file path from the URL
    const urlParts = url.split('/');
    const bucketIndex = urlParts.findIndex(part => part === bucket);
    if (bucketIndex === -1) {
      throw new Error('Invalid URL format');
    }

    const filePath = urlParts.slice(bucketIndex + 1).join('/');

    console.log('Deleting file from Supabase:', filePath);

    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath]);

    if (error) {
      console.error('Supabase delete failed:', error);
      throw new Error(`Failed to delete image: ${error.message}`);
    }

    console.log('Delete successful');
  } catch (error) {
    console.error('Error in deleteImageFromSupabase function:', error);
    throw error;
  }
}
