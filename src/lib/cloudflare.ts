import { supabase } from './supabase';

interface CloudflareUploadResult {
  id: string;
  uploadURL: string;
  result: {
    id: string;
    uploadURL: string;
    variants: string[];
  };
}

export interface ImageInput {
  file?: File;
  base64Data?: string;
  filename?: string;
  mimeType?: string;
}

export async function getUploadToken(count = 1): Promise<CloudflareUploadResult | CloudflareUploadResult[]> {
  const { data: { publicUrl } } = supabase.storage.from('products').getPublicUrl('');
  const functionUrl = publicUrl.replace('/storage/v1/object/public/products', '/functions/v1/get-upload-token');

  const response = await fetch(functionUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
    },
    body: JSON.stringify({ count }),
  });

  if (!response.ok) {
    throw new Error('Failed to get upload token');
  }

  const { tokens } = await response.json();
  return tokens;
}

/**
 * Converts base64 data to a File object
 */
async function base64ToFile(base64Data: string, filename: string = `image_${Date.now()}.jpg`, mimeType: string = 'image/jpeg'): Promise<File> {
  try {
    console.log('Converting base64 to file:', filename);
    // Remove data URL prefix if present
    const base64Content = base64Data.includes('base64,')
      ? base64Data.split('base64,')[1]
      : base64Data;

    // Convert base64 to blob
    const response = await fetch(`data:${mimeType};base64,${base64Content}`);
    const blob = await response.blob();

    // Create a file from the blob
    return new File([blob], filename, { type: mimeType });
  } catch (error) {
    console.error('Error converting base64 to file:', error);
    throw new Error('Failed to convert base64 to file');
  }
}

/**
 * Prepares an image input (File or base64) for upload
 */
async function prepareImageForUpload(input: ImageInput): Promise<File> {
  try {
    // If a File is provided, use it directly
    if (input.file) {
      return input.file;
    }

    // If base64 data is provided, convert it to a File
    if (input.base64Data) {
      return await base64ToFile(
        input.base64Data,
        input.filename || `image_${Date.now()}.jpg`,
        input.mimeType || 'image/jpeg'
      );
    }

    throw new Error('No valid image input provided');
  } catch (error) {
    console.error('Error preparing image for upload:', error);
    throw error;
  }
}

/**
 * Uploads a single image to Cloudflare
 */
export async function uploadImage(input: File | ImageInput, token: any): Promise<string> {
  try {
    // Prepare the file for upload
    const file = input instanceof File ? input : await prepareImageForUpload(input);

    // Create a new file with prefixed name
    const prefixedFile = new File([file], `AXWU_${file.name}`, { type: file.type });
    console.log('Uploading file:', prefixedFile.name, 'Size:', prefixedFile.size, 'Type:', prefixedFile.type);

    const formData = new FormData();
    formData.append('file', prefixedFile);

    // Upload to Cloudflare image storage via batch token
    const response = await fetch('https://batch.imagedelivery.net/images/v1', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` },
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Cloudflare upload failed:', response.status, errorText);
      throw new Error(`Failed to upload image: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    console.log('Upload successful, result:', result);

    // Return the public variant URL
    return result.result.variants.find((url: string) => url.includes('/public')) || result.result.variants[0];
  } catch (error) {
    console.error('Error in uploadImage function:', error);
    throw error;
  }
}

/**
 * Uploads multiple images to Cloudflare using a single token
 */
export async function uploadImages(inputs: (File | ImageInput)[]): Promise<string[]> {
  if (inputs.length === 0) return [];

  try {
    console.log(`Starting upload of ${inputs.length} images`);

    // Get a single batch token for all files
    const token = await getUploadToken() as CloudflareUploadResult;

    // Upload all files in parallel using the same token
    const uploadPromises = inputs.map(input => uploadImage(input, token));
    const results = await Promise.all(uploadPromises);

    console.log(`Successfully uploaded ${results.length}/${inputs.length} images`);
    return results;
  } catch (error) {
    console.error('Error in uploadImages function:', error);
    throw error;
  }
}