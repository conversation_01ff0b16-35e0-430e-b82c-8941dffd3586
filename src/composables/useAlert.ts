import { alertController } from '@ionic/vue';
import type { AlertOptions } from '@ionic/vue';

export function useAlert() {
  /**
   * Present a customizable alert/prompt to the user
   * @param options Alert configuration options
   * @returns Promise that resolves with the alert result when dismissed
   */
  const presentPrompt = async (options: AlertOptions) => {
    const alert = await alertController.create(options);
    await alert.present();
    return alert.onDidDismiss();
  };

  return {
    presentPrompt
  };
}
