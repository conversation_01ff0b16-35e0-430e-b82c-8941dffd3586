// icons
import { trash, close, } from 'ionicons/icons';

import { ref } from 'vue';
import { Camera, CameraResultType, CameraSource } from "@capacitor/camera";
import { actionSheetController, loadingController, isPlatform, getPlatforms } from '@ionic/vue';

export function usePhotoGallery() {
  const photos = ref<Photo[]>([]);

  const takePhoto = async (showPrompt: boolean = true, width = 2000, quality = 80) => {
    const loading = await loadingController.create({ duration: 15000 });
    await loading.present();
    try {
      // Determine the source based on platform and preference
      // On mobile, use prompt if requested, otherwise use Photos
      // On desktop/web, always use Photos (no prompt)
      const canTakePhotos = !isPlatform('desktop') && !isPlatform('ios');
      const source = canTakePhotos ? CameraSource.Prompt : CameraSource.Photos;
      console.log(`Taking photo with source: ${source}`);

      const cameraPhoto = await Camera.getPhoto({
        resultType: CameraResultType.Base64,
        source,
        width,
        quality,
      });
      const fileName = new Date().getTime() + '.jpeg';
      let mimeType = 'image/jpeg'; // default

      if (cameraPhoto.base64String) {
        switch (cameraPhoto.base64String.charAt(0)) {
          case '/':
            mimeType = 'image/jpeg';
            break;
          case 'i':
            mimeType = 'image/png';
            break;
          case 'R':
            mimeType = 'image/gif';
            break;
          case 'U':
            mimeType = 'image/webp';
            break;
        }
      }
      const image = {
        filepath: fileName,
        base64Data: `data:${mimeType};base64,${cameraPhoto.base64String}`,
        mimeType,
      }
      photos.value = [image, ...photos.value];
      return image;
    } catch (e) {
      console.error(e);
    } finally {
      loading.dismiss();
    }
  };

  const deletePhoto = async (photo: Photo) => {
    photos.value = photos.value.filter(p => p.filepath !== photo.filepath);
  };

  const showActionSheet = async (photo: Photo) => {
    const actionSheet = await actionSheetController.create({
      header: 'Photos',
      buttons: [{
        text: '刪除',
        role: 'destructive',
        icon: trash,
        handler: () => {
          deletePhoto(photo);
        }
      },
      {
        text: '取消',
        icon: close,
        role: 'cancel',
      }]
    });
    await actionSheet.present();
  }

  const setPhotos = (newPhotos: any) => {
    photos.value = newPhotos;
  }

  return {
    photos,
    takePhoto,
    showActionSheet,
    deletePhoto,
    setPhotos,
  };
}

export interface Photo {
  filepath?: string;
  base64Data?: string;
  mimeType?: string;
}