import { alertController, isPlatform, loading<PERSON>ontroller, modalController, toastController } from '@ionic/vue';
import { ZodError } from 'zod';
import type { UserRole } from '@/types';
import moment from 'moment';
import Compressor from 'compressorjs';

export function utils() {
  const sleep = (s: any) => {
    return new Promise((resolve) => {
      setTimeout(resolve, s * 1000);
    });
  };
  const openModal = async (component: any, componentProps: any, backdropDismiss = true, cssClass = '', sheetModal = false,
                            initialBreakpoint = 1, showHandle = false) => {
    const modal = await modalController.create({
      ...(sheetModal ? {
        breakpoints: [...new Set([0, initialBreakpoint, 1])],
        initialBreakpoint,
        handle: showHandle,
      } : {}),
      cssClass,
      backdropDismiss,
      component,
      componentProps,
    });
    await modal.present();
    return modal;
  }

  const toCamel = (str) => {
    return str.replace(/([-_][a-z])/ig, ($1) => {
      return $1.toUpperCase()
        .replace('-', '')
        .replace('_', '');
    });
  };
  const convertKeysToCamelCase = (o) => {
    let newO, origKey, newKey, value;
    if (o instanceof Array) {
      return o.map(function(value) {
          if (typeof value === "object") {
            value = convertKeysToCamelCase(value)
          }
          return value
      })
    } else {
      newO = {};
      for (origKey in o) {
        if (Object.prototype.hasOwnProperty.call(o, origKey)) {
          if (origKey === "_RowNumber") { // skip AppSheet row number property
            newO[origKey] = o[origKey];
          } else {
            newKey = toCamel(origKey);
            value = o[origKey]
            if (value instanceof Array || (value && value.constructor === Object)) {
              value = convertKeysToCamelCase(value)
            }
            newO[newKey] = value
          }
        }
      }
    }
    return newO
  };

  const getHTMLImg = async (imgLink: any) => {
    return new Promise((resolve: any) => {
      const IMG_RETRY_MS = 3000; // retry downloading images every X ms on failure
      const img = new Image();
      img.onload = () => {
        const { width, height } = img;
        resolve({ img, aspectRatio: width/height, width, height });
      }
      img.onerror = () => { setTimeout(() => img.src = imgLink, IMG_RETRY_MS); }
      img.crossOrigin = 'Anonymous';
      img.src = imgLink;
    })
  }


  const presentToast = async(msg: string, duration = 3000, position: any = 'bottom') => {
    const toast = await toastController.create({
      message: msg,
      duration,
      position,
    });
    toast.present();
  }

  const formatZodError = (error: ZodError) => {
    return error.errors.map(err => {
      // Remove the path from the message
      const message = err.message.replace(`${err.path.join('.')} `, '');

      // Translate field names to Chinese
      const fieldMap: Record<string, string> = {
        title: '活動標題',
        date: '活動日期',
        start_time: '開始時間',
        end_time: '結束時間',
        address: '地址',
        description: '活動描述',
        banner_photo: '橫幅圖片',
        max_participants: '人數上限'
      };

      // Get the field name
      const field = err.path[0] as string;
      const fieldName = fieldMap[field] || field;

      return `${fieldName}：${message}`;
    }).join('\n');
  };

  const compressImage = async (imageBase64Data: string, maxWidth = 1500, quality = 0.8): Promise<Blob> => {
    const response = await fetch(imageBase64Data);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      new Compressor(blob, {
        maxWidth,
        quality,
        success: resolve,
        error: reject,
      });
    });
  };

  const formatTime = (time: string) => (time ? time.toString().split(":").slice(0, 2).join(":") : '');
  const formatDay = (date: string) => (new Date(date).getDate().toString());
  const formatMonth = (date: string) => (new Date(date).toLocaleDateString('zh-HK', { month: 'short' }));

  // Get user role label in Chinese
  const getRoleLabel = (role?: UserRole) => {
    switch (role) {
      case 'free':
        return '普通會員';
      case 'merchant':
        return '商家會員';
      case 'president':
        return '分會長';
      default:
        return '未知';
    }
  };

  return {
    formatTime, formatDay, formatMonth,
    formatDate: (date: any, format = 'YYYY/M/D') => moment(new Date(date)).format(format),
    copyText: (text, msg = "Text copied.") => {
      navigator.clipboard.writeText(text); // copy innerText of the span
      presentToast(msg, 1000);
    },
    getHTMLImg,
    convertKeysToCamelCase,
    getRoleLabel,
    getBase64FromUrl: async (url, noCache  = false) => {
      const imgUrl = noCache ? `${url}&ts=${new Date().valueOf()}` : url;
      //const options: any = noCache ? { cache: "no-store" } : {};
      //const data = await fetch(getProxyImgLink(imgUrl));
      const data = await fetch(imgUrl);
      const blob = await data.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = () => {
          const base64data = reader.result;
          resolve(base64data);
        }
      });
    },

    isNativeApp: () => (Capacitor.isNativePlatform()),
    isAndroid: () => (isPlatform('android')),
    isMobileWebApp: () => (isPlatform('mobileweb') && (isPlatform('ios') || isPlatform('android'))),
    iPhoneNativeApp: () => (isPlatform('ios') && !isPlatform('mobileweb')),

    uniqueId: () => Math.random().toString(36).slice(-8),
    sleep,
    openModal,
    closeModal: async (data: any = {}) => (await modalController.dismiss(data)),
    reloadApp: () => {
      SplashScreen.show();
      window.location.reload();
    },
    numberWithCommas: (x: any) => {
      return (x || "").toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    distanceBetweenCoordinates: (lat1: any, lon1: any, lat2: any, lon2: any) => {
      const p = 0.017453292519943295;    // Math.PI / 180
      const c = Math.cos;
      const a = 0.5 - c((lat2 - lat1) * p)/2 +
              c(lat1 * p) * c(lat2 * p) *
              (1 - c((lon2 - lon1) * p))/2;

      return 12742 * Math.asin(Math.sqrt(a)); // 2 * R; R = 6371 km
    },
    presentPrompt: async (options: any) => {
      const alert = await alertController.create(options);
      await alert.present();
      return alert.onDidDismiss();
    },
    presentAlert: async (msg: string, hideHeader = false) => {
      const obj: any = {
        header: t('errorHeader'),
        message: msg,
        buttons: ['OK']
      }
      if (hideHeader) delete obj.header;
      const alert = await alertController.create(obj);
      return await alert.present();
    },
    formatZodError,
    presentToast,
    compressImage,
  }
}