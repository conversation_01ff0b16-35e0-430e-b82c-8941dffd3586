import { createRouter, createWebHistory } from '@ionic/vue-router';
import { RouteRecordRaw } from 'vue-router';
import TabsPage from '@/views/TabsPage.vue';
import { useAuthStore } from '@/stores/auth';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: TabsPage,
    children: [
      {
        path: '',
        redirect: '/home'
      },
      {
        path: 'home',
        component: () => import('@/views/HomePage.vue')
      },
      {
        path: 'products',
        component: () => import('@/views/ProductListPage.vue')
      },
      {
        path: 'shops',
        component: () => import('@/views/ShopListPage.vue')
      },
      {
        path: 'material-requests',
        component: () => import('@/views/MaterialRequestsPage.vue')
      },
      {
        path: 'conversations',
        component: () => import('@/views/ConversationListPage.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'my-events',
        redirect: to => {
          return { path: '/events', query: { tab: 'registered' } };
        },
        meta: { requiresAuth: true }
      },
      {
        path: 'bonus',
        component: () => import('@/views/BonusPage.vue')
      },
      {
        path: 'profile',
        component: () => import('@/views/ProfilePage.vue'),
        meta: { requiresAuth: true }
      }
    ]
  },

  // Liked Products
  {
    path: '/liked-products',
    component: () => import('@/views/LikedProductsPage.vue'),
    meta: { requiresAuth: true }
  },

  // Liked Shops
  {
    path: '/liked-shops',
    component: () => import('@/views/LikedShopsPage.vue'),
    meta: { requiresAuth: true }
  },

  // Liked Branches
  {
    path: '/liked-branches',
    component: () => import('@/views/LikedBranchesPage.vue'),
    meta: { requiresAuth: true }
  },

  // Liked Events
  {
    path: '/liked-events',
    component: () => import('@/views/LikedEventsPage.vue'),
    meta: { requiresAuth: true }
  },

  // Liked Users
  {
    path: '/liked-users',
    component: () => import('@/views/LikedUsersPage.vue'),
    meta: { requiresAuth: true }
  },

  // Material Requests (吹雞)
  {
    path: '/material-requests/create',
    component: () => import('@/views/CreateMaterialRequestPage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/material-requests/:requestId',
    component: () => import('@/views/MaterialRequestDetailPage.vue')
  },

  // Chat / Messages
  {
    path: '/chat/:conversationId',
    component: () => import('@/views/ChatroomPage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/chat/new/:otherUserId',
    component: () => import('@/views/ChatroomPage.vue'),
    meta: { requiresAuth: true }
  },

  // User Profile
  {
    path: '/users/:userId',
    component: () => import('@/views/UserProfilePage.vue'),
    meta: {
      requiresAuth: true,
      requiresRole: ['merchant', 'president']
    }
  },

  // Referral Tree
  {
    path: '/referral-tree',
    component: () => import('@/views/ReferralTreePage.vue'),
    meta: { requiresAuth: true }
  },

  // Category Browse
  {
    path: '/categories',
    component: () => import('@/views/CategoryBrowsePage.vue')
  },

  // Shop / Products
  {
    path: '/shops/:shopId',
    component: () => import('@/views/ShopDetailPage.vue')
  },
  {
    path: '/shops/:shopId/edit-profile',
    component: () => import('@/views/EditShopProfilePage.vue'),
    meta: { requiresAuth: true }
  },

  // Branches
  {
    path: '/branches/:branchId',
    component: () => import('@/views/BranchDetailPage.vue')
  },

  {
    path: '/shops/:shopId/products/new',
    component: () => import('@/views/AddProductPage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/shops/:shopId/products/:productId/edit',
    component: () => import('@/views/AddProductPage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/shops/:shopId/products/:productId',
    component: () => import('@/views/ProductDetailPage.vue')
  },

  // Events
  {
    path: '/events/:id',
    component: () => import('@/views/EventDetailPage.vue')
  },
  {
    path: '/events/:id/apply',
    component: () => import('@/views/EventApplicationPage.vue')
  },
  {
    path: '/events/:eventId/application/:applicationId/confirmation',
    component: () => import('@/views/ApplicationConfirmationPage.vue')
  },
  {
    path: '/events/:id/check-in',
    component: () => import('@/views/EventCheckInPage.vue'),
    meta: { requiresAuth: true }
  },

  // Auth
  {
    path: '/login',
    component: () => import('@/views/LoginPage.vue'),
    meta: { redirectIfAuth: true }
  },
  {
    path: '/register',
    component: () => import('@/views/RegisterPage.vue'),
    meta: { redirectIfAuth: true }
  },
  {
    path: '/registration-success',
    component: () => import('@/views/RegistrationSuccessPage.vue'),
    meta: { redirectIfAuth: true }
  },
  {
    path: '/reset-password',
    component: () => import('@/views/ResetPasswordPage.vue')
  },

  // T&C
  {
    path: '/terms-and-conditions',
    component: () => import('@/views/TermsAndConditionsPage.vue')
  },

  // Admin Feedback
  {
    path: '/admin/feedback',
    component: () => import('@/views/AdminFeedbackPage.vue'),
    meta: { requiresAuth: true }
  },

  // My Feedback
  {
    path: '/my-feedback',
    component: () => import('@/views/MyFeedbackPage.vue'),
    meta: { requiresAuth: true }
  },

  // Notification Test (for development)
  {
    path: '/notification-test',
    component: () => import('@/views/NotificationTestPage.vue'),
    meta: { requiresAuth: true }
  },

  // Add catch-all route for SPA
  {
    path: '/:pathMatch(.*)*',
    redirect: '/home'
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
});

// Navigation guard
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();

  // Wait for auth initialization if still loading
  if (authStore.isLoading) {
    // Wait a bit for auth to initialize
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Check if user is authenticated
  if (authStore.isAuthenticated) {
    // Check if there's a stored redirect path (regardless of current route)
    const redirectPath = sessionStorage.getItem('redirectAfterLogin');
    if (redirectPath) {
      sessionStorage.removeItem('redirectAfterLogin');
      console.log('Redirecting to stored path:', redirectPath);
      next(redirectPath);
      return;
    }

    // If trying to access auth pages while logged in, redirect to home
    if (to.path === '/login' || to.path === '/register' || to.path === '/registration-success') {
      next('/home');
      return;
    }
  } else {
    // If not authenticated and trying to access protected route
    if (to.meta.requiresAuth) {
      // Store the intended destination
      console.log('Storing redirect path:', to.fullPath);
      sessionStorage.setItem('redirectAfterLogin', to.fullPath);
      next('/login');
      return;
    }
  }

  next();
});

export default router;