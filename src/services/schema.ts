import { z } from 'zod';

// Common timestamp fields
const timestampFields = {
  created_at: z.string().nullable().transform(val => val ? new Date(val).toISOString() : null),
  updated_at: z.string().nullable().transform(val => val ? new Date(val).toISOString() : null)
};

// User schema - Simplified for construction workers/contractors
export const UserSchema = z.object({
  id: z.string().uuid(),
  username: z.string().min(1),
  full_name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().nullable(),
  role: z.enum(['free', 'merchant', 'president']),
  referrer_id: z.string().uuid().nullable(),
  industry: z.string().nullable(),
  company_name: z.string().nullable(),
  ...timestampFields
});

// Product schema - Adapted for construction materials
export const ProductSchema = z.object({
  id: z.string().uuid(),
  shop_id: z.string().uuid().nullable(),
  title: z.string().min(1),
  description: z.string().nullable(),
  price: z.number().min(0), // Allow 0 for free items
  is_in_stock: z.boolean().default(true),
  profit_sharing_rate: z.number().min(0).max(100),
  cover_image: z.string().nullable(),
  status: z.enum(['active', 'trashed']).default('active'),
  created_by: z.string().nullable(),
  // New construction material fields
  category: z.string().nullable(),
  condition: z.enum(['new', 'used_good', 'used_fair', 'used_poor']).nullable(),
  quantity_available: z.number().default(1),
  unit: z.string().nullable(),
  location_district_id: z.string().uuid().nullable(),
  is_free: z.boolean().default(false),
  pickup_location: z.string().nullable(),
  contact_method: z.enum(['chat', 'phone', 'both']).nullable(),
  ...timestampFields
});

// District schema
export const DistrictSchema = z.object({
  id: z.string().uuid(),
  name_en: z.string().min(1),
  name_zh: z.string().min(1),
  region: z.enum(['Hong Kong Island', 'Kowloon', 'New Territories']),
  sort_order: z.number().default(999),
  created_at: z.string().nullable().transform(val => val ? new Date(val).toISOString() : null)
});

// Material Request schema (吹雞 feature)
export const MaterialRequestSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  title: z.string().min(1),
  description: z.string().min(1),
  category: z.string().nullable(),
  quantity_needed: z.number().nullable(),
  unit: z.string().nullable(),
  max_budget: z.number().min(0).nullable(),
  preferred_condition: z.enum(['new', 'used_good', 'used_fair', 'used_poor', 'any']).nullable(),
  district_id: z.string().uuid().nullable(),
  urgency: z.enum(['low', 'medium', 'high']).default('medium'),
  status: z.enum(['active', 'fulfilled', 'cancelled']).default('active'),
  contact_method: z.enum(['chat', 'phone', 'both']).default('chat'),
  expires_at: z.string().nullable().transform(val => val ? new Date(val).toISOString() : null),
  ...timestampFields
});

// Create Material Request schema
export const CreateMaterialRequestSchema = MaterialRequestSchema.omit({
  id: true,
  created_at: true,
  updated_at: true
});

// Shop schema - Simplified for construction material sellers
export const ShopSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().nullable(),
  logo: z.string().nullable(),
  banner: z.string().nullable(),
  is_featured: z.boolean().default(false),
  notification_emails: z.array(z.string()).nullable(),
  notification_group: z.string().nullable(),
  owner_id: z.string().uuid(),
  // Metrics
  product_count: z.number().default(0),
  like_count: z.number().default(0),
  rating: z.number().default(0),
  rating_count: z.number().default(0),
  // Construction material seller specific fields
  district_id: z.string().uuid().nullable(),
  contact_phone: z.string().nullable(),
  business_hours: z.string().nullable(),
  specialties: z.array(z.string()).nullable(), // e.g., ['steel', 'concrete', 'wood']
  is_verified: z.boolean().default(false),
  ...timestampFields
});

// Material Request Response schema
export const MaterialRequestResponseSchema = z.object({
  id: z.string().uuid(),
  request_id: z.string().uuid(),
  responder_id: z.string().uuid(),
  message: z.string().min(1),
  offered_price: z.number().min(0).nullable(),
  available_quantity: z.number().nullable(),
  contact_info: z.string().nullable(),
  status: z.enum(['pending', 'accepted', 'declined']).default('pending'),
  ...timestampFields
});

// Type exports
export type User = z.infer<typeof UserSchema>;
export type Product = z.infer<typeof ProductSchema>;
export type District = z.infer<typeof DistrictSchema>;
export type MaterialRequest = z.infer<typeof MaterialRequestSchema>;
export type CreateMaterialRequest = z.infer<typeof CreateMaterialRequestSchema>;
export type MaterialRequestResponse = z.infer<typeof MaterialRequestResponseSchema>;
export type Shop = z.infer<typeof ShopSchema>;