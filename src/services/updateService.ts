import { CapacitorUpdater } from '@capgo/capacitor-updater';
import { Capacitor } from '@capacitor/core';

export interface UpdateInfo {
  version: string;
  url: string;
  checksum?: string;
}

export class UpdateService {
  private static instance: UpdateService;
  private isChecking = false;
  private currentVersion = '';
  private lastCheckTime = 0;
  private readonly MIN_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes minimum between checks
  private readonly MAX_DAILY_CHECKS = 10; // Maximum checks per day
  private dailyCheckCount = 0;
  private lastCheckDate = '';

  private constructor() {}

  static getInstance(): UpdateService {
    if (!UpdateService.instance) {
      UpdateService.instance = new UpdateService();
    }
    return UpdateService.instance;
  }

  async initialize(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      console.log('UpdateService: Running on web, updates disabled');
      return;
    }

    try {
      // Get current bundle version (this is the version of the currently running bundle)
      const currentBundle = await CapacitorUpdater.current();
      this.currentVersion = currentBundle.bundle.version;

      console.log('UpdateService: Initialized with current bundle version', this.currentVersion);
      console.log('UpdateService: Current bundle info:', currentBundle);

      // Notify Capacitor Updater that the app has loaded successfully
      // This is required for manual setup to prevent rollback to previous version
      await CapacitorUpdater.notifyAppReady();
      console.log('UpdateService: Notified app ready');

      // Set up listeners (though most won't be used in manual mode)
      await this.setupListeners();

      // Start checking for updates after a short delay
      setTimeout(() => {
        this.checkForUpdates();
      }, 5000); // 5 second delay to let app fully initialize

    } catch (error) {
      console.error('UpdateService: Failed to initialize', error);
    }
  }

  private async setupListeners(): Promise<void> {
    try {
      // In manual mode, we only need a few listeners for debugging/monitoring

      // Listen for download events (for progress tracking)
      await CapacitorUpdater.addListener('download', (info) => {
        console.log('UpdateService: Download progress', info);
      });

      // Listen for update failed
      await CapacitorUpdater.addListener('updateFailed', (info) => {
        console.error('UpdateService: Update failed', info);
      });

      // Note: In manual mode, we don't use updateAvailable, majorAvailable,
      // noNeedUpdate, or downloadComplete listeners since we handle everything manually

    } catch (error) {
      console.error('UpdateService: Failed to setup listeners', error);
    }
  }

  async checkForUpdates(): Promise<void> {
    if (!Capacitor.isNativePlatform() || this.isChecking) {
      return;
    }

    // Rate limiting to prevent infinite loops
    const now = Date.now();
    const today = new Date().toDateString();

    // Reset daily counter if it's a new day
    if (this.lastCheckDate !== today) {
      this.lastCheckDate = today;
      this.dailyCheckCount = 0;
    }

    // Check rate limits
    if (now - this.lastCheckTime < this.MIN_CHECK_INTERVAL) {
      console.log('UpdateService: Rate limited - too soon since last check');
      return;
    }

    if (this.dailyCheckCount >= this.MAX_DAILY_CHECKS) {
      console.log('UpdateService: Rate limited - max daily checks reached');
      return;
    }

    this.isChecking = true;
    this.lastCheckTime = now;
    this.dailyCheckCount++;

    try {
      console.log(`UpdateService: Checking for updates... (${this.dailyCheckCount}/${this.MAX_DAILY_CHECKS} today)`);

      // Check for updates from GitHub releases
      const updateInfo = await this.fetchLatestRelease();

      if (updateInfo && this.isNewerVersion(updateInfo.version)) {
        console.log('UpdateService: New version available:', updateInfo.version);
        await this.downloadAndInstallUpdate(updateInfo);
      } else {
        console.log('UpdateService: App is up to date');
      }

    } catch (error) {
      console.error('UpdateService: Error checking for updates', error);
    } finally {
      this.isChecking = false;
    }
  }

  private async fetchLatestRelease(): Promise<UpdateInfo | null> {
    try {
      const response = await fetch('https://api.github.com/repos/mlolpet/syner-biz-releases/releases/latest');

      if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status}`);
      }

      const release = await response.json();

      // Look for the update bundle asset
      const updateAsset = release.assets.find((asset: any) =>
        asset.name.endsWith('.zip') && asset.name.includes('update')
      );

      if (!updateAsset) {
        console.log('UpdateService: No update bundle found in latest release');
        return null;
      }

      return {
        version: release.tag_name.replace('v', ''), // Remove 'v' prefix if present
        url: updateAsset.browser_download_url,
        checksum: updateAsset.checksum || undefined
      };

    } catch (error) {
      console.error('UpdateService: Failed to fetch latest release', error);
      return null;
    }
  }

  private isNewerVersion(remoteVersion: string): boolean {
    try {
      // Check if we've already processed this version
      const lastProcessedVersion = localStorage.getItem('lastProcessedUpdateVersion');
      if (lastProcessedVersion === remoteVersion) {
        console.log('UpdateService: Version already processed:', remoteVersion);
        return false;
      }

      // Compare against current bundle version (not native app version)
      console.log(`UpdateService: Comparing versions - Current: ${this.currentVersion}, Remote: ${remoteVersion}`);

      // Simple version comparison (assumes semantic versioning)
      const current = this.currentVersion.split('.').map(Number);
      const remote = remoteVersion.split('.').map(Number);

      for (let i = 0; i < Math.max(current.length, remote.length); i++) {
        const currentPart = current[i] || 0;
        const remotePart = remote[i] || 0;

        if (remotePart > currentPart) {
          console.log(`UpdateService: Remote version ${remoteVersion} is newer than current ${this.currentVersion}`);
          return true;
        }
        if (remotePart < currentPart) {
          console.log(`UpdateService: Remote version ${remoteVersion} is older than current ${this.currentVersion}`);
          return false;
        }
      }

      console.log(`UpdateService: Remote version ${remoteVersion} is same as current ${this.currentVersion}`);
      return false;
    } catch (error) {
      console.error('UpdateService: Error comparing versions', error);
      return false;
    }
  }

  private async downloadAndInstallUpdate(updateInfo: UpdateInfo): Promise<void> {
    try {
      console.log('UpdateService: Downloading update from', updateInfo.url);

      // Download the update bundle
      const downloadResult = await CapacitorUpdater.download({
        url: updateInfo.url,
        version: updateInfo.version,
        sessionKey: '',
        checksum: updateInfo.checksum
      });

      console.log('UpdateService: Download result', downloadResult);

      if (downloadResult && downloadResult.version) {
        // Set the new bundle to be used on next app restart
        await CapacitorUpdater.set(downloadResult);
        console.log('UpdateService: Update bundle set successfully. Update will be applied on next app restart.');

        // Mark this version as successfully processed
        localStorage.setItem('lastProcessedUpdateVersion', updateInfo.version);
      } else {
        console.error('UpdateService: Download failed - no bundle received');
      }

    } catch (error) {
      console.error('UpdateService: Failed to download/install update', error);
      // Remove the processed version marker if download failed
      localStorage.removeItem('lastProcessedUpdateVersion');
    }
  }

  async getCurrentVersion(): Promise<string> {
    try {
      // Get the current bundle version (not the native app version)
      const currentBundle = await CapacitorUpdater.current();
      return currentBundle.bundle.version;
    } catch (error) {
      console.error('UpdateService: Failed to get current version', error);
      return '0.0.0';
    }
  }

  async getNativeVersion(): Promise<string> {
    try {
      // Get the native app version (from package.json at build time)
      const builtinVersion = await CapacitorUpdater.getBuiltinVersion();
      return builtinVersion.version;
    } catch (error) {
      console.error('UpdateService: Failed to get native version', error);
      return '0.0.0';
    }
  }

  async forceReload(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      window.location.reload();
      return;
    }

    try {
      await CapacitorUpdater.reload();
    } catch (error) {
      console.error('UpdateService: Failed to reload', error);
    }
  }
}

// Export singleton instance
export const updateService = UpdateService.getInstance();
