import { supabase } from '@/lib/supabase';

export class NotificationService {
  private static instance: NotificationService;
  private processingInterval: number | null = null;
  private isProcessing = false;

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Start processing notification queue periodically
   * This should be called when the app starts and user is authenticated
   */
  startProcessing(intervalMs: number = 30000) { // Default: 30 seconds
    if (this.processingInterval) {
      this.stopProcessing();
    }

    console.log('Starting notification queue processing...');
    
    // Process immediately
    this.processQueue();
    
    // Set up periodic processing
    this.processingInterval = window.setInterval(() => {
      this.processQueue();
    }, intervalMs);
  }

  /**
   * Stop processing notification queue
   */
  stopProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('Stopped notification queue processing');
    }
  }

  /**
   * Process the notification queue once
   */
  private async processQueue() {
    if (this.isProcessing) {
      console.log('Already processing notification queue, skipping...');
      return;
    }

    try {
      this.isProcessing = true;
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/chat-notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({ processQueue: true }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.processed > 0) {
        console.log(`Processed ${result.processed} notifications`);
      }
      
      if (result.errors && result.errors.length > 0) {
        console.warn('Notification processing errors:', result.errors);
      }
    } catch (error) {
      console.error('Error processing notification queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Send a single notification immediately (for testing)
   */
  async sendTestNotification(messageId: string, conversationId: string, senderId: string, content: string, messageType: string = 'text') {
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/chat-notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          messageId,
          conversationId,
          senderId,
          content,
          messageType
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Test notification sent:', result);
      return result;
    } catch (error) {
      console.error('Error sending test notification:', error);
      throw error;
    }
  }

  /**
   * Get notification queue status (for debugging)
   */
  async getQueueStatus() {
    try {
      const { data, error } = await supabase
        .from('chat_notification_queue')
        .select('processed, created_at')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) {
        throw error;
      }

      const total = data?.length || 0;
      const processed = data?.filter(item => item.processed).length || 0;
      const pending = total - processed;

      return {
        total,
        processed,
        pending,
        recentItems: data?.slice(0, 10) || []
      };
    } catch (error) {
      console.error('Error getting queue status:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();
