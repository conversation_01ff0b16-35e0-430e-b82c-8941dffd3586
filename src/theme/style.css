.clamp-text {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

ion-segment {
  flex: 1;
  --background: transparent;
  min-width: 0;
  margin-right: 8px;
}

ion-segment-button {
  --color: var(--ion-color-medium);
  --indicator-color: var(--ion-color-primary);
  min-width: auto;
  font-size: 0.9rem;
}
ion-segment[mode="ios"] ion-segment-button {
  --color-checked: var(--ion-color-light);
}

/* Base styles */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --swiper-wrapper-transition-timing-function: linear !important;
}

/* schedule-x calendar */
button[class*="sx__month-agenda-day"] {
  color: var(--sx-internal-color-text) !important; /* override buttontext browser color */
}
.sx-vue-calendar-wrapper {
  max-width: 100vw;
  height: 800px;
  max-height: 90vh;
}
.sx__month-grid-day__events {
  cursor: pointer;
}

/* Container styles */
.page-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  width: 100%;
  background: var(--ion-background-color);
  border-radius: 0;
}

@media (min-width: 1400px) {
  .page-container {
    border-radius: 24px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  }
}

/* Link styles */
a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

.card {
  padding: 2em;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
}

/* ========================================
   STANDARDIZED IMAGE CLASSES
   ======================================== */

/* Square Image Container - Forces 1:1 aspect ratio */
.square-image-container {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
  background-color: #f5f5f5;
}

.square-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Square Logo - Small circular logos */
.square-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
  flex-shrink: 0;
  background-color: #f5f5f5;
}

/* Square Logo Small - For smaller contexts */
.square-logo-small {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  background-color: #f5f5f5;
}

/* Square Logo Large - For larger contexts */
.square-logo-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
  flex-shrink: 0;
  background-color: #f5f5f5;
}

/* Square Thumbnail - For list items */
.square-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

/* Square Avatar - For user profile pictures */
.square-avatar {
  border-radius: 50%;
  object-fit: cover;
  background-color: #f5f5f5;
}

/* Product Image Container - Specific for product cards */
.product-image-container {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
  background-color: #f5f5f5;
}

.product-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Banner Image Container - For banners that should be wider */
.banner-image-container {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.banner-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Event Image Container - Square for events */
.event-image-container {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 Aspect Ratio */
  overflow: hidden;
  background-color: #f5f5f5;
}

.event-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Hover effects for interactive images */
.square-image-container:hover img,
.product-image-container:hover img,
.banner-image-container:hover img,
.event-image-container:hover img {
  transform: scale(1.05);
}

/* Image upload preview styles */
.image-preview {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
  background-color: #f5f5f5;
}

.logo-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  background-color: #f5f5f5;
}

/* Upload hint text */
.upload-hint {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-top: 0.5rem;
  text-align: center;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .square-logo {
    width: 32px;
    height: 32px;
  }

  .square-logo-large {
    width: 50px;
    height: 50px;
  }

  .square-thumbnail {
    width: 60px;
    height: 60px;
  }

  .banner-image-container {
    height: 120px;
  }
}
</style>