<template>
  <ion-page>
    <ion-header>
      <!-- Search Bar -->
      <ion-toolbar>
        <ion-searchbar
          v-model="searchQuery"
          placeholder="搜尋產品或商店"
          @ionInput="handleSearch"
          class="custom-searchbar"
        ></ion-searchbar>
        <ion-buttons slot="end">
          <ion-button v-if="authStore.isAuthenticated" router-link="/liked-products">
            <ion-icon :icon="heart" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>

      <!-- Category Tabs -->
      <ion-toolbar class="category-toolbar">
        <ion-buttons slot="start">
          <ion-button router-link="/categories" class="browse-categories-btn">
            <ion-icon :icon="gridOutline" slot="start"></ion-icon>
            分類瀏覽
          </ion-button>
        </ion-buttons>
        <ion-segment mode="ios" v-model="selectedCategory" scrollable @ionChange="applyFilters" class="category-segment">
          <ion-segment-button value="" class="category-segment-button">
            <ion-label>全部</ion-label>
          </ion-segment-button>
          <ion-segment-button v-for="category in categories" :key="category.id" :value="category.id.toString()" class="category-segment-button">
            <ion-label>{{ category.title }}</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-toolbar>

      <!-- Compact Filters -->
      <ion-toolbar class="filters-toolbar">
        <div class="filters-container">
          <ion-select
            v-model="priceRange"
            placeholder="價格範圍"
            interface="popover"
            @ionChange="applyFilters"
            class="compact-select"
          >
            <ion-select-option value="">全部價格</ion-select-option>
            <ion-select-option value="0-100">HK$ 0-100</ion-select-option>
            <ion-select-option value="101-500">HK$ 101-500</ion-select-option>
            <ion-select-option value="501-1000">HK$ 501-1000</ion-select-option>
            <ion-select-option value="1001+">HK$ 1000+</ion-select-option>
          </ion-select>

          <ion-select
            v-model="sortBy"
            placeholder="排序"
            interface="popover"
            @ionChange="applyFilters"
            class="compact-select"
          >
            <ion-select-option value="newest">最新上架</ion-select-option>
            <ion-select-option value="price-asc">價格由低至高</ion-select-option>
            <ion-select-option value="price-desc">價格由高至低</ion-select-option>
            <ion-select-option value="popular">最受歡迎</ion-select-option>
          </ion-select>
        </div>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <!-- Products Grid -->
        <div class="products-grid ion-padding">
          <ion-card
            v-for="product in filteredProducts"
            :key="product.id"
            class="product-card"
            button
            :router-link="`/products/${product.id}`"
          >
            <div class="product-image-container">
              <img :src="product.cover_image" :alt="product.title" />
              <div v-if="authStore.isAuthenticated" class="product-actions">
                <ion-button fill="clear" @click.prevent.stop="toggleFavorite(product)" class="action-button">
                  <ion-icon :icon="isProductFavorite(product) ? heart : heartOutline" />
                </ion-button>
              </div>
              <ion-badge
                :color="product.is_in_stock ? 'success' : 'medium'"
                class="stock-badge"
              >
                {{ product.is_in_stock ? '有貨' : '缺貨' }}
              </ion-badge>
              <ion-badge
                v-if="product.category_id"
                color="primary"
                class="category-badge"
              >
                {{ getCategoryName(product.category_id) }}
              </ion-badge>
            </div>
            <ion-card-header>
              <div class="shop-info">
                <img :src="product.shops?.logo" :alt="getShopName(product.shop_id)" class="square-logo-small"
                      v-if="product.shops?.logo">
                <span class="shop-name">{{ getShopName(product.shop_id) }}</span>
              </div>
              <ion-card-title>{{ product.title }}</ion-card-title>
              <div class="price-row">
                <ion-card-subtitle>HK$ {{ product.price }}</ion-card-subtitle>
                <div class="rating" v-if="product.rating">
                  <ion-icon :icon="star" color="warning"></ion-icon>
                  <span>{{ product.rating }}</span>
                </div>
              </div>
            </ion-card-header>
          </ion-card>
        </div>

        <!-- Empty State -->
        <div v-if="!isLoading && filteredProducts.length === 0" class="empty-state ion-padding">
          <ion-icon :icon="searchOutline" color="medium"></ion-icon>
          <p>沒有找到相關產品</p>
        </div>

        <!-- Toast Messages -->
        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonButtons, IonButton,
  IonIcon,
  IonSearchbar,
  IonSelect,
  IonSelectOption,
  IonToast,
  IonBadge,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heartOutline,
  heart,
  searchOutline,
  star,
  gridOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

const route = useRoute();
const authStore = useAuthStore();
const favoriteProducts = ref(new Set<string>());
const products = ref<any[]>([]);
const shops = ref<Map<string, string>>(new Map());
const categories = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');

// Filter states
const searchQuery = ref('');
const selectedCategory = ref('');
const priceRange = ref('');
const sortBy = ref('newest');
const categoryMap = ref(new Map<string, string>());

const loadData = async () => {
  try {
    isLoading.value = true;

    // Load categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*')
      .order('id', { ascending: true });

    if (categoriesError) throw categoriesError;
    categories.value = categoriesData;

    // Create category map for quick lookups
    categoryMap.value = new Map(
      categoriesData.map(c => [c.id.toString(), c.title])
    );

    // Load products with shop information
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        shops (
          id,
          name,
          logo
        )
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (productsError) throw productsError;
    products.value = productsData;

    // Create shops map
    shops.value = new Map(
      productsData
        .filter(p => p.shops)
        .map(p => [p.shop_id, p.shops.name])
    );

    // Load user's liked products if authenticated
    if (authStore.isAuthenticated && authStore.currentUser?.id) {
      const { data: likedData, error: likedError } = await supabase
        .from('user_liked_products')
        .select('product_id')
        .eq('user_id', authStore.currentUser.id);

      if (!likedError && likedData) {
        favoriteProducts.value = new Set(likedData.map(item => item.product_id));
      }
    }
  } catch (error) {
    console.error('Error loading data:', error);
    toastMessage.value = '載入資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
}

// Check for category in URL when component mounts
onMounted(() => {
  if (route.query.category) {
    selectedCategory.value = route.query.category as string;
  }
});

// Watch for route changes
watch(() => route.query.category, (newCategory) => {
  if (newCategory) {
    selectedCategory.value = newCategory as string;
  } else {
    selectedCategory.value = '';
  }
});

// Use Ionic lifecycle hook
onIonViewDidEnter(() => {
  loadData();
});

const getShopName = (shopId: string) => {
  return shops.value.get(shopId) || '未知商店';
};

const getCategoryName = (categoryId: string) => {
  return categoryMap.value.get(categoryId) || '未分類';
};

const filteredProducts = computed(() => {
  let filtered = [...products.value];

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(product =>
      product.title.toLowerCase().includes(query) ||
      getShopName(product.shop_id).toLowerCase().includes(query) ||
      getCategoryName(product.category_id).toLowerCase().includes(query)
    );
  }

  // Apply category filter
  if (selectedCategory.value) {
    filtered = filtered.filter(product => product.category_id === selectedCategory.value);
  }

  // Apply price range filter
  if (priceRange.value) {
    const [min, max] = priceRange.value.split('-').map(v => v === '+' ? Infinity : Number(v));
    filtered = filtered.filter(product => {
      const price = Number(product.price);
      return price >= min && (max === Infinity ? true : price <= max);
    });
  }

  // Apply sorting
  switch (sortBy.value) {
    case 'price-asc':
      filtered.sort((a, b) => a.price - b.price);
      break;
    case 'price-desc':
      filtered.sort((a, b) => b.price - a.price);
      break;
    case 'popular':
      // Sort by number of likes (if we had that data)
      // For now, just use a random order as placeholder
      filtered.sort(() => Math.random() - 0.5);
      break;
    case 'newest':
    default:
      filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      break;
  }

  return filtered;
});

const handleSearch = (event: CustomEvent) => {
  searchQuery.value = event.detail.value;
};

const applyFilters = () => {
  // Filters are automatically applied through the computed property
};

const toggleFavorite = async (product: any) => {
  // Check if user is logged in
  if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
    toastMessage.value = '請先登入以收藏產品';
    return;
  }

  try {
    if (favoriteProducts.value.has(product.id)) {
      // Remove from favorites in database
      const { error } = await supabase
        .from('user_liked_products')
        .delete()
        .eq('user_id', authStore.currentUser.id)
        .eq('product_id', product.id);

      if (error) throw error;

      // Update local state
      favoriteProducts.value.delete(product.id);
      toastMessage.value = '已從收藏移除';
    } else {
      // Add to favorites in database
      const { error } = await supabase
        .from('user_liked_products')
        .insert({
          user_id: authStore.currentUser.id,
          product_id: product.id
        });

      if (error) throw error;

      // Update local state
      favoriteProducts.value.add(product.id);
      toastMessage.value = '已加入收藏';
    }
  } catch (error) {
    console.error('Error toggling favorite:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};

const isProductFavorite = (product: any) => {
  return favoriteProducts.value.has(product.id);
};
</script>

<style scoped>
.custom-searchbar {
  --background: var(--ion-color-light);
  --border-radius: 8px;
  --box-shadow: none;
  --placeholder-color: var(--ion-color-medium);
  padding: 0 8px;
}

.category-toolbar {
  --min-height: 48px;
  --padding-top: 4px;
  --padding-bottom: 4px;
  border-bottom: 1px solid var(--ion-color-light);
}

.category-segment {
  --background: transparent;
}

.browse-categories-btn {
  --color: var(--ion-color-primary);
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.category-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-light);
  --indicator-color: var(--ion-color-primary);
  min-width: 80px;
  font-size: 14px;
}

.filters-toolbar {
  --min-height: 48px;
  --padding-top: 0;
  --padding-bottom: 8px;
}

.filters-container {
  display: flex;
  gap: 8px;
  padding: 0 8px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.compact-select {
  --padding-start: 8px;
  --padding-end: 24px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  min-width: 120px;
  max-width: 160px;
  background: var(--ion-color-light);
  border-radius: 8px;
  font-size: 14px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1rem;
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.product-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* Product image styling now handled by global .product-image-container class */

.product-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 0.25rem;
  opacity: 1;
  transform: translateX(0);
}

.action-button {
  --background: transparent;
  --border-radius: 4px;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  margin: 0;
  width: 36px;
  height: 36px;
}

.action-button ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-primary);
}

.stock-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
}

.category-badge {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

ion-card-header {
  padding: 0.75rem;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Shop logo styling now handled by global .square-logo-small class */

.shop-name {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

ion-card-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

ion-card-subtitle {
  font-size: 1rem;
  color: var(--ion-color-primary);
  font-weight: 600;
  margin: 0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--ion-color-medium);
  font-size: 0.8rem;
}

.rating ion-icon {
  font-size: 0.9rem;
}

.empty-state {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0;
  color: var(--ion-color-medium);
}

/* Hide scrollbar but keep functionality */
.filters-container::-webkit-scrollbar {
  display: none;
}

.filters-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Desktop optimizations */
@media (min-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .filters-container {
    justify-content: center;
  }

  .compact-select {
    min-width: 160px;
  }

  .product-actions {
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
  }

  .product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (min-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 2rem;
    padding: 2rem;
  }
}
</style>