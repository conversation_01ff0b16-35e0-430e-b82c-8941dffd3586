<template>
  <ion-page>
    <ion-tabs>
      <ion-router-outlet></ion-router-outlet>
      <ion-tab-bar color="primary" slot="bottom">
        <ion-tab-button tab="home" href="/home">
          <ion-icon :icon="home" />
          <ion-label>首頁</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="products" href="/products">
          <ion-icon :icon="cart" />
          <ion-label>物料</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="material-requests" href="/material-requests">
          <ion-icon :icon="megaphone" />
          <ion-label>吹雞</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="shops" href="/shops">
          <ion-icon :icon="storefront" />
          <ion-label>商家</ion-label>
        </ion-tab-button>

        <ion-tab-button tab="chat" href="/conversations">
          <ion-icon :icon="chatbubbles" />
          <ion-label>對話</ion-label>
        </ion-tab-button>

        <!--<ion-tab-button tab="bonus" href="/bonus">
          <ion-icon :icon="wallet" />
          <ion-label>奬金</ion-label>
        </ion-tab-button>-->

        <ion-tab-button tab="profile" href="/profile">
          <ion-icon :icon="person" />
          <ion-label>個人</ion-label>
        </ion-tab-button>
      </ion-tab-bar>
    </ion-tabs>
  </ion-page>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import {
  IonTabBar,
  IonTabButton,
  IonTabs,
  IonLabel,
  IonIcon,
  IonPage,
  IonRouterOutlet
} from '@ionic/vue';
import {
  home,
  cart,
  storefront,
  person,
  megaphone,
  chatbubbles,
} from 'ionicons/icons';

// Remove unused route logic for branches/organizations
</script>