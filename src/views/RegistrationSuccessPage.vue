<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>註冊成功</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div class="success-container">
        <ion-icon :icon="checkmarkCircle" class="success-icon"></ion-icon>
        <h2>註冊成功！</h2>
        <p>感謝您註冊成為我們的會員。</p>
        
        <div v-if="selectedRole !== 'free'" class="payment-info">
          <h3>付款資訊</h3>
          <p>請完成支付註冊費用：HK$ {{ paymentAmount }}</p>
          <!-- Add payment instructions or integration here -->
        </div>

        <ion-button expand="block" @click="goToLogin" class="login-button">
          前往登入
        </ion-button>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
} from '@ionic/vue';
import { checkmarkCircle } from 'ionicons/icons';

const router = useRouter();
const selectedRole = computed(() => {
  // Get the role from route query or state management
  return 'free';
});

const paymentAmount = computed(() => {
  switch (selectedRole.value) {
    case 'merchant':
      return 6000;
    case 'president':
      return 12000;
    default:
      return 0;
  }
});

const goToLogin = () => {
  router.push('/login');
};
</script>

<style scoped>
.success-container {
  max-width: 400px;
  margin: 3rem auto;
  text-align: center;
}

.success-icon {
  font-size: 5rem;
  color: var(--ion-color-success);
  margin-bottom: 1rem;
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.payment-info {
  margin: 2rem 0;
  padding: 1rem;
  background: var(--ion-color-light);
  border-radius: 8px;
}

.login-button {
  margin-top: 2rem;
}
</style>