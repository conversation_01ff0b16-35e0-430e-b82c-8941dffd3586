<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/home" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>訊息</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <!-- Loading indicator -->
      <div class="spin ion-text-center" v-if="chatStore.isLoading">
        <ion-spinner></ion-spinner>
      </div>

      <!-- Conversations list -->
      <ion-list v-else-if="chatStore.sortedConversations.length > 0">
        <ion-item
          v-for="conversation in chatStore.sortedConversations"
          :key="conversation.id"
          button
          @click="openConversation(conversation)"
          class="conversation-item"
        >
          <ion-avatar slot="start">
            <img
              :src="conversation.other_participant?.avatar || 'https://ionicframework.com/docs/img/demos/avatar.svg'"
              :alt="conversation.other_participant?.full_name"
            />
          </ion-avatar>

          <ion-label>
            <h2>{{ conversation.other_participant?.full_name || '未知用戶' }}</h2>

            <!-- Product context if available -->
            <div v-if="conversation.last_message?.metadata?.product" class="product-context">
              <div class="product-thumbnail">
                <img
                  :src="conversation.last_message.metadata.product.cover_image || '/placeholder-image.jpg'"
                  :alt="conversation.last_message.metadata.product.title"
                  @error="handleImageError"
                />
              </div>
              <div class="product-info">
                <span class="product-title">{{ conversation.last_message.metadata.product.title }}</span>
                <span class="product-price" v-if="conversation.last_message.metadata.product.price > 0">
                  HK${{ conversation.last_message.metadata.product.price }}
                </span>
                <span class="product-price free" v-else>免費</span>
              </div>
            </div>

            <p v-if="conversation.last_message" class="last-message">
              <!-- Read status tick if last message is from me -->
              <span v-if="isLastMessageFromMe(conversation)" class="message-status">
                <ion-icon
                  v-if="!conversation.last_message.is_read_by_other"
                  :icon="checkmarkOutline"
                  class="tick-sent"
                ></ion-icon>
                <ion-icon
                  v-else
                  :icon="checkmarkDoneOutline"
                  class="tick-read"
                ></ion-icon>
              </span>
              <span v-if="conversation.last_message.message_type === 'image'">
                <ion-icon :icon="imageOutline" size="small"></ion-icon>
                圖片
              </span>
              <span v-else>{{ conversation.last_message.content }}</span>
            </p>
            <p v-else class="last-message">尚未開始對話</p>
          </ion-label>

          <ion-note slot="end" class="conversation-time">
            {{ formatConversationTime(conversation.last_message_at) }}
          </ion-note>
        </ion-item>
      </ion-list>

      <!-- Empty state -->
      <div v-else class="empty-state ion-text-center">
        <ion-icon :icon="chatbubblesOutline" size="large" color="medium"></ion-icon>
        <h3>尚無對話</h3>
        <p>從用戶資料頁面開始新的對話</p>
      </div>
    </ion-content>

    <!-- Toast for notifications -->
    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonList,
  IonItem,
  IonLabel,
  IonAvatar,
  IonNote,
  IonIcon,
  IonSpinner,
  IonToast,
  onIonViewDidEnter
} from '@ionic/vue';
import {
  chatbubblesOutline,
  imageOutline,
  checkmarkOutline,
  checkmarkDoneOutline
} from 'ionicons/icons';
import { useChatStore, type Conversation } from '@/stores/chat';
import { useAuthStore } from '@/stores/auth';
import { format } from 'date-fns';
import { zhTW } from 'date-fns/locale';

const router = useRouter();
const chatStore = useChatStore();
const authStore = useAuthStore();

// Component state
const toastMessage = ref('');

// Load conversations
const loadConversations = async () => {
  try {
    if (!authStore.isAuthenticated) {
      toastMessage.value = '請先登入';
      router.replace('/login');
      return;
    }

    await chatStore.loadConversations();
  } catch (error) {
    console.error('Error loading conversations:', error);
    toastMessage.value = '載入對話列表時發生錯誤';
  }
};

// Open conversation
const openConversation = (conversation: Conversation) => {
  router.push(`/chat/${conversation.id}`);
};

// Check if last message is from current user
const isLastMessageFromMe = (conversation: Conversation): boolean => {
  return conversation.last_message?.sender_id === authStore.currentUser?.id;
};

// Format conversation time
const formatConversationTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    return '剛剛';
  } else if (diffInHours < 24) {
    return format(date, 'HH:mm', { locale: zhTW });
  } else if (diffInHours < 24 * 7) {
    return format(date, 'EEE', { locale: zhTW });
  } else {
    return format(date, 'MM/dd', { locale: zhTW });
  }
};

// Handle image error
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = '/placeholder-image.jpg';
};

onIonViewDidEnter(() => {
  loadConversations();
});
</script>

<style scoped>
.conversation-item {
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 4px;
}

.product-context {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 4px 0;
  padding: 6px;
  background-color: var(--ion-color-light);
  border-radius: 6px;
}

.product-thumbnail {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-title {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: var(--ion-color-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: block;
  font-size: 11px;
  font-weight: 700;
  color: var(--ion-color-primary);
}

.product-price.free {
  color: var(--ion-color-success);
}

.last-message {
  color: var(--ion-color-medium);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  display: flex;
  align-items: center;
}

.message-status {
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
}

.tick-sent {
  color: var(--ion-color-medium);
  font-size: 12px;
}

.tick-read {
  color: var(--ion-color-primary);
  font-size: 12px;
}

.conversation-time {
  font-size: 12px;
  color: var(--ion-color-medium);
}

.empty-state {
  margin-top: 50%;
  transform: translateY(-50%);
}

.empty-state ion-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  color: var(--ion-color-medium);
  margin-bottom: 8px;
}

.empty-state p {
  color: var(--ion-color-medium);
  font-size: 14px;
}

.spin {
  margin-top: 50%;
  transform: translateY(-50%);
}
</style>
