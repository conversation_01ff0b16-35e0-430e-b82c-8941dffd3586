<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <LogoImg className="header-logo" slot="start" />
        <ion-buttons slot="end">
          <template v-if="!authStore.isAuthenticated">
            <ion-button router-link="/login">
              登入
            </ion-button>
            <ion-button router-link="/register" fill="solid">
              註冊
            </ion-button>
          </template>
          <template v-else>
            <ion-text class="user-greeting">
              歡迎，{{ userStore.currentUser?.full_name }}
            </ion-text>

            <!-- Inbox icon with unread count -->
            <ion-button fill="clear" router-link="/conversations">
              <ion-icon :icon="chatbubbleOutline" slot="icon-only"></ion-icon>
              <ion-badge
                v-if="unreadCount > 0"
                color="danger"
                class="inbox-badge"
              >
                {{ unreadCount > 99 ? '99+' : unreadCount }}
              </ion-badge>
            </ion-button>
          </template>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <!-- 吹雞 Section with Green Theme -->
        <div class="chuiji-section">
          <div class="chuiji-header">
            <h2>搵料</h2>
            <ion-searchbar
              placeholder="搜尋..."
              show-clear-button="focus"
              class="custom-searchbar"
            ></ion-searchbar>
          </div>

          <!-- Category Buttons -->
          <div class="category-buttons">
            <div class="category-button" @click="filterByCategory('urgent')">
              <div class="category-icon">
                <ion-icon :icon="flashOutline"></ion-icon>
              </div>
              <span>臨時鐵料</span>
            </div>
            <div class="category-button" @click="filterByCategory('ground')">
              <div class="category-icon">
                <ion-icon :icon="triangleOutline"></ion-icon>
              </div>
              <span>地盤臨時</span>
            </div>
            <div class="category-button" @click="filterByCategory('tools')">
              <div class="category-icon">
                <ion-icon :icon="constructOutline"></ion-icon>
              </div>
              <span>潔具</span>
            </div>
            <div class="category-button" @click="filterByCategory('storage')">
              <div class="category-icon">
                <ion-icon :icon="cubeOutline"></ion-icon>
              </div>
              <span>五金</span>
            </div>
            <div class="category-button" @click="filterByCategory('all')">
              <div class="category-icon">
                <ion-icon :icon="ellipsisHorizontalOutline"></ion-icon>
              </div>
              <span>全部</span>
            </div>
          </div>

          <!-- Hero Banner -->
          <div class="hero-banner-container">
            <img src="@/assets/banner.jpeg" alt="搵料 - 建材交易平台" class="banner-image" />
          </div>
        </div>

        <!-- Material Categories Section -->
        <div class="section-tabs">
          <div class="tab-buttons">
            <ion-button
              :fill="activeTab === 'recommended' ? 'solid' : 'clear'"
              :color="activeTab === 'recommended' ? 'primary' : 'medium'"
              @click="setActiveTab('recommended')"
            >
              推介
            </ion-button>
            <ion-button
              :fill="activeTab === 'free' ? 'solid' : 'clear'"
              :color="activeTab === 'free' ? 'primary' : 'medium'"
              @click="setActiveTab('free')"
            >
              免費
            </ion-button>
          </div>
        </div>

        <!-- Construction Materials Grid -->
        <div v-if="isLoadingProducts" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入建材中...</p>
        </div>

        <div v-else-if="displayedProducts.length > 0" class="materials-grid ion-padding">
          <div class="materials-container">
            <div
              v-for="product in displayedProducts"
              :key="product.id"
              class="material-card"
              @click="navigateToProduct(product)"
            >
              <div class="material-image">
                <img
                  :src="product.cover_image || 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=300'"
                  :alt="product.title"
                  loading="lazy"
                />
              </div>
              <div class="material-info">
                <h4 class="material-title">{{ product.title }}</h4>
                <p class="material-seller">{{ getShopName(product.shop_id) || 'admin' }}</p>
                <div class="material-price">
                  <span v-if="product.is_free" class="free-badge">免費</span>
                  <span v-else class="price">${{ product.price }}</span>
                </div>
                <div class="material-actions">
                  <ion-button fill="clear" size="small" @click.stop="addToWishlist(product)">
                    <ion-icon :icon="heartOutline" slot="start"></ion-icon>
                    加入願望清單
                  </ion-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="empty-state ion-padding">
          <p class="ion-text-center">暫無建材資料</p>
        </div>








      </div>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonContent,
  IonButton,
  IonButtons,
  IonText,
  IonBadge,
  IonSpinner,
  IonToast,
  IonIcon,
  IonSearchbar,
  onIonViewDidEnter,
} from '@ionic/vue';
import LogoImg from '@/components/LogoImg.vue';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { useChatStore } from '@/stores/chat';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'vue-router';
import {
  chatbubbleOutline,
  flashOutline,
  triangleOutline,
  constructOutline,
  cubeOutline,
  ellipsisHorizontalOutline,
  heartOutline
} from 'ionicons/icons';

const authStore = useAuthStore();
const userStore = useUserStore();
const chatStore = useChatStore();
const router = useRouter();

// Construction materials state
const latestProducts = ref<any[]>([]);
const shopNames = ref(new Map<string, string>());
const isLoadingProducts = ref(false);
const toastMessage = ref('');
const activeTab = ref('recommended');
const selectedCategory = ref('all');

// Computed properties
const unreadCount = computed(() => {
  if (!authStore.isAuthenticated || !chatStore.conversations) return 0;
  return chatStore.conversations.filter(conv => {
    return conv.last_message &&
           conv.last_message.sender_id !== authStore.currentUser?.id &&
           !conv.last_message.is_read_by_me;
  }).length;
});

// Computed properties for construction materials
const displayedProducts = computed(() => {
  let filtered = latestProducts.value;

  // Filter by tab (recommended/free)
  if (activeTab.value === 'free') {
    filtered = filtered.filter(product => product.is_free);
  }

  // Filter by category
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(product => product.category === selectedCategory.value);
  }

  return filtered;
});

// Methods
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const filterByCategory = (category: string) => {
  selectedCategory.value = category;
};

const navigateToProduct = (product: any) => {
  router.push(`/products/${product.id}`);
};

const addToWishlist = (product: any) => {
  // TODO: Implement wishlist functionality
  toastMessage.value = '已加入願望清單';
};

const getShopName = (shopId: string) => {
  return shopNames.value.get(shopId) || 'Unknown Shop';
};

onIonViewDidEnter(async () => {
  // Load conversations for unread count if user is authenticated
  if (authStore.isAuthenticated) {
    await chatStore.loadConversations();
  }

  try {
    isLoadingProducts.value = true;

    // Load construction materials with shop info
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        shops (
          id,
          name,
          logo
        )
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(20);

    if (productsError) throw productsError;
    latestProducts.value = products || [];

    // Create shop names map
    shopNames.value = new Map(
      products
        ?.filter(p => p.shops)
        .map(p => [p.shop_id, p.shops.name]) || []
    );

  } catch (error) {
    console.error('Error loading construction materials:', error);
  } finally {
    isLoadingProducts.value = false;
  }
});
</script>

<style scoped>
/* Header */
.header-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-left: 1rem;
}

.user-greeting {
  margin-right: 1rem;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.inbox-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  min-width: 18px;
  height: 18px;
  font-size: 11px;
  font-weight: 600;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 吹雞 Section */
.chuiji-section {
  background: var(--ion-color-primary);
  color: white;
  padding: 0;
}

.chuiji-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.chuiji-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  flex-shrink: 0;
}

.custom-searchbar {
  flex: 1;
  --background: rgba(255, 255, 255, 0.2);
  --color: white;
  --placeholder-color: rgba(255, 255, 255, 0.7);
  --icon-color: white;
  --clear-button-color: white;
}

.category-buttons {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  gap: 8px;
}

.category-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.category-button:active {
  transform: scale(0.95);
}

.category-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.category-button span {
  font-size: 0.8rem;
  text-align: center;
  white-space: nowrap;
}

.hero-banner-container {
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
  max-height: 200px;
  object-fit: cover;
}

/* Section Tabs */
.section-tabs {
  padding: 16px;
  background: white;
}

.tab-buttons {
  display: flex;
  gap: 8px;
}

.tab-buttons ion-button {
  --border-radius: 20px;
  height: 36px;
  font-size: 0.9rem;
}

/* Materials Grid */
.materials-grid {
  background: white;
}

.materials-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.material-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.material-card:active {
  transform: scale(0.98);
}

.material-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.material-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.material-info {
  padding: 12px;
}

.material-title {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.material-seller {
  margin: 0 0 8px 0;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.material-price {
  margin-bottom: 8px;
}

.free-badge {
  background: var(--ion-color-success);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.price {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.material-actions ion-button {
  --color: var(--ion-color-medium);
  font-size: 0.8rem;
  height: 32px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.feature-card {
  margin: 0;
  padding: 1rem;
  text-align: center;
  border-radius: 16px;
  background: var(--ion-color-light);
}

.feature-card ion-icon {
  font-size: 2rem;
  color: var(--ion-color-primary);
  margin-bottom: 0.5rem;
}

.feature-card h3 {
  font-size: 0.9rem;
  margin: 0;
  color: var(--ion-color-dark);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0 1rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0;
}

.view-more-btn {
  font-size: 0.9rem;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  height: 2rem;
  color: var(--ion-color-primary);
}

.latest-shops,
.latest-branches,
.featured-products {
  margin-bottom: 2rem;
}

.shop-card,
.branch-card {
  margin: 0;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.shop-card:hover,
.branch-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.shop-banner,
.branch-banner {
  height: 140px;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}

.shop-banner img,
.branch-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
  will-change: transform;
  max-width: 100%;
}

@media (hover: hover) {
  .shop-card:hover .shop-banner img,
  .branch-card:hover .branch-banner img {
    transform: scale(1.05);
  }
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.shop-logo,
.branch-logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
  background-color: #f5f5f5;
  will-change: transform;
  max-width: 100%;
}

.branch-details {
  flex: 1;
}

.branch-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.member-count,
.district,
.owner-name,
.created-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.branch-activity-level {
  margin-top: 6px;
}

.branch-activity-level span {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.activity-low {
  background-color: rgba(var(--ion-color-danger-rgb), 0.15);
  color: var(--ion-color-danger);
}

.activity-medium {
  background-color: rgba(var(--ion-color-warning-rgb), 0.15);
  color: var(--ion-color-warning-shade);
}

.activity-high {
  background-color: rgba(var(--ion-color-success-rgb), 0.15);
  color: var(--ion-color-success);
}

.activity-unknown {
  background-color: rgba(var(--ion-color-medium-rgb), 0.15);
  color: var(--ion-color-medium);
}

.featured-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--ion-color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 10;
}

ion-card-header {
  padding: 12px 16px;
}

ion-card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.products-section {
  margin-bottom: 2rem;
}

.product-card {
  margin: 0;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
  will-change: transform;
  max-width: 100%;
}

@media (hover: hover) {
  .product-card:hover .product-image img {
    transform: scale(1.05);
  }
}

.stock-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.shop-info:hover {
  background-color: var(--ion-color-light);
}

.price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.event-list ion-thumbnail {
  width: 120px;
  height: 80px;
}

.event-list ion-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.stat-card {
  background: var(--ion-card-background);
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: var(--ion-card-box-shadow);
}

.stat-card ion-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0.5rem 0;
  color: var(--ion-text-color);
}

.stat-card p {
  color: var(--ion-color-medium);
  margin: 0;
}

:root {
  --swiper-theme-color: var(--ion-color-primary);
}

.swiper {
  width: 100%;
  height: 100%;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.swiper-slide {
  height: auto;
  will-change: transform;
  transform: translateZ(0);
}

.loading-placeholder {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--ion-color-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: loading-spin 1s infinite linear;
  opacity: 0;
}

img:not([src]), img[src=""] {
  visibility: hidden;
}

img:not([src]) + .loading-placeholder,
img[src=""] + .loading-placeholder {
  opacity: 1;
}

@keyframes loading-spin {
  100% {
    transform: rotate(360deg);
  }
}

/* Loading and empty states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 16px;
  margin: 0 1rem 1.5rem;
}

.loading-container ion-spinner {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  color: var(--ion-color-primary);
}

.loading-container p {
  color: var(--ion-color-medium);
  margin: 0;
  font-size: 0.9rem;
}

.empty-state {
  padding: 2rem;
  background: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 16px;
  margin: 0 1rem 1.5rem;
}

.empty-state p {
  color: var(--ion-color-medium);
  margin: 0;
  font-size: 0.9rem;
}

.view-more-item {
  --background: transparent;
  --border-color: transparent;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .hero-section {
    height: 40vh;
    min-height: 300px;
  }

  .section-header {
    margin: 1.25rem 0 0.75rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .view-more-btn {
    font-size: 0.8rem;
  }

  .shop-banner {
    height: 140px;
  }

  .shop-logo {
    width: 28px;
    height: 28px;
  }

  .loading-container {
    padding: 1.5rem;
    margin: 0 0.5rem 1rem;
  }

  .empty-state {
    padding: 1.5rem;
    margin: 0 0.5rem 1rem;
  }

  /* Optimize for mobile performance */
  .product-image {
    height: 180px;
  }

  .swiper-slide {
    contain: content;
  }

  .shop-card, .product-card, .branch-card {
    contain: content;
  }

  /* Reduce animation complexity on mobile */
  .shop-card:hover, .product-card:hover, .branch-card:hover {
    transform: none;
  }

  .shop-card:hover .shop-banner img,
  .product-card:hover .product-image img,
  .branch-card:hover .branch-banner img {
    transform: none;
  }
}
</style>