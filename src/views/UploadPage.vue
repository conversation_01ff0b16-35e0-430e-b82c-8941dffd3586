<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/home" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>填寫物件資料</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <form @submit.prevent="handleSubmit" class="upload-form">
          <ion-list>
            <!-- Category -->
            <ion-item>
              <ion-label position="stacked">分類 *</ion-label>
              <ion-select
                v-model="formData.category_id"
                interface="popover"
                placeholder="請選擇分類"
                required
              >
                <ion-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.title }}
                </ion-select-option>
              </ion-select>
            </ion-item>

            <!-- Condition (Sub-category) -->
            <ion-item>
              <ion-label position="stacked">狀態 *</ion-label>
              <ion-select
                v-model="formData.condition"
                interface="popover"
                placeholder="請選擇狀態"
                required
              >
                <ion-select-option value="new">New</ion-select-option>
                <ion-select-option value="like-new">Like New</ion-select-option>
                <ion-select-option value="good">Good</ion-select-option>
                <ion-select-option value="fair">Fair</ion-select-option>
                <ion-select-option value="poor">Poor</ion-select-option>
              </ion-select>
            </ion-item>

            <!-- Product Title -->
            <ion-item>
              <ion-label position="stacked">物件名稱 *</ion-label>
              <ion-input
                v-model="formData.title"
                type="text"
                required
                placeholder="物件名稱"
              ></ion-input>
            </ion-item>

            <!-- Brand -->
            <ion-item>
              <ion-label position="stacked">品牌 (如有)</ion-label>
              <ion-input
                v-model="formData.brand"
                type="text"
                placeholder="物件品牌"
              ></ion-input>
            </ion-item>

            <!-- Rich Text Description -->
            <div class="editor-container">
              <ion-label position="stacked">簡介 *</ion-label>
              <div id="editor"></div>
            </div>

            <!-- Quantity -->
            <ion-item>
              <ion-label position="stacked">數量 (如有)</ion-label>
              <ion-input
                v-model="formData.quantity"
                type="number"
                min="1"
                placeholder="輸入數量 (如有)"
              ></ion-input>
            </ion-item>

            <!-- For Sale / Free Toggle -->
            <div class="price-section">
              <ion-label position="stacked">物件是否免費？</ion-label>
              <ion-radio-group v-model="formData.is_free">
                <ion-item>
                  <ion-radio slot="start" :value="false"></ion-radio>
                  <ion-label>For Sale</ion-label>
                </ion-item>
                <ion-item>
                  <ion-radio slot="start" :value="true"></ion-radio>
                  <ion-label>Free</ion-label>
                </ion-item>
              </ion-radio-group>
            </div>

            <!-- Price (only show if not free) -->
            <ion-item v-if="!formData.is_free">
              <ion-label position="stacked">價錢</ion-label>
              <ion-input
                v-model="formData.price"
                type="number"
                min="0"
                step="0.01"
                placeholder="輸入價錢 (HKD)"
                required
              ></ion-input>
            </ion-item>
          </ion-list>

          <!-- Photo Upload Section -->
          <div class="section-title ion-padding-top">
            <h2>物件相片</h2>
            <ion-button size="small" fill="clear" @click="addProductPhoto">
              <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
              新增相片
            </ion-button>
          </div>
          <p class="upload-hint">建議上傳正方形圖片以獲得最佳顯示效果</p>

          <ion-list class="photo-list">
            <ion-reorder-group @ionItemReorder="handleReorder($event)" :disabled="false">
              <!-- Product Photos -->
              <ion-item v-for="(photo, index) in productPhotos" :key="index" class="photo-item">
                <ion-thumbnail slot="start">
                  <img :src="photo.preview" :alt="`Product photo ${index + 1}`" class="square-thumbnail" />
                </ion-thumbnail>
                <ion-label>
                  <div class="photo-info">
                    <ion-input
                      v-model="photo.caption"
                      placeholder="輸入相片說明"
                      class="caption-input"
                    ></ion-input>
                  </div>
                </ion-label>
                <ion-button
                  fill="clear"
                  color="danger"
                  slot="end"
                  @click="removePhoto(index)"
                >
                  <ion-icon :icon="trashOutline"></ion-icon>
                </ion-button>
                <ion-reorder slot="end">
                  <ion-icon :icon="menuOutline"></ion-icon>
                </ion-reorder>
              </ion-item>
            </ion-reorder-group>
          </ion-list>

          <!-- Submit Button -->
          <div class="submit-section">
            <ion-button
              expand="block"
              type="submit"
              :disabled="isSubmitting"
              color="primary"
            >
              <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
              <span v-else>發佈物料</span>
            </ion-button>
          </div>
        </form>
      </div>

      <!-- Success Toast -->
      <ion-toast
        :is-open="showSuccessToast"
        message="物料發佈成功！"
        duration="3000"
        color="success"
        @didDismiss="showSuccessToast = false"
      ></ion-toast>

      <!-- Error Toast -->
      <ion-toast
        :is-open="showErrorToast"
        :message="errorMessage"
        duration="5000"
        color="danger"
        @didDismiss="showErrorToast = false"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonButtons,
  IonBackButton,
  IonSpinner,
  IonToast,
  IonRadioGroup,
  IonRadio,
  IonThumbnail,
  IonReorderGroup,
  IonReorder,
  IonIcon,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  cameraOutline,
  trashOutline,
  menuOutline,
} from 'ionicons/icons';
import Quill from 'quill';
import "quill/dist/quill.snow.css";
import QuillResize from 'quill-resize-module';
Quill.register('modules/resize', QuillResize);

import LoadingSpinner from '@/components/LoadingSpinner.vue';
import { useAuthStore } from '@/stores/auth';
import { supabase } from '@/lib/supabase';
import { uploadImageToSupabase } from '@/lib/supabase-storage';
import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';

interface ProductPhoto {
  base64Data: string;
  preview: string;
  caption: string;
}

const router = useRouter();
const authStore = useAuthStore();

// Initialize photo gallery
const { takePhoto } = usePhotoGallery();

// Form data
const formData = ref({
  title: '',
  description: '',
  category_id: null as number | null,
  condition: '',
  brand: '',
  quantity: 1,
  is_free: false,
  price: 0,
});

// State
const isLoading = ref(false);
const isSubmitting = ref(false);
const categories = ref<any[]>([]);
const userShop = ref<any>(null);
const productPhotos = ref<ProductPhoto[]>([]);
const showSuccessToast = ref(false);
const showErrorToast = ref(false);
const errorMessage = ref('');

// Quill editor instance
let quillEditor: Quill | null = null;

// Load categories and user's shop
onMounted(async () => {
  isLoading.value = true;
  try {
    // Load categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*')
      .order('title');

    if (categoriesError) throw categoriesError;
    categories.value = categoriesData || [];

    // Load user's shop (auto-created)
    const { data: shopData, error: shopError } = await supabase
      .from('shops')
      .select('*')
      .eq('owner_id', authStore.currentUser?.id)
      .single();

    if (shopError) throw shopError;
    userShop.value = shopData;

  } catch (error: any) {
    console.error('Error loading data:', error);
    errorMessage.value = '載入資料失敗';
    showErrorToast.value = true;
  } finally {
    isLoading.value = false;
  }
});

// Initialize Quill editor after view enters
onIonViewDidEnter(() => {
  setTimeout(() => {
    initializeQuillEditor();
  }, 100);
});

// Initialize Quill editor
const initializeQuillEditor = () => {
  if (quillEditor) {
    return; // Already initialized
  }

  quillEditor = new Quill('#editor', {
    theme: 'snow',
    placeholder: '請輸入物件簡介...',
    modules: {
      resize: {},
      toolbar: {
        container: [
          ['bold', 'italic', 'underline'],
          [{ 'header': [1, 2, false] }],
          ['link', 'blockquote', 'code-block'],
          [{ list: 'ordered' }, { list: 'bullet' }],
          ['clean']
        ]
      }
    }
  });

  // Listen for content changes and update formData
  quillEditor.on('text-change', () => {
    formData.value.description = quillEditor?.root.innerHTML || '';
  });

  console.log('Quill editor initialized');
};

// Add product photo
const addProductPhoto = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      productPhotos.value.push({
        base64Data: photo.base64Data || '',
        preview: photo.base64Data || '',
        caption: ''
      });
    }
  } catch (error) {
    console.error('Error taking product photo:', error);
    errorMessage.value = '無法獲取相片，請稍後再試';
    showErrorToast.value = true;
  }
};

// Remove photo
const removePhoto = (index: number) => {
  const photo = productPhotos.value[index];
  URL.revokeObjectURL(photo.preview);
  productPhotos.value.splice(index, 1);
};

// Handle photo reordering
const handleReorder = (event: CustomEvent) => {
  const { from, to } = event.detail;
  const itemMove = productPhotos.value.splice(from, 1)[0];
  productPhotos.value.splice(to, 0, itemMove);
  event.detail.complete();
};

// Handle form submission
const handleSubmit = async () => {
  if (!userShop.value) {
    errorMessage.value = '找不到您的商店資訊';
    showErrorToast.value = true;
    return;
  }

  isSubmitting.value = true;
  try {
    // Create product
    const productData = {
      shop_id: userShop.value.id,
      title: formData.value.title,
      description: formData.value.description,
      category_id: formData.value.category_id,
      price: formData.value.is_free ? 0 : (formData.value.price || 0),
      is_in_stock: true,
      profit_sharing_rate: 0,
      status: 'active',
    };

    const { data: product, error: productError } = await supabase
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (productError) throw productError;

    // Upload product photos if any
    if (productPhotos.value.length > 0) {
      console.log(`Uploading ${productPhotos.value.length} product photos`);

      try {
        // Upload photos and create records
        const photoInserts = productPhotos.value.map(async (photo, index) => {
          if (!photo.base64Data) return;

          // Upload image to Supabase storage
          const uploadedUrl = await uploadImageToSupabase({
            base64Data: photo.base64Data,
            filename: `product_${product.id}_${Date.now()}_${index}.jpg`,
            mimeType: 'image/jpeg'
          }, 'products', 'photos');

          // Insert photo record
          return supabase
            .from('product_photos')
            .insert({
              product_id: product.id,
              photo_url: uploadedUrl,
              caption: photo.caption,
              order: index
            });
        });

        await Promise.all(photoInserts);
        console.log('Photo records inserted successfully');
      } catch (photoError) {
        console.error('Error uploading photos:', photoError);
        errorMessage.value = '物件相片上傳失敗，但物件基本資料已保存';
        showErrorToast.value = true;
      }
    }

    showSuccessToast.value = true;

    // Reset form
    formData.value = {
      title: '',
      description: '',
      category_id: null,
      condition: '',
      brand: '',
      quantity: 1,
      is_free: false,
      price: 0,
    };
    productPhotos.value = [];

    // Clear Quill editor
    if (quillEditor) {
      quillEditor.root.innerHTML = '';
    }

    // Navigate to home after success
    setTimeout(() => {
      router.push('/home');
    }, 2000);

  } catch (error: any) {
    console.error('Error creating product:', error);
    errorMessage.value = error.message || '發佈失敗，請重試';
    showErrorToast.value = true;
  } finally {
    isSubmitting.value = false;
  }
};

// Cleanup Quill editor when component unmounts
onUnmounted(() => {
  if (quillEditor) {
    quillEditor = null;
  }
});
</script>

<style scoped>
.upload-form {
  max-width: 800px;
  margin: 0 auto;
}

.editor-container {
  margin: 16px;
}

.editor-container ion-label {
  margin-bottom: 8px;
  display: block;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.price-section {
  margin: 16px;
}

.price-section ion-label {
  margin-bottom: 8px;
  display: block;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px;
}

.section-title h2 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--ion-color-dark);
}

.upload-hint {
  margin: 8px 16px;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.photo-list {
  margin: 0 16px;
}

.photo-item {
  margin-bottom: 8px;
}

.square-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.photo-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.caption-input {
  font-size: 0.9rem;
}

.submit-section {
  margin: 24px 16px 16px;
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
}

:deep(.ql-editor) {
  min-height: 150px;
  max-height: 300px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .upload-form {
    margin: 0;
  }

  .editor-container {
    margin: 12px;
  }

  .section-title {
    margin: 12px;
  }

  .photo-list {
    margin: 0 12px;
  }

  .submit-section {
    margin: 20px 12px 12px;
  }

  :deep(.ql-container) {
    min-height: 120px;
    max-height: 250px;
  }

  :deep(.ql-editor) {
    min-height: 120px;
    max-height: 250px;
  }

  ion-thumbnail {
    --size: 60px;
  }
}
</style>
