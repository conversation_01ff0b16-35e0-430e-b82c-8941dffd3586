<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>上傳物料</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <form @submit.prevent="handleSubmit" class="upload-form">
          <ion-list>
            <!-- Product Title -->
            <ion-item>
              <ion-label position="stacked">物料名稱 *</ion-label>
              <ion-input
                v-model="formData.title"
                type="text"
                required
                placeholder="請輸入物料名稱"
              ></ion-input>
            </ion-item>

            <!-- Category -->
            <ion-item>
              <ion-label position="stacked">物料類別 *</ion-label>
              <ion-select
                v-model="formData.category_id"
                interface="popover"
                placeholder="請選擇物料類別"
                required
              >
                <ion-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.title }}
                </ion-select-option>
              </ion-select>
            </ion-item>

            <!-- Description -->
            <ion-item>
              <ion-label position="stacked">物料描述 *</ion-label>
              <ion-textarea
                v-model="formData.description"
                placeholder="請描述物料的狀況、用途等詳細資訊"
                rows="4"
                required
              ></ion-textarea>
            </ion-item>

            <!-- Price -->
            <ion-item>
              <ion-label position="stacked">價格 (HKD)</ion-label>
              <ion-input
                v-model="formData.price"
                type="number"
                min="0"
                step="0.01"
                placeholder="0 = 免費"
              ></ion-input>
              <ion-note slot="helper">輸入 0 表示免費提供</ion-note>
            </ion-item>

            <!-- Photo Upload -->
            <ion-item>
              <ion-label position="stacked">物料照片</ion-label>
              <input
                type="file"
                accept="image/*"
                @change="handleImageUpload"
                class="file-input"
              />
            </ion-item>

            <!-- Preview uploaded image -->
            <div v-if="imagePreview" class="image-preview">
              <img :src="imagePreview" alt="Preview" />
            </div>

            <!-- Submit Button -->
            <div class="submit-section">
              <ion-button
                expand="block"
                type="submit"
                :disabled="isSubmitting"
                color="primary"
              >
                <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
                <span v-else>發佈物料</span>
              </ion-button>
            </div>
          </ion-list>
        </form>
      </div>

      <!-- Success Toast -->
      <ion-toast
        :is-open="showSuccessToast"
        message="物料發佈成功！"
        duration="3000"
        color="success"
        @didDismiss="showSuccessToast = false"
      ></ion-toast>

      <!-- Error Toast -->
      <ion-toast
        :is-open="showErrorToast"
        :message="errorMessage"
        duration="5000"
        color="danger"
        @didDismiss="showErrorToast = false"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonSpinner,
  IonToast,
  IonNote,
} from '@ionic/vue';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import { useAuthStore } from '@/stores/auth';
import { supabase } from '@/lib/supabase';

const router = useRouter();
const authStore = useAuthStore();

// Form data
const formData = ref({
  title: '',
  description: '',
  category_id: null as number | null,
  price: 0,
});

// State
const isLoading = ref(false);
const isSubmitting = ref(false);
const categories = ref<any[]>([]);
const userShop = ref<any>(null);
const imagePreview = ref<string | null>(null);
const selectedFile = ref<File | null>(null);
const showSuccessToast = ref(false);
const showErrorToast = ref(false);
const errorMessage = ref('');

// Load categories and user's shop
onMounted(async () => {
  isLoading.value = true;
  try {
    // Load categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*')
      .order('title');

    if (categoriesError) throw categoriesError;
    categories.value = categoriesData || [];

    // Load user's shop (auto-created)
    const { data: shopData, error: shopError } = await supabase
      .from('shops')
      .select('*')
      .eq('owner_id', authStore.currentUser?.id)
      .single();

    if (shopError) throw shopError;
    userShop.value = shopData;

  } catch (error: any) {
    console.error('Error loading data:', error);
    errorMessage.value = '載入資料失敗';
    showErrorToast.value = true;
  } finally {
    isLoading.value = false;
  }
});

// Handle image upload
const handleImageUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  
  if (file) {
    selectedFile.value = file;
    const reader = new FileReader();
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
};

// Handle form submission
const handleSubmit = async () => {
  if (!userShop.value) {
    errorMessage.value = '找不到您的商店資訊';
    showErrorToast.value = true;
    return;
  }

  isSubmitting.value = true;
  try {
    // Create product
    const productData = {
      shop_id: userShop.value.id,
      title: formData.value.title,
      description: formData.value.description,
      category_id: formData.value.category_id,
      price: formData.value.price || 0,
      is_in_stock: true,
      profit_sharing_rate: 0,
      status: 'active',
    };

    const { data: product, error: productError } = await supabase
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (productError) throw productError;

    // TODO: Handle image upload to storage if needed
    // For now, we'll skip image upload to keep it simple

    showSuccessToast.value = true;
    
    // Reset form
    formData.value = {
      title: '',
      description: '',
      category_id: null,
      price: 0,
    };
    imagePreview.value = null;
    selectedFile.value = null;

    // Navigate to home after success
    setTimeout(() => {
      router.push('/home');
    }, 2000);

  } catch (error: any) {
    console.error('Error creating product:', error);
    errorMessage.value = error.message || '發佈失敗，請重試';
    showErrorToast.value = true;
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.upload-form {
  max-width: 600px;
  margin: 0 auto;
}

.file-input {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--ion-color-medium);
  border-radius: 8px;
  margin-top: 8px;
}

.image-preview {
  margin: 16px 0;
  text-align: center;
}

.image-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.submit-section {
  margin-top: 24px;
  padding: 16px 0;
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
}
</style>
