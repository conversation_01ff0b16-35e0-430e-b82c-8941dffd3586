<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>吹雞</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="openDistrictFilter">
            <ion-icon :icon="locationOutline" slot="start"></ion-icon>
            {{ selectedDistrict?.name_zh || '全港' }}
            <ion-icon :icon="chevronDownOutline" slot="end"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <!-- User's own requests section -->
      <div v-if="authStore.isAuthenticated" class="user-requests-section">
        <div class="section-header">
          <h3>我的請求</h3>
          <ion-button fill="clear" size="small" @click="createNewRequest">
            <ion-icon :icon="addOutline" slot="start"></ion-icon>
            新增
          </ion-button>
        </div>
        
        <div v-if="userRequests.length === 0" class="empty-state">
          <p>You don't have any requests.</p>
          <ion-button @click="createNewRequest" fill="outline" color="primary">
            建立第一個請求
          </ion-button>
        </div>
        
        <div v-else class="user-requests-list">
          <ion-card v-for="request in userRequests" :key="request.id" class="request-card user-request">
            <ion-card-content>
              <div class="request-header">
                <h4>{{ request.title }}</h4>
                <ion-badge :color="getStatusColor(request.status)">
                  {{ getStatusText(request.status) }}
                </ion-badge>
              </div>
              <p class="request-description">{{ request.description }}</p>
              <div class="request-meta">
                <span class="district">{{ getDistrictName(request.district_id) }}</span>
                <span class="category">{{ request.category }}</span>
                <span class="budget" v-if="request.max_budget">預算: ${{ request.max_budget }}</span>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </div>

      <!-- Other users' requests section -->
      <div class="other-requests-section">
        <div class="section-header">
          <h3>其他請求</h3>
        </div>
        
        <div v-if="isLoading" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入請求中...</p>
        </div>
        
        <div v-else-if="otherRequests.length === 0" class="empty-state">
          <p>暫無其他請求</p>
        </div>
        
        <div v-else class="other-requests-list">
          <ion-card 
            v-for="request in otherRequests" 
            :key="request.id" 
            class="request-card other-request"
            button
            @click="viewRequestDetail(request)"
          >
            <ion-card-content>
              <div class="request-header">
                <div class="request-image">
                  <ion-icon :icon="constructOutline" size="large"></ion-icon>
                </div>
                <div class="request-info">
                  <h4>{{ request.title }}</h4>
                  <p class="district">{{ getDistrictName(request.district_id) }}</p>
                  <p class="user-code">{{ getUserCode(request.user_id) }}</p>
                </div>
                <ion-button fill="clear" size="small">
                  <ion-icon :icon="chatbubbleOutline"></ion-icon>
                </ion-button>
              </div>
            </ion-card-content>
          </ion-card>
        </div>
      </div>

      <!-- Floating Action Button -->
      <ion-fab vertical="bottom" horizontal="end" slot="fixed">
        <ion-fab-button @click="createNewRequest" color="primary">
          <ion-icon :icon="addOutline"></ion-icon>
        </ion-fab-button>
      </ion-fab>
    </ion-content>

    <!-- District Filter Modal -->
    <ion-modal :is-open="isDistrictModalOpen" @did-dismiss="closeDistrictFilter">
      <ion-header>
        <ion-toolbar>
          <ion-title>選擇地區</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="closeDistrictFilter">關閉</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <ion-list>
          <ion-item button @click="selectDistrict(null)">
            <ion-label>全港</ion-label>
            <ion-radio-group v-model="selectedDistrictId" :value="null"></ion-radio-group>
          </ion-item>
          <ion-item 
            v-for="district in districts" 
            :key="district.id" 
            button 
            @click="selectDistrict(district)"
          >
            <ion-label>{{ district.name_zh }}</ion-label>
            <ion-radio-group v-model="selectedDistrictId" :value="district.id"></ion-radio-group>
          </ion-item>
        </ion-list>
      </ion-content>
    </ion-modal>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonCard,
  IonCardContent,
  IonBadge,
  IonSpinner,
  IonFab,
  IonFabButton,
  IonModal,
  IonList,
  IonItem,
  IonLabel,
  IonRadioGroup,
} from '@ionic/vue';
import {
  locationOutline,
  chevronDownOutline,
  addOutline,
  constructOutline,
  chatbubbleOutline,
} from 'ionicons/icons';
import { useAuthStore } from '@/stores/auth';
import { useMaterialRequestsStore } from '@/stores/materialRequests';
import { useDistrictsStore } from '@/stores/districts';

const router = useRouter();
const authStore = useAuthStore();
const materialRequestsStore = useMaterialRequestsStore();
const districtsStore = useDistrictsStore();

const isLoading = ref(false);
const isDistrictModalOpen = ref(false);
const selectedDistrictId = ref<string | null>(null);

const selectedDistrict = computed(() => 
  selectedDistrictId.value ? districtsStore.getDistrictById(selectedDistrictId.value) : null
);

const districts = computed(() => districtsStore.districts);
const userRequests = computed(() => materialRequestsStore.userRequests);
const otherRequests = computed(() => {
  const filtered = materialRequestsStore.activeRequests.filter(
    request => request.user_id !== authStore.user?.id
  );
  
  if (selectedDistrictId.value) {
    return filtered.filter(request => request.district_id === selectedDistrictId.value);
  }
  
  return filtered;
});

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'fulfilled': return 'medium';
    case 'cancelled': return 'danger';
    default: return 'medium';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '進行中';
    case 'fulfilled': return '已完成';
    case 'cancelled': return '已取消';
    default: return status;
  }
};

const getDistrictName = (districtId: string | null) => {
  if (!districtId) return '未指定';
  const district = districtsStore.getDistrictById(districtId);
  return district?.name_zh || '未知地區';
};

const getUserCode = (userId: string) => {
  // Generate a simple user code like "DK123"
  return `DK${userId.slice(-3).toUpperCase()}`;
};

const openDistrictFilter = () => {
  isDistrictModalOpen.value = true;
};

const closeDistrictFilter = () => {
  isDistrictModalOpen.value = false;
};

const selectDistrict = (district: any) => {
  selectedDistrictId.value = district?.id || null;
  closeDistrictFilter();
};

const createNewRequest = () => {
  router.push('/material-requests/create');
};

const viewRequestDetail = (request: any) => {
  router.push(`/material-requests/${request.id}`);
};

onMounted(async () => {
  isLoading.value = true;
  try {
    await Promise.all([
      districtsStore.fetchDistricts(),
      materialRequestsStore.fetchMaterialRequests(),
      authStore.isAuthenticated ? materialRequestsStore.fetchUserMaterialRequests() : Promise.resolve()
    ]);
  } catch (error) {
    console.error('Error loading material requests:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 8px;
}

.section-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.user-requests-section {
  background: #f8f9fa;
  margin-bottom: 16px;
}

.request-card {
  margin: 8px 16px;
  border-radius: 12px;
}

.user-request {
  border-left: 4px solid var(--ion-color-primary);
}

.other-request {
  background: var(--ion-color-primary);
  color: white;
}

.other-request ion-card-content {
  color: white;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.other-request .request-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.request-image {
  flex-shrink: 0;
}

.request-info {
  flex: 1;
}

.request-info h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
}

.request-info p {
  margin: 2px 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.request-description {
  margin: 8px 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.request-meta {
  display: flex;
  gap: 12px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: var(--ion-color-medium);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  gap: 16px;
}
</style>
