<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-button v-if="isPreviewMode" @click="exitPreview" class="preview-exit">
            <ion-icon :icon="closeCircleOutline" slot="start"></ion-icon>
            退出預覽
          </ion-button>
          <ion-back-button v-else default-href="/branches" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ branch?.name || '分會' }}</ion-title>
        <ion-buttons slot="end" v-if="isOwner">
          <ion-button @click="previewBranch" title="預覽分會">
            <ion-icon :icon="eyeOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button @click="showEditModal = true">
            <ion-icon :icon="createOutline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="branch" class="branch-container">
        <!-- Branch Banner -->
        <div class="branch-banner">
          <img :src="branch.banner || 'https://images.unsplash.com/photo-1517048676732-d65bc937f952'" :alt="branch.name">
          <div class="branch-info-overlay">
            <div class="branch-logo">
              <img :src="branch.logo || 'https://images.unsplash.com/photo-1522071820081-009f0129c71c'" :alt="branch.name">
            </div>
            <div class="branch-details">
              <h1>{{ branch.name }}</h1>
              <p class="branch-description clamp-text">{{ branch.description }}</p>
              <div class="branch-meta">
                <div class="meta-item">
                  <ion-icon :icon="peopleOutline"></ion-icon>
                  <span>{{ members.length }} 位成員</span>
                </div>
                <div class="meta-item">
                  <ion-icon :icon="locationOutline"></ion-icon>
                  <span>{{ branch.district }}</span>
                </div>
                <div class="meta-item">
                  <ion-icon :icon="timeOutline"></ion-icon>
                  <span>{{ formatDate(branch.created_at) }} 創建</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Branch Owner Information -->
        <div class="branch-owner-section">
          <ion-item lines="none" class="branch-owner-item">
            <ion-icon :icon="personOutline" slot="start" class="owner-icon"></ion-icon>
            <ion-label>
              <h3>分會長</h3>
              <h2>{{ branchOwnerName }}</h2>
            </ion-label>
          </ion-item>
        </div>

        <div v-if="!isOwner && !isMember && !hasApplied" class="join-section">
          <ion-button expand="block" @click="handleJoinButtonClick">
            申請加入分會
            <ion-icon :icon="personAddOutline" slot="end"></ion-icon>
          </ion-button>
        </div>
        <div v-else-if="hasApplied" class="join-section">
          <ion-note color="medium">您的申請正在審核中</ion-note>
        </div>

        <!-- Members Tab -->
        <div>
          <div class="section">
            <h2>分會成員 ({{ members.length }})</h2>

            <!-- Pending Applications Section (Only visible to branch owner) -->
            <div v-if="isOwner && pendingApplications.length > 0" class="section applications-section">
              <h2>待審核申請 ({{ pendingApplications.length }})</h2>
              <div class="applications-list">
                <ion-card v-for="application in pendingApplications" :key="application.id" class="application-card">
                  <ion-card-header>
                    <ion-card-title>{{ application.users?.full_name }}</ion-card-title>
                    <ion-card-subtitle>{{ formatDate(application.created_at) }} 申請</ion-card-subtitle>
                  </ion-card-header>
                  <ion-card-content>
                    <p v-if="application.message">{{ application.message }}</p>
                    <p v-else class="empty-text">無申請訊息</p>
                    <div class="application-actions">
                      <ion-button color="success" @click="showApproveConfirm(application)">
                        <ion-icon :icon="checkmarkOutline" slot="start"></ion-icon>
                        批准
                      </ion-button>
                      <ion-button color="danger" fill="outline" @click="showRejectConfirm(application)">
                        <ion-icon :icon="closeOutline" slot="start"></ion-icon>
                        拒絕
                      </ion-button>
                    </div>
                  </ion-card-content>
                </ion-card>
              </div>
            </div>

            <!-- Members List Section (Ultra Compact View) -->
            <div v-if="members.length > 0" class="members-list ultra-compact">
              <ion-item v-for="member in displayedMembers" :key="member.id" lines="full" :class="{'owner-highlight': isOwnerMember(member)}" button @click="navigateToUserProfile(member.user_id)">
                <ion-avatar slot="start" class="small-avatar">
                  <img src="https://ionicframework.com/docs/img/demos/avatar.svg" :alt="member.users?.full_name">
                </ion-avatar>
                <ion-label>
                  <div class="compact-member-info">
                    <span class="member-name">
                      {{ member.users?.full_name }}
                      <ion-badge v-if="isOwnerMember(member)" color="primary" class="small-badge">分會長</ion-badge>
                    </span>
                    <div class="member-details">
                      <span v-if="member.users?.industry" class="industry">{{ member.users?.industry }}</span>
                      <span class="join-date">{{ formatCompactDate(member.joined_at) }}</span>
                    </div>
                  </div>
                </ion-label>
                <ion-buttons slot="end" class="action-buttons">
                  <ion-button fill="clear" color="primary" size="small" title="查看資料" @click.stop="navigateToUserProfile(member.user_id)">
                    <ion-icon :icon="personOutline" slot="icon-only"></ion-icon>
                  </ion-button>
                </ion-buttons>
              </ion-item>

              <!-- View All Members Button (only if there are more members than shown) -->
              <div v-if="members.length > maxDisplayedMembers" class="view-all-container">
                <ion-button expand="block" fill="clear" @click="showMembersModal = true">
                  查看全部 {{ members.length }} 位成員
                  <ion-icon :icon="peopleOutline" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>
            <p v-else class="empty-text">暫無分會成員</p>
          </div>
        </div>

        <!-- Branch Content -->
        <div class="branch-content">
          <ion-segment v-model="activeTab" mode="md" scrollable>
            <ion-segment-button value="intro">
              <ion-label>分會介紹</ion-label>
            </ion-segment-button>
            <ion-segment-button value="missions">
              <ion-label>分會理念</ion-label>
            </ion-segment-button>
            <ion-segment-button value="activities">
              <ion-label>分會活動</ion-label>
            </ion-segment-button>
            <ion-segment-button value="shops">
              <ion-label>相關商店</ion-label>
            </ion-segment-button>
          </ion-segment>

          <!-- Introduction Tab -->
          <div v-if="activeTab === 'intro'" class="tab-content">
            <div class="section">
              <DescriptionSection sectionTitle="" :description="branch.introduction || '暫無分會介紹'" :truncateLength="500"></DescriptionSection>
            </div>
          </div>

          <!-- Mission Tab -->
          <div v-if="activeTab === 'missions'" class="tab-content">
            <div class="section">
              <DescriptionSection sectionTitle="" :description="branch.philosophy || '暫無分會理念'" :truncateLength="500"></DescriptionSection>
            </div>
          </div>

          <!-- Activities Tab -->
          <div v-if="activeTab === 'activities'" class="tab-content">
            <div class="section">
              <h2>即將舉行的活動</h2>
              <div v-if="upcomingEvents.length > 0" class="events-grid">
                <EventCard v-for="event in upcomingEvents" :key="event.id" :event="event" />
              </div>
              <p v-else class="empty-text">暫無即將舉行的活動</p>
            </div>

            <div class="section">
              <h2>已結束的活動</h2>
              <div v-if="pastEvents.length > 0" class="events-grid">
                <EventCard v-for="event in pastEvents" :key="event.id" :event="event" />
              </div>
              <p v-else class="empty-text">暫無已結束的活動</p>
            </div>
          </div>

          <!-- Shops Tab -->
          <div v-if="activeTab === 'shops'" class="tab-content">
            <div class="section">
              <h2>相關商店</h2>
              <div v-if="relatedShops.length > 0" class="shops-grid">
                <ShopCard
                  v-for="shop in relatedShops"
                  :key="shop.id"
                  :shop="shop"
                />
              </div>
              <p v-else class="empty-text">暫無相關商店</p>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline"></ion-icon>
        <p>找不到分會資料</p>
        <ion-button router-link="/branches" fill="outline">
          返回分會列表
        </ion-button>
      </div>

      <!-- Branch Edit Modal -->
      <BranchFormModal
        :is-open="showEditModal"
        :branch="branch"
        @close="showEditModal = false"
        @updated="handleBranchUpdated"
      />

      <!-- Join Branch Modal -->
      <ion-modal :is-open="showJoinModal" @didDismiss="showJoinModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>申請加入分會</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showJoinModal = false">取消</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <form @submit.prevent="submitJoinApplication">
            <ion-item>
              <ion-label position="stacked">申請訊息</ion-label>
              <ion-textarea
                v-model="applicationMessage"
                placeholder="請簡單介紹自己，並說明為何想加入此分會"
                :rows="6"
              ></ion-textarea>
            </ion-item>
            <div class="ion-padding">
              <ion-button
                type="submit"
                expand="block"
                :disabled="isSubmittingApplication"
              >
                <ion-spinner v-if="isSubmittingApplication" name="crescent"></ion-spinner>
                <span v-else>提交申請</span>
              </ion-button>
            </div>
          </form>
        </ion-content>
      </ion-modal>

      <!-- Preview Mode Alert -->
      <ion-alert
        :is-open="showPreviewAlert"
        header="預覽模式"
        message="您正在以訪客身份預覽分會。點擊左上角「退出預覽」返回管理模式。"
        :buttons="[
          {
            text: '確定',
            handler: () => { showPreviewAlert = false; }
          }
        ]"
      ></ion-alert>

      <!-- Approve Confirmation Alert -->
      <ion-alert
        :is-open="showApproveAlertFlag"
        header="確認批准"
        message="確定要批准此申請嗎？批准後，該用戶將成為分會成員。"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => { showApproveAlertFlag = false; }
          },
          {
            text: '確定批准',
            handler: () => {
              approveApplication(currentApplication);
              showApproveAlertFlag = false;
            }
          }
        ]"
      ></ion-alert>

      <!-- Reject Confirmation Alert -->
      <ion-alert
        :is-open="showRejectAlertFlag"
        header="確認拒絕"
        message="確定要拒絕此申請嗎？"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => { showRejectAlertFlag = false; }
          },
          {
            text: '確定拒絕',
            handler: () => {
              rejectApplication(currentApplication);
              showRejectAlertFlag = false;
            }
          }
        ]"
      ></ion-alert>



      <!-- Members Modal -->
      <ion-modal :is-open="showMembersModal" @didDismiss="showMembersModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>分會成員 ({{ members.length }})</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showMembersModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <ion-list>
            <ion-item v-for="member in members" :key="member.id" lines="full" :class="{'owner-highlight': isOwnerMember(member)}" button @click="navigateToUserProfile(member.user_id)">
              <ion-avatar slot="start">
                <img src="https://ionicframework.com/docs/img/demos/avatar.svg" :alt="member.users?.full_name">
              </ion-avatar>
              <ion-label>
                <h2>
                  {{ member.users?.full_name }}
                  <ion-badge v-if="isOwnerMember(member)" color="primary">分會長</ion-badge>
                </h2>
                <p v-if="member.users?.industry">{{ member.users?.industry }}</p>
                <p>{{ formatDate(member.joined_at) }} 加入</p>
              </ion-label>
              <ion-buttons slot="end">
                <ion-button fill="clear" color="primary" title="查看資料" @click.stop="navigateToUserProfile(member.user_id)">
                  <ion-icon :icon="personOutline" slot="icon-only"></ion-icon>
                </ion-button>
                <ion-button fill="clear" color="primary" title="聯絡" @click.stop="showMessageModal(member.user_id)">
                  <ion-icon :icon="mailOutline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-buttons>
            </ion-item>
          </ion-list>
        </ion-content>
      </ion-modal>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonSpinner,
  IonToast,
  IonAlert,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonList,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonItem,
  IonAvatar,
  IonNote,
  IonModal,
  IonTextarea,
  IonBadge,
  onIonViewWillEnter,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  createOutline,
  alertCircleOutline,
  eyeOutline,
  closeCircleOutline,
  peopleOutline,
  locationOutline,
  timeOutline,
  personAddOutline,
  personOutline,
  checkmarkOutline,
  closeOutline,
  mailOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import BranchFormModal from '@/components/BranchFormModal.vue';
import DescriptionSection from '@/components/DescriptionSection.vue';
import ShopCard from '@/components/ShopCard.vue';
import EventCard from '@/components/EventCard.vue';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const branchId = route.params.branchId as string;

const branch = ref<any>(null);
const members = ref<any[]>([]);
const upcomingEvents = ref<any[]>([]);
const pastEvents = ref<any[]>([]);
const relatedShops = ref<any[]>([]);
const pendingApplications = ref<any[]>([]);
const isLoading = ref(true);
const showEditModal = ref(false);
const showJoinModal = ref(false);
const toastMessage = ref('');
const showPreviewAlert = ref(false);
const isPreviewMode = ref(false);
const activeTab = ref('intro');
const applicationMessage = ref('');
const isSubmittingApplication = ref(false);
const hasApplied = ref(false);
const isProcessingApplication = ref(false);
const showApproveAlertFlag = ref(false);
const showRejectAlertFlag = ref(false);
const currentApplication = ref<any>(null);
const showMembersModal = ref(false);
const maxDisplayedMembers = ref(5); // Show 5 members by default

const isOwner = computed(() => {
  if (isPreviewMode.value) return false;
  return branch.value?.owner_id === authStore.currentUser?.id;
});

const isMember = computed(() => {
  return members.value.some(member => member.user_id === authStore.currentUser?.id);
});

const branchOwnerName = computed(() => {
  // First try to get the owner name directly from the joined owner data
  if (branch.value?.owner?.full_name) {
    return branch.value.owner.full_name;
  }

  // Fallback to finding the owner in the members list
  const ownerMember = members.value.find(member => member.user_id === branch.value?.owner_id);
  return ownerMember?.users?.full_name || '未知';
});

// Compute the members to display in the compact view
const displayedMembers = computed(() => {
  // If there are fewer members than the max, show all
  if (members.value.length <= maxDisplayedMembers.value) {
    return members.value;
  }

  // Otherwise, show only the first few members
  // First, ensure the owner is included in the displayed members
  const ownerMember = members.value.find(member => member.user_id === branch.value?.owner_id);

  if (!ownerMember) {
    // If no owner found (unlikely), just return the first few members
    return members.value.slice(0, maxDisplayedMembers.value);
  }

  // Otherwise, ensure owner is included and fill the rest with other members
  const otherMembers = members.value.filter(member => member.user_id !== branch.value?.owner_id);
  const remainingSlots = maxDisplayedMembers.value - 1; // -1 for the owner

  return [ownerMember, ...otherMembers.slice(0, remainingSlots)];
});



const loadBranchDetails = async () => {
  try {
    // Check if current user is likely the owner to decide whether to fetch applications
    const isLikelyOwner = authStore.currentUser?.id === route.query.owner_id ||
                          userStore.currentUser?.role === 'president';

    // Build the query with conditional parts
    let query = `
      *,
      branch_categories (
        id,
        title
      ),
      owner:owner_id (
        id,
        full_name,
        username
      ),
      branch_members!branch_members_branch_id_fkey (
        *,
        users (
          id,
          full_name,
          username
        )
      )
    `;

    // Add pending applications to the query if the user is likely the owner
    if (isLikelyOwner) {
      query += `,
      branch_member_applications!branch_member_applications_branch_id_fkey (
        *,
        users (
          id,
          full_name,
          username,
          email,
          phone
        )
      )`;
    }

    const { data: branchData, error: branchError } = await supabase
      .from('branches')
      .select(query)
      .eq('id', branchId)
      .single();

    if (branchError) throw branchError;
    branch.value = branchData;

    // Extract members from the joined data
    if (branchData?.branch_members) {
      members.value = branchData.branch_members
        .filter((member: any) => member.status === 'active')
        .sort((a: any, b: any) => new Date(a.joined_at).getTime() - new Date(b.joined_at).getTime());
    }

    // Extract pending applications if available
    if (branchData?.branch_member_applications && isOwner.value) {
      pendingApplications.value = branchData.branch_member_applications
        .filter((app: any) => app.status === 'pending')
        .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    }

    // Load related events (events hosted by the branch owner)
    if (branchData?.owner_id) {
      await loadRelatedEvents(branchData.owner_id);
    }
  } catch (error) {
    console.error('Error fetching branch details:', error);
    toastMessage.value = '載入分會資料時發生錯誤';
  }
};

const loadMembers = async () => {
  try {
    const { data: membersData, error: membersError } = await supabase
      .from('branch_members')
      .select(`
        *,
        users (
          id,
          full_name,
          username
        )
      `)
      .eq('branch_id', branchId)
      .eq('status', 'active')
      .order('joined_at', { ascending: true });

    if (membersError) throw membersError;
    members.value = membersData || [];
  } catch (error) {
    console.error('Error fetching branch members:', error);
    toastMessage.value = '載入分會成員時發生錯誤';
  }
};

const loadPendingApplications = async () => {
  if (!isOwner.value) return;

  try {
    const { data, error } = await supabase
      .from('branch_member_applications')
      .select(`
        *,
        users (
          id,
          full_name,
          username,
          email,
          phone
        )
      `)
      .eq('branch_id', branchId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false });

    if (error) throw error;
    pendingApplications.value = data || [];
  } catch (error) {
    console.error('Error fetching pending applications:', error);
    toastMessage.value = '載入申請資料時發生錯誤';
  }
};

const isOwnerMember = (member: any) => {
  return member.user_id === branch.value?.owner_id;
};

const loadRelatedShops = async () => {
  try {
    // Get member user IDs from the already loaded members data
    const memberUserIds = members.value.map(member => member.user_id);

    if (memberUserIds.length === 0) {
      relatedShops.value = [];
      return;
    }

    // Get shops owned by branch members
    const { data: shopsData, error: shopsError } = await supabase
      .from('shops')
      .select('*')
      .in('owner_id', memberUserIds)
      .order('created_at', { ascending: false });

    if (shopsError) throw shopsError;
    relatedShops.value = shopsData || [];
  } catch (error) {
    console.error('Error fetching related shops:', error);
    toastMessage.value = '載入相關商店時發生錯誤';
  }
};

const loadRelatedEvents = async (ownerId: string) => {
  try {
    // Fetch events where the host is the branch owner
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        users:user_id (
          full_name
        )
      `)
      .eq('user_id', ownerId)
      .order('start_datetime', { ascending: true });

    if (error) throw error;

    // Transform the data to include creator_full_name
    const eventsWithCreator = data.map(event => ({
      ...event,
      creator_full_name: event.users?.full_name || null
    }));

    // Split events into upcoming and past
    const now = new Date();
    upcomingEvents.value = eventsWithCreator
      .filter(event => new Date(event.start_datetime) >= now)
      .sort((a, b) => new Date(a.start_datetime).getTime() - new Date(b.start_datetime).getTime());

    pastEvents.value = eventsWithCreator
      .filter(event => new Date(event.start_datetime) < now)
      .sort((a, b) => new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime());
  } catch (error) {
    console.error('Error fetching related events:', error);
    toastMessage.value = '載入相關活動時發生錯誤';
  }
};

const checkApplicationStatus = async () => {
  if (!authStore.isAuthenticated) return;

  try {
    const { data, error } = await supabase
      .from('branch_member_applications')
      .select('*')
      .eq('branch_id', branchId)
      .eq('user_id', authStore.currentUser?.id)
      .eq('status', 'pending')
      .maybeSingle();

    if (error) throw error;
    hasApplied.value = !!data;
  } catch (error) {
    console.error('Error checking application status:', error);
  }
};

// Approve an application
const approveApplication = async (application: any) => {
  if (!isOwner.value) return;

  try {
    isProcessingApplication.value = true;

    // Update application status to approved
    const { error } = await supabase
      .from('branch_member_applications')
      .update({ status: 'approved' })
      .eq('id', application.id);

    if (error) throw error;

    // Manually add the user to branch_members to ensure it works
    // This is a fallback in case the database trigger doesn't work
    try {
      await supabase
        .from('branch_members')
        .upsert({
          branch_id: application.branch_id,
          user_id: application.user_id,
          status: 'active',
          joined_at: new Date().toISOString()
        }, { onConflict: 'branch_id,user_id' });
    } catch (memberError) {
      console.error('Fallback member creation error (non-critical):', memberError);
      // Continue even if this fails - the trigger should handle it
    }

    // Refresh data
    await Promise.all([
      loadMembers(),
      loadPendingApplications()
    ]);

    toastMessage.value = '已批准申請';
  } catch (error) {
    console.error('Error approving application:', error);
    toastMessage.value = '批准申請時發生錯誤';
  } finally {
    isProcessingApplication.value = false;
  }
};

// Reject an application
const rejectApplication = async (application: any) => {
  if (!isOwner.value) return;

  try {
    isProcessingApplication.value = true;

    // Update application status to rejected
    const { error } = await supabase
      .from('branch_member_applications')
      .update({ status: 'rejected' })
      .eq('id', application.id);

    if (error) throw error;

    // Refresh pending applications
    await loadPendingApplications();

    toastMessage.value = '已拒絕申請';
  } catch (error) {
    console.error('Error rejecting application:', error);
    toastMessage.value = '拒絕申請時發生錯誤';
  } finally {
    isProcessingApplication.value = false;
  }
};

// Use Ionic lifecycle hooks
onIonViewDidEnter(async () => {
  isLoading.value = true;
  await loadBranchDetails();
  await Promise.all([
    checkApplicationStatus(),
    loadRelatedShops()
  ]);
  isLoading.value = false;
});

const handleBranchUpdated = (updatedBranch: any) => {
  // Merge the updated data with existing data to preserve joined fields
  branch.value = {
    ...branch.value,
    ...updatedBranch
  };
  showEditModal.value = false;
  toastMessage.value = '分會資料已更新';
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// More compact date format for the members list
const formatCompactDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
  }) + ' 加入';
};

const submitJoinApplication = async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }

  try {
    isSubmittingApplication.value = true;

    // Submit application to branch_member_applications
    const { error } = await supabase
      .from('branch_member_applications')
      .insert({
        branch_id: branchId,
        user_id: authStore.currentUser?.id,
        message: applicationMessage.value,
      });

    if (error) throw error;

    hasApplied.value = true;
    showJoinModal.value = false;
    applicationMessage.value = '';
    toastMessage.value = '申請已提交，請等待分會長審核';
  } catch (error: any) {
    console.error('Error submitting application:', error);
    toastMessage.value = error.message || '申請提交失敗，請稍後再試';
  } finally {
    isSubmittingApplication.value = false;
  }
};

const previewBranch = () => {
  isPreviewMode.value = true;
  showPreviewAlert.value = true;
};

const exitPreview = () => {
  isPreviewMode.value = false;
};

// Show confirmation before approving application
const showApproveConfirm = (application: any) => {
  currentApplication.value = application;
  showApproveAlertFlag.value = true;
};

// Show confirmation before rejecting application
const showRejectConfirm = (application: any) => {
  currentApplication.value = application;
  showRejectAlertFlag.value = true;
};

// Handle join button click - check if user is logged in first
const handleJoinButtonClick = () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    router.push('/login');
    return;
  }

  // User is logged in, show the join modal
  showJoinModal.value = true;
};

// Navigate to user profile
const navigateToUserProfile = (userId: string) => {
  if (!userId) return;

  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入以查看用戶資料';
    router.push('/login');
    return;
  }

  // Check if user has permission to view profiles (merchant or president only)
  if (authStore.currentUser?.role === 'free') {
    toastMessage.value = '升級會員以查看用戶資料';
    return;
  }

  // Navigate to user profile
  router.push(`/users/${userId}`);
};

// Show message modal (placeholder for future implementation)
const showMessageModal = (userId: string) => {
  if (!userId) return;

  // For now, just show a toast message
  toastMessage.value = '訊息功能即將推出';
};
</script>

<style scoped>
.loading-container,
.error-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.branch-container {
  max-width: 1200px;
  margin: 0 auto;
}

.branch-banner {
  position: relative;
  width: 100%;
  height: 350px;
  overflow: hidden;
  border-radius: 24px;
  margin-bottom: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.branch-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.branch-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  display: flex;
  align-items: flex-end;
}

.branch-logo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid white;
  flex-shrink: 0;
  margin-right: 1.5rem;
}

.branch-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.branch-details {
  flex: 1;
}

.branch-details h1 {
  margin: 0 0 0.5rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.branch-description {
  margin: 0 0 1rem;
  font-size: 1rem;
  opacity: 0.9;
}

.branch-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.meta-item ion-icon {
  margin-right: 0.5rem;
}

.branch-content {
  margin-top: 1rem;
}

.tab-content {
  padding: 0 0 1.5rem 0;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
}

.empty-text {
  color: var(--ion-color-medium);
  text-align: center;
  padding: 2rem 0;
}

.activities-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.activity-card {
  margin: 0;
}

.members-list {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.members-list.ultra-compact ion-item {
  --padding-start: 8px;
  --padding-end: 8px;
  --min-height: 48px;
}

.small-avatar {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

.compact-member-info {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.member-name {
  font-weight: 500;
  display: flex;
  align-items: center;
}

.member-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 2px;
}

.small-badge {
  font-size: 0.6rem;
  padding: 2px 6px;
  margin-left: 6px;
  height: 16px;
}

.industry {
  font-size: 0.75rem;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.join-date {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
}

.action-buttons {
  margin-left: 8px;
}

.action-buttons ion-button {
  --padding-start: 4px;
  --padding-end: 4px;
  height: 28px;
  font-size: 14px;
}

.branch-owner-section {
  margin: 1rem 0;
  background-color: var(--ion-color-light);
  border-radius: 12px;
  overflow: hidden;
}

.branch-owner-item {
  --background: transparent;
}

.branch-owner-item ion-icon {
  color: var(--ion-color-primary);
  font-size: 1.5rem;
}

.branch-owner-item h3 {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.branch-owner-item h2 {
  font-weight: 600;
  font-size: 1.1rem;
  margin: 0;
}

.join-section {
  margin-top: 1rem;
  text-align: center;
}

.shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.shop-card {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
}

.shop-banner {
  height: 150px;
  overflow: hidden;
}

.shop-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.shop-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.shop-logo {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 0.75rem;
  object-fit: cover;
}

/* Applications section styles */
.applications-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: var(--ion-color-light);
  border-radius: 12px;
}

.applications-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.application-card {
  margin: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.application-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

/* Owner highlight styles */
.owner-highlight {
  background-color: rgba(var(--ion-color-primary-rgb), 0.05);
  border-left: 4px solid var(--ion-color-primary);
}

@media (max-width: 768px) {
  .branch-banner {
    height: 250px;
    border-radius: 16px;
  }

  .branch-info-overlay {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .branch-logo {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .branch-meta {
    justify-content: center;
  }

  .activities-list,
  .shops-grid,
  .applications-list {
    grid-template-columns: 1fr;
  }
}
</style>
