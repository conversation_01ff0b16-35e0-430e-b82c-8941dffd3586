<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>已收藏活動</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <div class="events-container ion-padding">
          <!-- Liked Events -->
          <div class="all-events">
            <div class="events-grid">
              <EventCard
                v-for="event in likedEvents"
                :key="event.id"
                :event="event"
              />
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="!isLoading && likedEvents.length === 0" class="empty-state">
            <ion-icon :icon="heartOutline" color="medium"></ion-icon>
            <p>您還沒有收藏任何活動</p>
            <ion-button router-link="/events" expand="block" class="browse-button">
              瀏覽活動
            </ion-button>
          </div>
        </div>

        <!-- Toast Messages -->
        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonToast,
  IonButtons,
  IonBackButton,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heartOutline,
  heart,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import EventCard from '@/components/EventCard.vue';

const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const likedEvents = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');

onIonViewDidEnter(() => {
  loadData();
});

const loadData = async () => {
  try {
    isLoading.value = true;

    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      router.push('/login');
      return;
    }

    // Load user's liked events
    const { data: likedData, error: likedError } = await supabase
      .from('user_liked_events')
      .select('event_id')
      .eq('user_id', authStore.currentUser.id);

    if (likedError) throw likedError;

    if (likedData && likedData.length > 0) {
      // Get the event details for liked events
      const eventIds = likedData.map((item: { event_id: string }) => item.event_id);

      const { data: eventsData, error: eventsError } = await supabase
        .from('events')
        .select(`
          *,
          users:user_id (
            full_name
          )
        `)
        .in('id', eventIds)
        .order('start_datetime', { ascending: true });

      if (eventsError) throw eventsError;
      
      // Transform the data to include creator_full_name
      likedEvents.value = eventsData.map((event: any) => ({
        ...event,
        creator_full_name: event.users?.full_name || null
      }));
    } else {
      likedEvents.value = [];
    }
  } catch (error) {
    console.error('Error loading liked events:', error);
    toastMessage.value = '載入資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.page-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.events-container {
  flex: 1;
  width: 100%;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-state ion-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state p {
  margin-bottom: 1.5rem;
  color: var(--ion-color-medium);
  font-size: 1.1rem;
}

.browse-button {
  max-width: 300px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
  }
}
</style>
