<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/events" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>報名確認</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <LoadingSpinner v-if="isLoading" />

      <div v-else-if="application" class="page-container">
        <div class="confirmation-card">
          <!-- Success Message -->
          <div class="success-message">
            <ion-icon :icon="checkmarkCircle" color="success"></ion-icon>
            <h1>報名成功！</h1>
            <p>您已成功報名以下活動</p>
          </div>

          <!-- Event Details -->
          <div class="event-details">
            <h2>{{ event?.title }}</h2>
            <ion-list>
              <ion-item lines="none">
                <ion-icon :icon="calendarOutline" slot="start"></ion-icon>
                <ion-label>
                  <h3>日期及時間</h3>
                  <p>{{ formatDateTime(event?.start_datetime) }}</p>
                  <p>至 {{ formatDateTime(event?.end_datetime) }}</p>
                </ion-label>
              </ion-item>

              <ion-item lines="none">
                <ion-icon :icon="locationOutline" slot="start"></ion-icon>
                <ion-label>
                  <h3>地點</h3>
                  <p>{{ event?.address }}</p>
                </ion-label>
              </ion-item>
            </ion-list>
          </div>

          <!-- QR Code -->
          <div class="qr-code-section">
            <h2>入場二維碼</h2>
            <p>請於活動當天出示此二維碼以供登記出席</p>
            <div class="qr-code">
              <img :src="qrCodeUrl" alt="QR Code" />
              <div class="check-in-code">
                <p>簽到碼：<span>{{ formatCheckInCode(extractCheckInCode(application?.qr_code || '')) }}</span></p>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="confirmation-actions">
            <!--
            <ion-button expand="block" @click="saveToCalendar">
              <ion-icon :icon="calendarOutline" slot="start"></ion-icon>
              加入日曆
            </ion-button>
            -->
            <ion-button expand="block" fill="outline" @click="shareApplication">
              <ion-icon :icon="shareOutline" slot="start"></ion-icon>
              分享
            </ion-button>
          </div>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
        <p>找不到報名記錄</p>
      </div>

      <!-- Toast Messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonButton,
  IonButtons,
  IonBackButton,
  IonIcon,
  IonToast,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  calendarOutline,
  locationOutline,
  checkmarkCircle,
  alertCircleOutline,
  shareOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import { Share } from '@capacitor/share';
import { Clipboard } from '@capacitor/clipboard';
import { extractCheckInCode, formatCheckInCode } from '@/utils/qrCodeUtils';

const route = useRoute();
const router = useRouter();
const eventId = route.params.eventId as string;
const applicationId = route.params.applicationId as string;

const event = ref<any>(null);
const application = ref<any>(null);
const isLoading = ref(true);
const toastMessage = ref('');

const qrCodeUrl = computed(() => {
  if (!application.value?.qr_code) return '';
  return `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(application.value.qr_code)}&dpi=96&quietzone=5`;
});

onIonViewDidEnter(async () => {
  await loadData();
});

const loadData = async () => {
  try {
    isLoading.value = true;

    // Load application details
    const { data: applicationData, error: applicationError } = await supabase
      .from('event_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (applicationError) throw applicationError;
    application.value = applicationData;

    // Load event details
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (eventError) throw eventError;
    event.value = eventData;
  } catch (error) {
    console.error('Error loading data:', error);
    toastMessage.value = '載入資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Format datetime for display (assuming input is already in HKT)
const formatDateTime = (datetimeStr?: string) => {
  if (!datetimeStr) return '';

  // Create a date object from the datetime string
  const date = new Date(datetimeStr);

  // Format the date
  return date.toLocaleString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const saveToCalendar = async () => {
  if (!event.value) return;

  try {
    // Use the UTC datetime values directly
    const startDate = new Date(event.value.start_datetime);
    const endDate = new Date(event.value.end_datetime);

    const calendarEvent = {
      title: event.value.title,
      description: event.value.description,
      location: event.value.address,
      start: startDate.toISOString(),
      end: endDate.toISOString(),
    };

    // Create .ics file
    const icsData = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'BEGIN:VEVENT',
      `SUMMARY:${calendarEvent.title}`,
      `DTSTART:${calendarEvent.start.replace(/[-:]/g, '').replace(/\.\d{3}/, '')}`,
      `DTEND:${calendarEvent.end.replace(/[-:]/g, '').replace(/\.\d{3}/, '')}`,
      `DESCRIPTION:${calendarEvent.description}`,
      `LOCATION:${calendarEvent.location}`,
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\n');

    const blob = new Blob([icsData], { type: 'text/calendar;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${event.value.title}.ics`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    toastMessage.value = '已下載日曆檔案';
  } catch (error) {
    console.error('Error saving to calendar:', error);
    toastMessage.value = '無法加入日曆';
  }
};

const shareApplication = async () => {
  if (!event.value) return;

  const shareText = `
我已報名參加「${event.value.title}」！

時間：${formatDateTime(event.value.start_datetime)}
至 ${formatDateTime(event.value.end_datetime)}
地點：${event.value.address}

活動詳情：https://syner-biz.com/events/${eventId}
  `.trim();

  const shareUrl = `https://syner-biz.com/events/${eventId}`;

  try {
    await Share.share({
      title: '活動報名確認',
      text: shareText,
      url: shareUrl,
      dialogTitle: '分享活動報名',
    });
  } catch (error) {
    if (navigator.share) {
      try {
        await navigator.share({
          title: '活動報名確認',
          text: shareText,
        });
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error);
          toastMessage.value = '分享失敗';
        }
      }
    } else {
      try {
        await Clipboard.write({ string: shareText });
        toastMessage.value = '已複製活動資料';
      } catch (e) {
        try {
          await navigator.clipboard.writeText(shareText);
          toastMessage.value = '已複製活動資料';
        } catch (error) {
          console.error('Error copying to clipboard:', error);
          toastMessage.value = '複製失敗';
        }
      }
    }
  }
};
</script>

<style scoped>
.confirmation-card {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.success-message {
  text-align: center;
  margin-bottom: 2rem;
}

.success-message ion-icon {
  font-size: 64px;
  margin-bottom: 1rem;
}

.success-message h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem;
  color: var(--ion-color-dark);
}

.success-message p {
  color: var(--ion-color-medium);
  margin: 0;
}

.event-details {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--ion-color-light);
  border-radius: 12px;
}

.event-details h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--ion-color-dark);
}

.event-details ion-item {
  --background: transparent;
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
}

.event-details ion-icon {
  color: var(--ion-color-primary);
  font-size: 1.25rem;
  margin-right: 1rem;
}

.event-details h3 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-medium);
}

.event-details p {
  font-size: 1rem;
  margin: 0.25rem 0 0;
  color: var(--ion-color-dark);
}

.qr-code-section {
  text-align: center;
  margin-bottom: 2rem;
}

.qr-code-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--ion-color-dark);
}

.qr-code-section p {
  color: var(--ion-color-medium);
  margin: 0 0 1rem;
}

.qr-code {
  display: inline-block;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code img {
  width: 200px;
  height: 200px;
}

.check-in-code {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed var(--ion-color-light-shade);
}

.check-in-code p {
  margin: 0;
  font-size: 1rem;
  color: var(--ion-color-dark);
}

.check-in-code span {
  font-weight: 700;
  font-size: 1.2rem;
  letter-spacing: 2px;
  color: var(--ion-color-primary);
}

.confirmation-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-container {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .confirmation-card {
    padding: 1.5rem;
  }

  .success-message ion-icon {
    font-size: 48px;
  }

  .success-message h1 {
    font-size: 1.25rem;
  }

  .event-details {
    padding: 1rem;
  }

  .qr-code img {
    width: 160px;
    height: 160px;
  }
}
</style>