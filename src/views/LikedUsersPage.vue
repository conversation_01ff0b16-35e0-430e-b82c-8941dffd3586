<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/profile" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>已收藏用戶</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">已收藏用戶</ion-title>
        </ion-toolbar>
      </ion-header>

      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="likedUsers.length === 0" class="empty-container">
        <ion-icon :icon="peopleOutline" size="large"></ion-icon>
        <h2>暫無收藏用戶</h2>
        <p>您還沒有收藏任何用戶</p>
        <ion-button router-link="/tabs/home" fill="outline">
          瀏覽用戶
        </ion-button>
      </div>

      <div v-else class="page-container">
        <div class="users-container ion-padding">
          <!-- Liked Users -->
          <ion-list>
            <ion-item
              v-for="user in likedUsers"
              :key="user.id"
              button
              :router-link="`/users/${user.id}`"
              detail
            >
              <ion-avatar slot="start">
                <img src="https://ionicframework.com/docs/img/demos/avatar.svg" :alt="user.full_name">
              </ion-avatar>
              <ion-label>
                <h2>{{ user.full_name }}</h2>
                <div class="user-meta">
                  <ion-badge color="primary">{{ getRoleLabel(user.role) }}</ion-badge>
                  <span v-if="user.industry" class="industry">{{ user.industry }}</span>
                </div>
                <p v-if="user.notes" class="notes">{{ user.notes }}</p>
              </ion-label>
              <ion-buttons slot="end">
                <ion-button fill="clear" color="danger" @click.prevent.stop="confirmUnlike(user)">
                  <ion-icon :icon="heartDislikeOutline" slot="icon-only"></ion-icon>
                </ion-button>
              </ion-buttons>
            </ion-item>
          </ion-list>
        </div>
      </div>

      <!-- Confirm Unlike Alert -->
      <ion-alert
        :is-open="!!userToUnlike"
        header="確認取消收藏"
        :message="`確定要取消收藏 ${userToUnlike?.full_name || '此用戶'} 嗎？`"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => { userToUnlike = null; }
          },
          {
            text: '確定',
            handler: () => { unlikeUser(userToUnlike); }
          }
        ]"
      ></ion-alert>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonSpinner,
  IonToast,
  IonList,
  IonItem,
  IonAvatar,
  IonLabel,
  IonBadge,
  IonAlert,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  peopleOutline,
  heartDislikeOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import type { UserRole } from '@/types';

const router = useRouter();
const authStore = useAuthStore();
const isLoading = ref(true);
const likedUsers = ref<any[]>([]);
const toastMessage = ref('');
const userToUnlike = ref<any>(null);

// Load liked users
const loadData = async () => {
  try {
    isLoading.value = true;

    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      router.push('/login');
      return;
    }

    // Load user's liked users with joined user data
    const { data, error } = await supabase
      .from('user_liked_users')
      .select(`
        *,
        liked_user:liked_user_id (
          id,
          full_name,
          username,
          role,
          industry,
          company_name,
          created_at
        )
      `)
      .eq('user_id', authStore.currentUser.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Transform the data to a more usable format
    likedUsers.value = data.map(item => ({
      id: item.liked_user.id,
      full_name: item.liked_user.full_name,
      username: item.liked_user.username,
      role: item.liked_user.role,
      industry: item.liked_user.industry,
      company_name: item.liked_user.company_name,
      created_at: item.liked_user.created_at,
      notes: item.notes,
      like_id: item.id,
      liked_at: item.created_at
    }));
  } catch (error) {
    console.error('Error loading liked users:', error);
    toastMessage.value = '載入收藏用戶時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Confirm unlike user
const confirmUnlike = (user: any) => {
  userToUnlike.value = user;
};

// Unlike user
const unlikeUser = async (user: any) => {
  if (!user || !authStore.isAuthenticated) return;

  try {
    const { error } = await supabase
      .from('user_liked_users')
      .delete()
      .eq('user_id', authStore.currentUser?.id)
      .eq('liked_user_id', user.id);

    if (error) throw error;

    // Remove from local list
    likedUsers.value = likedUsers.value.filter(u => u.id !== user.id);
    toastMessage.value = '已取消收藏用戶';
  } catch (error) {
    console.error('Error unliking user:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  } finally {
    userToUnlike.value = null;
  }
};

// Helper functions
const getRoleLabel = (role: UserRole) => {
  switch (role) {
    case 'free': return '普通會員';
    case 'merchant': return '商家會員';
    case 'president': return '分會長';
    default: return '會員';
  }
};

// Use Ionic lifecycle hooks
onIonViewDidEnter(() => {
  loadData();
});
</script>

<style scoped>
.loading-container,
.empty-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
  text-align: center;
  padding: 2rem;
}

.empty-container ion-icon {
  font-size: 64px;
  margin-bottom: 1rem;
}

.empty-container h2 {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.empty-container p {
  margin-bottom: 1.5rem;
}

.users-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.25rem 0;
}

.industry {
  font-size: 0.85rem;
  color: var(--ion-color-medium);
}

.notes {
  font-style: italic;
  color: var(--ion-color-dark-shade);
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}
</style>
