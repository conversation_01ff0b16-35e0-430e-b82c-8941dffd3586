<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/login" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>註冊</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div class="auth-container">
        <div class="auth-card">
          <div class="brand-section">
            <h1 class="brand-title">Waste to Gold</h1>
            <p class="brand-subtitle">「搵料」</p>
          </div>

          <form @submit.prevent="handleSubmit" class="register-form">
            <div class="form-header">
              <h2>創建帳戶</h2>
              <p>填寫以下資料以完成註冊</p>
            </div>

            <ion-list class="form-list">
              <!-- Basic Information Section 
              <div class="form-section">
                <ion-item-group>
                  <ion-item class="custom-item">
                    <ion-select
                      interface="popover"
                      label="會員類型"
                      label-placement="floating"
                      v-model="formData.role"
                      @ion-change="handleRoleChange"
                      required
                    >
                      <ion-select-option value="free">普通會員</ion-select-option>
                      <ion-select-option value="merchant">商家會員</ion-select-option>
                      <ion-select-option value="president">分會長</ion-select-option>
                    </ion-select>
                    <ion-icon :icon="peopleOutline" slot="start" class="form-icon"></ion-icon>
                  </ion-item>

                  <ion-item class="custom-item">
                    <ion-input
                      label="推薦人代碼"
                      label-placement="floating"
                      v-model="formData.referralCode"
                      type="text"
                      placeholder="輸入6位數字代碼或掃描QR碼"
                      :class="{ 'ion-invalid': referralError, 'ion-valid': referralValid }"
                      :helper-text="referralValid ? `推薦人: ${referrerInfo?.fullName} (${referrerInfo?.username})` : referralError ? `無效推薦人代碼 (推薦人將會被改成公司)` : '如漏空代表推薦人是公司'"
                      :disabled="isReferralCodeFromUrl"
                    ></ion-input>
                    <ion-icon :icon="peopleCircleOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-button
                      v-if="!isReferralCodeFromUrl"
                      slot="end"
                      fill="clear"
                      @click="showQrScanner = true"
                    >
                      <ion-icon :icon="qrCodeOutline"></ion-icon>
                    </ion-button>
                  </ion-item>
                </ion-item-group>
              </div>-->

              <!-- Account Information Section -->
              <div class="form-section">
                <ion-item-group>
                  <ion-item class="custom-item" :class="{ 'ion-invalid': usernameError || usernameExists }">
                    <ion-icon :icon="personOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="登入帳號*"
                      label-placement="floating"
                      type="text"
                      v-model="formData.username"
                      @ion-blur="checkUsername"
                      @ion-input="validateUsername"
                      required
                    ></ion-input>
                  </ion-item>
                  <div class="error-message" v-if="usernameError">{{ usernameError }}</div>
                  <div class="error-message" v-if="usernameExists && !usernameError">此帳號已被註冊</div>

                  <ion-item class="custom-item" :class="{ 'ion-invalid': emailError || emailExists }">
                    <ion-icon :icon="mailOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="登入電郵地址*"
                      label-placement="floating"
                      type="email"
                      v-model="formData.email"
                      @ion-blur="checkEmail"
                      @ion-input="validateEmails"
                      required
                    ></ion-input>
                  </ion-item>
                  <div class="error-message" v-if="emailError">{{ emailError }}</div>
                  <div class="error-message" v-if="emailExists && !emailError">此電郵地址已被註冊</div>

                  <!--<ion-item class="custom-item" v-if="!emailExists && formData.email">-->
                  <ion-item class="custom-item" :class="{ 'ion-invalid': emailMismatch }" v-if="formData.email">
                    <ion-icon :icon="mailOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="確認電郵地址*"
                      label-placement="floating"
                      type="email"
                      v-model="formData.confirmEmail"
                      @ion-blur="validateEmails"
                      @ion-input="validateEmails"
                      required
                    ></ion-input>
                  </ion-item>
                  <div class="error-message" v-if="emailMismatch">電郵地址不符</div>

                  <ion-item class="custom-item" :class="{ 'ion-invalid': passwordError }">
                    <ion-icon :icon="lockClosedOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="密碼*"
                      label-placement="floating"
                      type="password"
                      v-model="formData.password"
                      @ion-blur="validatePasswords"
                      @ion-input="validatePasswords"
                      required
                    ></ion-input>
                  </ion-item>
                  <div class="error-message" v-if="passwordError">{{ passwordError }}</div>

                  <ion-item class="custom-item" :class="{ 'ion-invalid': passwordMismatch }">
                    <ion-icon :icon="lockClosedOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="確認密碼*"
                      label-placement="floating"
                      type="password"
                      v-model="formData.confirmPassword"
                      @ion-blur="validatePasswords"
                      @ion-input="validatePasswords"
                      required
                    ></ion-input>
                  </ion-item>
                  <div class="error-message" v-if="passwordMismatch">密碼不符</div>
                </ion-item-group>
              </div>

              <!-- Personal Information Section -->
              <div class="form-section">
                <ion-item-group>
                  <ion-item class="custom-item">
                    <ion-icon :icon="idCardOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="姓名*"
                      label-placement="floating"
                      type="text"
                      v-model="formData.fullName"
                      required
                    ></ion-input>
                  </ion-item>

                  <ion-item class="custom-item">
                    <ion-icon :icon="globeOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-select
                      label="國家/地區*"
                      label-placement="floating"
                      v-model="formData.countryCode"
                    >
                      <ion-select-option value="+852">香港 (+852)</ion-select-option>
                      <ion-select-option value="+853">澳門 (+853)</ion-select-option>
                      <ion-select-option value="+86">中國內地 (+86)</ion-select-option>
                    </ion-select>
                  </ion-item>

                  <ion-item class="custom-item">
                    <ion-icon :icon="callOutline" slot="start" class="form-icon"></ion-icon>
                    <ion-input
                      label="手機號碼"
                      label-placement="floating"
                      type="tel"
                      v-model="formData.phoneNumber"
                    ></ion-input>
                  </ion-item>
                </ion-item-group>
              </div>

              <!-- Business Information Section
              <div v-if="isMerchantOrPresident" class="form-section">
                <ion-item-group>
                  <ion-item class="custom-item">
                    <ion-select
                      label="行業"
                      label-placement="floating"
                      v-model="formData.industry"
                      @ion-change="handleIndustryChange"
                    >
                      <ion-select-option value="零售業">零售業</ion-select-option>
                      <ion-select-option value="批發業">批發業</ion-select-option>
                      <ion-select-option value="餐飲業">餐飲業</ion-select-option>
                      <ion-select-option value="金融業">金融業</ion-select-option>
                      <ion-select-option value="保險業">保險業</ion-select-option>
                      <ion-select-option value="資訊科技">資訊科技</ion-select-option>
                      <ion-select-option value="教育業">教育業</ion-select-option>
                      <ion-select-option value="補習社">補習社</ion-select-option>
                      <ion-select-option value="醫療服務">醫療服務</ion-select-option>
                      <ion-select-option value="保健服務">保健服務</ion-select-option>
                      <ion-select-option value="地產代理">地產代理</ion-select-option>
                      <ion-select-option value="建築業">建築業</ion-select-option>
                      <ion-select-option value="室內設計">室內設計</ion-select-option>
                      <ion-select-option value="貿易業">貿易業</ion-select-option>
                      <ion-select-option value="物流業">物流業</ion-select-option>
                      <ion-select-option value="運輸業">運輸業</ion-select-option>
                      <ion-select-option value="旅遊業">旅遊業</ion-select-option>
                      <ion-select-option value="酒店業">酒店業</ion-select-option>
                      <ion-select-option value="法律服務">法律服務</ion-select-option>
                      <ion-select-option value="會計服務">會計服務</ion-select-option>
                      <ion-select-option value="美容業">美容業</ion-select-option>
                      <ion-select-option value="健身業">健身業</ion-select-option>
                      <ion-select-option value="傳媒業">傳媒業</ion-select-option>
                      <ion-select-option value="廣告業">廣告業</ion-select-option>
                      <ion-select-option value="婚禮策劃">婚禮策劃</ion-select-option>
                      <ion-select-option value="活動策劃">活動策劃</ion-select-option>
                      <ion-select-option value="家居服務">家居服務</ion-select-option>
                      <ion-select-option value="維修服務">維修服務</ion-select-option>
                      <ion-select-option value="時裝業">時裝業</ion-select-option>
                      <ion-select-option value="珠寶業">珠寶業</ion-select-option>
                      <ion-select-option value="藝術文化">藝術文化</ion-select-option>
                      <ion-select-option value="社會服務">社會服務</ion-select-option>
                      <ion-select-option value="顧問服務">顧問服務</ion-select-option>
                      <ion-select-option value="製造業">製造業</ion-select-option>
                      <ion-select-option value="電子商務">電子商務</ion-select-option>
                      <ion-select-option value="其他">其他</ion-select-option>
                    </ion-select>
                    <ion-icon :icon="businessOutline" slot="start" class="form-icon"></ion-icon>
                  </ion-item>

                  <ion-item class="custom-item" v-if="formData.industry === '其他'">
                    <ion-input
                      label="請輸入行業"
                      label-placement="floating"
                      v-model="formData.otherIndustry"
                    ></ion-input>
                    <ion-icon :icon="businessOutline" slot="start" class="form-icon"></ion-icon>
                  </ion-item>

                  <ion-item class="custom-item">
                    <ion-input
                      label="公司名稱"
                      label-placement="floating"
                      type="text"
                      v-model="formData.companyName"
                    ></ion-input>
                    <ion-icon :icon="briefcaseOutline" slot="start" class="form-icon"></ion-icon>
                  </ion-item>
                </ion-item-group>
              </div>-->

              <!-- Terms Section -->
              <div class="terms-section">
                <p class="terms-notice">
                  點擊註冊按鈕，即表示您同意我們的 <a style="cursor: pointer; text-decoration: underline;" @click="showTermsModal = true">註冊條款</a>
                </p>
              </div>
            </ion-list>

            <!-- Form Actions -->
            <div class="form-actions">
              <ion-button
                expand="block"
                type="submit"
                class="submit-button"
                :disabled="!isFormValid || isSubmitting"
              >
                <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
                <template v-else>
                  註冊
                  <ion-icon slot="end" :icon="personAddOutline"></ion-icon>
                </template>
              </ion-button>

              <div class="login-prompt">
                <span>已有帳戶？</span>
                <ion-button fill="clear" router-link="/login" class="login-link">
                  立即登入
                  <ion-icon slot="end" :icon="arrowForward"></ion-icon>
                </ion-button>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- QR Code Scanner Modal -->
      <ion-modal :is-open="showQrScanner" @didDismiss="showQrScanner = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>掃描推薦人QR碼</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showQrScanner = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content>
          <QrCodeScanner @code-scanned="handleQrCodeScanned" />
        </ion-content>
      </ion-modal>

      <!-- Add alert -->
      <ion-alert
        :is-open="showConfirmAlert"
        header="確認註冊"
        :message="confirmationMessage"
        :buttons="[
          {
            text: '取消',
            role: 'cancel',
            handler: () => {
              showConfirmAlert = false;
            },
          },
          {
            text: '確定',
            handler: () => {
              showConfirmAlert = false;
              submitRegistration();
            },
          },
        ]"
      ></ion-alert>

      <!-- Add toast for messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>

      <!-- Terms and Conditions Modal -->
      <ion-modal :is-open="showTermsModal" @didDismiss="showTermsModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>註冊條款</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showTermsModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div v-if="isLoadingTerms" class="terms-loading">
            <ion-spinner name="crescent"></ion-spinner>
            <p>載入中...</p>
          </div>
          <div v-else class="terms-content">
            <div v-if="termsContent" v-html="termsContent"></div>
            <div v-else class="terms-error">
              <p>無法載入註冊條款。請稍後再試。</p>
            </div>
          </div>
        </ion-content>
        <ion-footer>
          <ion-toolbar>
            <ion-button expand="block" @click="showTermsModal = false">關閉</ion-button>
          </ion-toolbar>
        </ion-footer>
      </ion-modal>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonItemGroup,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonIcon,
  IonButtons,
  IonBackButton,
  IonAlert,
  IonToast,
  IonModal, IonFooter,
  IonSpinner,
} from '@ionic/vue';
import {
  personOutline,
  mailOutline,
  lockClosedOutline,
  callOutline,
  globeOutline,
  businessOutline,
  briefcaseOutline,
  peopleOutline,
  peopleCircleOutline,
  idCardOutline,
  personAddOutline,
  arrowForward,
  qrCodeOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { api } from '@/services/api';
import { useUserStore } from '@/stores/user';
import QrCodeScanner from '@/components/QrCodeScanner.vue';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const showConfirmAlert = ref(false);
const toastMessage = ref('');
const emailExists = ref(false);
const emailError = ref('');
const emailMismatch = ref(false);
const passwordError = ref('');
const passwordMismatch = ref(false);
const usernameExists = ref(false);
const usernameError = ref('');
const showQrScanner = ref(false);
const showTermsModal = ref(false);
const referralError = ref(false);
const referralValid = ref(false);
const referrerInfo = ref<{ fullName: string; username: string } | null>(null);
const isReferralCodeFromUrl = ref(false);
const isSubmitting = ref(false);
const isLoadingTerms = ref(false);
const termsContent = ref('');

// Default form data for testing
const defaultFormData = {
  //role: 'free' as 'free' | 'merchant' | 'president',
  role: 'merchant' as 'free' | 'merchant' | 'president',
  username: '',
  fullName: '',
  email: '',
  confirmEmail: '',
  countryCode: '+852',
  phoneNumber: '',
  password: '',
  confirmPassword: '',
  referralCode: '',
  industry: '',
  otherIndustry: '',
  companyName: ''
};

const formData = ref({ ...defaultFormData });

const isMerchantOrPresident = computed(() => {
  return formData.value.role === 'merchant' || formData.value.role === 'president';
});

const registrationFee = computed(() => {
  switch (formData.value.role) {
    case 'merchant':
      return 6000;
    case 'president':
      return 12000;
    default:
      return 0;
  }
});

const isFormValid = computed(() => {
  const emailsMatch = !formData.value.email || formData.value.email === formData.value.confirmEmail;
  const passwordsMatch = formData.value.password === formData.value.confirmPassword;
  const passwordLongEnough = !formData.value.password || formData.value.password.length >= 6;
  const usernameValid = !usernameError.value && !usernameExists.value;
  const emailValid = !emailError.value && !emailExists.value;

  // Additional validation for merchant/president roles
  //const businessFieldsValid = !isMerchantOrPresident.value || (formData.value.industry && formData.value.companyName);

  return emailsMatch && passwordsMatch && passwordLongEnough &&
         !emailMismatch.value && !passwordMismatch.value &&
         emailValid && !passwordError.value &&
         usernameValid;
    //&& businessFieldsValid
    //&& !emailExists.value
});

// Function to fetch terms and conditions from settings table
const fetchTermsAndConditions = async () => {
  isLoadingTerms.value = true;
  try {
    const { data, error } = await supabase
      .from('settings')
      .select('value')
      .eq('key', 'registration_terms')
      .single();

    if (error) throw error;

    if (data && data.value) {
      termsContent.value = data.value;
    } else {
      console.error('Terms and conditions not found in settings');
      termsContent.value = '無法載入註冊條款。請稍後再試。';
    }
  } catch (error) {
    console.error('Error fetching terms and conditions:', error);
    termsContent.value = '無法載入註冊條款。請稍後再試。';
  } finally {
    isLoadingTerms.value = false;
  }
};

onMounted(() => {
  // Check for referral code in URL
  const code = route.query.code as string;
  if (code) {
    formData.value.referralCode = code;
    isReferralCodeFromUrl.value = true;
    verifyReferralCode();
  }

  // Fetch terms and conditions
  fetchTermsAndConditions();
});

const handleRoleChange = () => {
  if (!isMerchantOrPresident.value) {
    formData.value.industry = '';
    formData.value.otherIndustry = '';
    formData.value.companyName = '';
  }
};

const handleIndustryChange = () => {
  // Reset otherIndustry when industry is not "其他"
  if (formData.value.industry !== '其他') {
    formData.value.otherIndustry = '';
  }
};

const checkUsername = async () => {
  if (!formData.value.username) return;

  // First validate username format
  validateUsername();

  // If username format is invalid, don't check with Supabase
  if (usernameError.value) return;

  try {
    // Trim and convert to lowercase
    const username = formData.value.username.trim().toLowerCase();

    const { data, error } = await supabase
      .from('users')
      .select('username')
      .eq('username', username)
      .maybeSingle();

    if (error) throw error;

    usernameExists.value = !!data;
    // Only set error message if not already set by validateUsername
    if (usernameExists.value && !usernameError.value) {
      usernameError.value = '此帳號已被註冊';
    }
  } catch (error) {
    console.error('Error checking username:', error);
    usernameError.value = '檢查帳號時發生錯誤';
  }
};

const checkEmail = async () => {
  if (!formData.value.email) return;

  // First validate email format
  validateEmails();

  // If email format is invalid, don't check with Supabase
  if (emailError.value) return;

  try {
    const { data, error } = await supabase
      .from('users')
      .select('email')
      .eq('email', formData.value.email)
      .maybeSingle();

    if (error) throw error;

    emailExists.value = !!data;
    // Only set error message if not already set by validateEmails
    if (emailExists.value && !emailError.value) {
      emailError.value = '此電郵地址已被註冊';
    }
  } catch (error) {
    console.error('Error checking email:', error);
    emailError.value = '檢查電郵時發生錯誤';
  }
};

const validateUsername = () => {
  // Clear previous errors
  usernameError.value = '';
  usernameExists.value = false;

  // Check if username is provided
  if (!formData.value.username) return;

  // Trim the username
  const username = formData.value.username.trim();

  // Check username length
  if (username.length < 3) {
    usernameError.value = '帳號長度至少需要3個字符';
    return;
  }

  // Check for valid characters (letters, numbers, underscores, hyphens)
  const usernameRegex = /^[a-zA-Z0-9_-]+$/;
  if (!usernameRegex.test(username)) {
    usernameError.value = '帳號只能包含字母、數字、底線和連字符';
    return;
  }
};

const validateEmails = () => {
  // Clear previous errors
  emailMismatch.value = false;
  emailError.value = '';

  // Check if both email fields have values
  if (formData.value.email && formData.value.confirmEmail) {
    // Check if emails match
    if (formData.value.email !== formData.value.confirmEmail) {
      emailMismatch.value = true;
    }
  }

  // More robust email format validation
  if (formData.value.email) {
    // This regex is more comprehensive for email validation
    const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!emailRegex.test(formData.value.email)) {
      emailError.value = '請輸入有效的電郵地址';
    }
  }
};



const validatePasswords = () => {
  // Clear previous errors
  passwordError.value = '';
  passwordMismatch.value = false;

  // Check if both password fields have values
  if (formData.value.password && formData.value.confirmPassword) {
    // Check if passwords match
    if (formData.value.password !== formData.value.confirmPassword) {
      passwordMismatch.value = true;
    }
  }

  // Check password strength if needed
  if (formData.value.password) {
    if (formData.value.password.length < 6) {
      passwordError.value = '密碼長度至少需要6個字符';
    }
  }
};

const verifyReferralCode = async () => {
  console.log(formData.value.referralCode);
  if (!formData.value.referralCode) return;

  try {
    const referrer = await api.verifyReferralCode(formData.value.referralCode);
    console.log(referrer);
    if (referrer) {
      referrerInfo.value = {
        fullName: referrer.fullName,
        username: referrer.username
      };
      referralValid.value = true;
      referralError.value = false;
    } else {
      referrerInfo.value = null;
      referralValid.value = false;
      referralError.value = true;
    }
  } catch (error) {
    console.error('Error verifying referral code:', error);
    referrerInfo.value = null;
    referralValid.value = false;
    referralError.value = true;
  }
};

// Create debounced versions of functions
const debouncedVerifyReferralCode = useDebounceFn(verifyReferralCode, 1000);
const debouncedCheckUsername = useDebounceFn(checkUsername, 500);

// Add watch for referralCode
watch(() => formData.value.referralCode, (newValue) => {
  if (newValue) {
    // Use debounced version for typing
    debouncedVerifyReferralCode();
  } else {
    referralValid.value = false;
    referralError.value = false;
    referrerInfo.value = null;
  }
});

// Add watch for username
watch(() => formData.value.username, (newValue) => {
  if (newValue && newValue.length >= 3) {
    // Use debounced version for typing
    debouncedCheckUsername();
  } else {
    usernameExists.value = false;
  }
});

const handleQrCodeScanned = async (code: string) => {
  formData.value.referralCode = code;
  showQrScanner.value = false;
  // Use non-debounced version for QR code scan
  await verifyReferralCode();
};

const handleSubmit = async (e: Event) => {
  e.preventDefault();

  // Validate all fields
  validateUsername();
  validateEmails();
  validatePasswords();

  // Check if username and email exist
  await checkUsername();
  await checkEmail();

  // Check if email is valid
  if (emailError.value) {
    toastMessage.value = emailError.value;
    return;
  }

  if (!isFormValid.value) {
    // Don't proceed if form is invalid
    return;
  }

  formData.value.email = formData.value.email.trim().toLowerCase();
  formData.value.username = formData.value.username.trim().toLowerCase();

  if (emailExists.value) {
    toastMessage.value = '此電郵地址已被註冊';
    return;
  }

  if (usernameExists.value) {
    toastMessage.value = '此帳號已被註冊';
    return;
  }

  showConfirmAlert.value = true;
};

const confirmationMessage = computed(() => {
  const referrer = referrerInfo.value ? `${referrerInfo.value?.fullName} (${referrerInfo.value?.username})` : '公司';
  const industryDisplay = formData.value.industry === '其他' ? `${formData.value.industry} - ${formData.value.otherIndustry}` : formData.value.industry;

  /*const msg = `請確認以下註冊資料：<br /><br />
會員類型：${formData.value.role === 'free' ? '普通免費會員' : formData.value.role === 'merchant' ? '商家會員' : '分會長'}<br />
姓名：${formData.value.fullName}<br />
帳號：${formData.value.username}<br />
電郵：${formData.value.email}<br />
電話：${formData.value.countryCode}${formData.value.phoneNumber}<br />
${isMerchantOrPresident.value ? `行業：${industryDisplay}<br />
公司名稱：${formData.value.companyName}<br />` : ''}
推薦人：${referrer}<br /><br />
<strong>點擊確定，即表示您同意我們的註冊條款</strong>`;*/

  return `請確認以下註冊資料：<br /><br />
姓名：${formData.value.fullName}<br />
帳號：${formData.value.username}<br />
電郵：${formData.value.email}<br />
電話：${formData.value.countryCode}${formData.value.phoneNumber}<br /><br />
<strong>點擊確定，即表示您同意我們的註冊條款</strong>`;
});

const submitRegistration = async () => {
  if (isSubmitting.value) return;

  // Validate passwords one more time
  validatePasswords();

  if (passwordMismatch.value || passwordError.value) {
    toastMessage.value = passwordError.value || '密碼不匹配';
    return;
  }

  try {
    isSubmitting.value = true;

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: formData.value.email,
      password: formData.value.password,
      options: {
        data: {
          username: formData.value.username,
          full_name: formData.value.fullName,
          phone: `${formData.value.countryCode}${formData.value.phoneNumber}`,
          role: formData.value.role,
          referral_code: formData.value.referralCode,
          industry: formData.value.industry === '其他' ? `${formData.value.industry} - ${formData.value.otherIndustry}` : formData.value.industry || null,
          company_name: formData.value.companyName || null
        }
      }
    });

    if (authError) {
      if (authError.message === 'User already registered') {
        toastMessage.value = '此電郵地址已被註冊';
      } else {
        toastMessage.value = authError.message || '註冊失敗，請稍後再試';
      }
      return;
    }

    if (!authData.user) {
      throw new Error('No user returned from signup');
    }

    // Wait a moment for the trigger to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Try to sign in immediately after registration
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: formData.value.email,
      password: formData.value.password
    });

    if (signInError) {
      console.error('Auto-login failed:', signInError);
      // Still show success but redirect to login
      toastMessage.value = '註冊成功！請登入繼續。';
      router.push('/login');
      return;
    }

    // If login successful, redirect to home
    toastMessage.value = '註冊成功！';
    router.push('/home');
  } catch (error: any) {
    console.error('Registration error:', error);
    toastMessage.value = error.message.includes('already registered')
      ? '此電郵地址已被註冊'
      : '註冊失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  /*align-items: center;*/
  justify-content: center;
  padding: 0;
}

.auth-card {
  width: 100%;
  max-width: 500px;
  border-radius: 24px;
  padding: 1rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  background: white;
}

.brand-section {
  text-align: center;
  margin-bottom: 2.5rem;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--ion-color-primary);
  margin: 0;
  line-height: 1.2;
}

.brand-subtitle {
  color: var(--ion-color-medium);
  font-size: 1.1rem;
  margin-top: 0.5rem;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-text-color);
}

.form-header p {
  color: var(--ion-color-medium);
  margin: 0.5rem 0 0;
}

.form-section {
  margin-bottom: 1.5rem;
}

.custom-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  --background: transparent;
  margin-bottom: 1.5rem;
  --border-color: var(--ion-color-medium);
  --border-style: solid;
  --border-width: 1px;
  --border-radius: 8px;
  --highlight-height: 0;
}

.custom-item::part(native) {
  padding: 0.75rem 1rem;
}

ion-input, ion-select {
  --padding-start: 2.5rem;
  font-size: 1rem;
}

.form-icon {
  font-size: 1.25rem;
  color: var(--ion-color-medium);
  margin-right: 0.5rem;
}

.fee-notice {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--ion-color-primary-contrast);
  border: 2px solid var(--ion-color-primary);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.fee-icon {
  font-size: 1.5rem;
  color: var(--ion-color-primary);
}

.fee-content {
  display: flex;
  flex-direction: column;
}

.fee-label {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.fee-amount {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.terms-section {
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.terms-notice {
  text-align: center;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  margin: 0;
}

.terms-content {
  padding: 1rem;
  max-height: 100%;
  overflow-y: auto;
}

.terms-content h2 {
  color: var(--ion-color-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.terms-content p {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.terms-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.terms-loading p {
  margin-top: 1rem;
  color: var(--ion-color-medium);
}

.terms-error {
  text-align: center;
  padding: 2rem;
  color: var(--ion-color-danger);
}

.form-actions {
  margin-top: 2rem;
}

.submit-button {
  --background: var(--ion-color-primary);
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
  font-weight: 600;
  font-size: 1.1rem;
  height: 48px;
  margin: 0;
  text-transform: none;
}

.submit-button ion-spinner {
  margin-right: 8px;
}

.login-prompt {
  margin-top: 1.5rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.login-prompt span {
  color: var(--ion-color-medium);
}

.login-link {
  --color: var(--ion-color-primary);
  font-weight: 600;
  text-transform: none;
}

.submit-button:hover {
  --background: var(--ion-color-primary-shade);
}

.custom-item:hover {
  --border-color: var(--ion-color-primary);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-card {
  animation: fadeInUp 0.6s ease-out;
}

.brand-section {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.form-header {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.register-form ion-item {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.form-actions {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* Error message styling */
ion-note[slot="error"] {
  color: var(--ion-color-danger);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block !important;
  padding-left: 2.5rem;
  position: relative;
  z-index: 10;
}

/* Add custom error message class for more control */
.error-message {
  color: var(--ion-color-danger);
  font-size: 0.85rem;
  margin-top: -1.5rem;
  margin-bottom: 0;
  display: block;
  padding-left: 2.5rem;
  font-weight: 500;
  animation: fadeIn 0.3s ease-in;
  background-color: rgba(var(--ion-color-danger-rgb), 0.05);
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border-radius: 4px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.ion-invalid {
  --border-color: var(--ion-color-danger) !important;
}
</style>