<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/material-requests"></ion-back-button>
        </ion-buttons>
        <ion-title>發起吹雞</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <form @submit.prevent="submitRequest" class="request-form">
        <div class="form-section">
          <h3>物料類型</h3>
          <div class="category-grid">
            <div 
              v-for="category in categories" 
              :key="category.value"
              class="category-option"
              :class="{ active: form.category === category.value }"
              @click="form.category = category.value"
            >
              <ion-icon :icon="category.icon"></ion-icon>
              <span>{{ category.label }}</span>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3>物料詳情</h3>
          <ion-item>
            <ion-input
              v-model="form.title"
              label="物料名稱"
              label-placement="stacked"
              placeholder="例如：鋼筋、水泥、木材等"
              required
            ></ion-input>
          </ion-item>
          
          <ion-item>
            <ion-textarea
              v-model="form.description"
              label="詳細描述"
              label-placement="stacked"
              placeholder="請詳細描述所需物料的規格、數量、用途等"
              :rows="4"
              required
            ></ion-textarea>
          </ion-item>
        </div>

        <div class="form-section">
          <h3>條件偏好</h3>
          <ion-item>
            <ion-select
              v-model="form.condition_preference"
              label="物料狀況"
              label-placement="stacked"
              placeholder="選擇偏好的物料狀況"
            >
              <ion-select-option value="new">全新</ion-select-option>
              <ion-select-option value="like_new">近乎全新</ion-select-option>
              <ion-select-option value="good">良好</ion-select-option>
              <ion-select-option value="fair">尚可</ion-select-option>
              <ion-select-option value="any">不限</ion-select-option>
            </ion-select>
          </ion-item>

          <ion-item>
            <ion-input
              v-model.number="form.budget"
              label="預算 (HK$)"
              label-placement="stacked"
              type="number"
              placeholder="0"
              min="0"
            ></ion-input>
          </ion-item>
        </div>

        <div class="form-section">
          <h3>地區</h3>
          <ion-item>
            <ion-select
              v-model="form.district_id"
              label="所在地區"
              label-placement="stacked"
              placeholder="選擇地區"
              required
            >
              <ion-select-option 
                v-for="district in districts" 
                :key="district.id" 
                :value="district.id"
              >
                {{ district.name }}
              </ion-select-option>
            </ion-select>
          </ion-item>
        </div>

        <div class="form-section">
          <h3>緊急程度</h3>
          <div class="urgency-options">
            <ion-radio-group v-model="form.urgency">
              <ion-item>
                <ion-radio slot="start" value="low"></ion-radio>
                <ion-label>
                  <h3>不急</h3>
                  <p>1-2週內</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-radio slot="start" value="medium"></ion-radio>
                <ion-label>
                  <h3>一般</h3>
                  <p>3-7天內</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-radio slot="start" value="high"></ion-radio>
                <ion-label>
                  <h3>緊急</h3>
                  <p>1-2天內</p>
                </ion-label>
              </ion-item>
            </ion-radio-group>
          </div>
        </div>

        <div class="form-actions">
          <ion-button 
            expand="block" 
            type="submit" 
            :disabled="!isFormValid || isSubmitting"
          >
            <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
            {{ isSubmitting ? '發佈中...' : '發佈吹雞' }}
          </ion-button>
        </div>
      </form>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonItem,
  IonInput,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonRadioGroup,
  IonRadio,
  IonLabel,
  IonButton,
  IonIcon,
  IonSpinner,
  IonToast,
} from '@ionic/vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useDistrictsStore } from '@/stores/districts';
import { useMaterialRequestsStore } from '@/stores/materialRequests';
import {
  hammerOutline,
  constructOutline,
  cubeOutline,
  flashOutline,
  buildOutline,
} from 'ionicons/icons';

const router = useRouter();
const authStore = useAuthStore();
const districtsStore = useDistrictsStore();
const materialRequestsStore = useMaterialRequestsStore();

const categories = [
  { value: 'steel', label: '鋼材', icon: buildOutline },
  { value: 'concrete', label: '混凝土', icon: cubeOutline },
  { value: 'wood', label: '木材', icon: constructOutline },
  { value: 'tools', label: '工具', icon: hammerOutline },
  { value: 'urgent', label: '緊急', icon: flashOutline },
];

const form = ref({
  title: '',
  description: '',
  category: '',
  condition_preference: '',
  budget: null as number | null,
  district_id: '',
  urgency: 'medium',
});

const isSubmitting = ref(false);
const toastMessage = ref('');

const districts = computed(() => districtsStore.districts);

const isFormValid = computed(() => {
  return form.value.title.trim() && 
         form.value.description.trim() && 
         form.value.category && 
         form.value.district_id;
});

const submitRequest = async () => {
  if (!isFormValid.value || isSubmitting.value) return;

  try {
    isSubmitting.value = true;
    
    await materialRequestsStore.createRequest({
      ...form.value,
      requester_id: authStore.currentUser?.id,
    });

    toastMessage.value = '吹雞發佈成功！';
    
    // Navigate back after short delay
    setTimeout(() => {
      router.push('/tabs/material-requests');
    }, 1500);
    
  } catch (error) {
    console.error('Error creating material request:', error);
    toastMessage.value = '發佈失敗，請重試';
  } finally {
    isSubmitting.value = false;
  }
};

onMounted(async () => {
  await districtsStore.fetchDistricts();
});
</script>

<style scoped>
.request-form {
  padding: 16px;
}

.form-section {
  margin-bottom: 24px;
}

.form-section h3 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--ion-color-dark);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.category-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border: 2px solid var(--ion-color-light);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.category-option.active {
  border-color: var(--ion-color-primary);
  background: var(--ion-color-primary-tint);
}

.category-option ion-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: var(--ion-color-medium);
}

.category-option.active ion-icon {
  color: var(--ion-color-primary);
}

.category-option span {
  font-size: 0.9rem;
  text-align: center;
}

.urgency-options ion-item {
  --padding-start: 0;
}

.form-actions {
  margin-top: 32px;
  padding-bottom: 32px;
}
</style>
