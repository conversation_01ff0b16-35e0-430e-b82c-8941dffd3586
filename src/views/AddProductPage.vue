<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button :default-href="`/shops/${shopId}`" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ isEditMode ? '編輯產品' : '新增產品' }}</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <form @submit.prevent="handleSubmit" class="product-form">
          <ion-list>
            <ion-item>
              <ion-label position="stacked">產品名稱 *</ion-label>
              <ion-input
                v-model="formData.title"
                type="text"
                required
                placeholder="請輸入產品名稱"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">產品類別 *</ion-label>
              <ion-select
                v-model="formData.category_id"
                interface="popover"
                placeholder="請選擇產品類別"
                required
              >
                <ion-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.title }}
                </ion-select-option>
              </ion-select>
            </ion-item>

            <div class="editor-container">
              <ion-label position="stacked">產品描述 *</ion-label>

              <!-- Create the editor container -->
              <div id="editor"></div>
            </div>


            <ion-item>
              <ion-label position="stacked">價格 (HK$) *</ion-label>
              <ion-input
                v-model="formData.price"
                type="number"
                required
                min="0"
                step="0.1"
                placeholder="請輸入價格"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label>有貨</ion-label>
              <ion-toggle v-model="formData.is_in_stock" slot="end"></ion-toggle>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">分潤比率 (%) *</ion-label>
              <ion-input
                v-model="formData.profit_sharing_rate"
                type="number"
                required
                min="0"
                max="100"
                step="0.1"
                placeholder="請輸入分潤比率"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">封面圖片 *</ion-label>
              <ion-button expand="block" @click="takeCoverPhoto" class="ion-margin-top">
                <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
                選擇封面圖片
              </ion-button>
              <p class="upload-hint">建議上傳正方形圖片以獲得最佳顯示效果</p>
              <div v-if="coverImagePreview || formData.cover_image" class="image-preview">
                <img :src="coverImagePreview || formData.cover_image" alt="Cover preview" />
              </div>
            </ion-item>
          </ion-list>

          <div class="section-title ion-padding-top">
            <h2>產品相片</h2>
            <ion-button size="small" fill="clear" @click="addProductPhoto">
              <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
              新增相片
            </ion-button>
          </div>
          <p class="upload-hint">建議上傳正方形圖片以獲得最佳顯示效果</p>

          <ion-list class="photo-list">
            <ion-reorder-group @ionItemReorder="handleReorder($event)" :disabled="false">
              <!-- Existing Photos -->
              <ion-item v-for="(photo, index) in existingPhotos" :key="photo.id" class="photo-item">
                <ion-thumbnail slot="start">
                  <img :src="photo.photo_url" :alt="`Product photo ${index + 1}`" class="square-thumbnail" />
                </ion-thumbnail>
                <ion-label>
                  <ion-input
                    v-model="photo.caption"
                    placeholder="輸入相片說明"
                    class="caption-input"
                  ></ion-input>
                </ion-label>
                <ion-button
                  fill="clear"
                  color="danger"
                  slot="end"
                  @click="removeExistingPhoto(photo.id, index)"
                >
                  <ion-icon :icon="trashOutline"></ion-icon>
                </ion-button>
                <ion-reorder slot="end">
                  <ion-icon :icon="menuOutline"></ion-icon>
                </ion-reorder>
              </ion-item>

              <!-- New Photos -->
              <ion-item v-for="(photo, index) in productPhotos" :key="index" class="photo-item">
                <ion-thumbnail slot="start">
                  <img :src="photo.preview" :alt="`Product photo ${index + 1}`" class="square-thumbnail" />
                </ion-thumbnail>
                <ion-label>
                  <div class="photo-info">
                    <ion-input
                      v-model="photo.caption"
                      placeholder="輸入相片說明"
                      class="caption-input"
                    ></ion-input>
                  </div>
                </ion-label>
                <ion-button
                  fill="clear"
                  color="danger"
                  slot="end"
                  @click="removePhoto(index)"
                >
                  <ion-icon :icon="trashOutline"></ion-icon>
                </ion-button>
                <ion-reorder slot="end">
                  <ion-icon :icon="menuOutline"></ion-icon>
                </ion-reorder>
              </ion-item>
            </ion-reorder-group>
          </ion-list>

          <div class="ion-padding">
            <ion-button
              type="submit"
              expand="block"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? '處理中...' : (isEditMode ? '更新產品' : '新增產品') }}
            </ion-button>
          </div>
        </form>
      </div>
    </ion-content>

    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-page>
</template>

<script setup lang="ts">
  import { ref, computed, onUnmounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    IonPage,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonList,
    IonItem,
    IonLabel,
    IonInput,
    IonButton,
    IonButtons,
    IonBackButton,
    IonToggle,
    IonThumbnail,
    IonReorderGroup,
    IonReorder,
    IonIcon,
    IonToast,
    onIonViewWillEnter,

    IonSelect,
    IonSelectOption,
    onIonViewDidEnter,
  } from '@ionic/vue';
  import {
    trashOutline,
    menuOutline,
    cameraOutline,
  } from 'ionicons/icons';
  import Quill from 'quill';
  import "quill/dist/quill.snow.css";
  import QuillResize from 'quill-resize-module';
  Quill.register('modules/resize', QuillResize);

  import { useAuthStore } from '@/stores/auth';
  import LoadingSpinner from '@/components/LoadingSpinner.vue';

  import { supabase } from '@/lib/supabase';
  import { uploadImages } from '@/lib/cloudflare';
  import { uploadImageToSupabase } from '@/lib/supabase-storage';
  import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';

  interface ProductPhoto {
    file?: File;
    preview: string;
    caption: string;
    base64Data?: string;
  }

  interface ExistingPhoto {
    id: string;
    photo_url: string;
    caption: string;
    order: number;
  }

  const route = useRoute();
  const router = useRouter();
  const authStore = useAuthStore();
  const shopId = route.params.shopId as string;
  const productId = route.params.productId as string;
  const toastMessage = ref('');
  const isSubmitting = ref(false);
  const isLoading = ref(true);

  // Initialize photo gallery
  const { takePhoto } = usePhotoGallery();

  const isEditMode = computed(() => !!productId);

  const formData = ref({
    title: '',
    description: '',
    price: 0,
    is_in_stock: true,
    profit_sharing_rate: 30,
    cover_image: '',
    category_id: null as number | null
  });

  const categories = ref<Array<{ id: number, title: string }>>([]);

  const coverImagePhoto = ref<Photo | null>(null);
  const coverImagePreview = ref<string | null>(null);
  const productPhotos = ref<ProductPhoto[]>([]);
  const existingPhotos = ref<ExistingPhoto[]>([]);
  const photosToDelete = ref<string[]>([]);

  // Quill editor instance
  let quillEditor: Quill | null = null;

  onIonViewWillEnter(async () => {
    if (!authStore.currentUser?.id) {
      router.push('/login');
      return;
    }
  });

  onIonViewDidEnter(async () => {
    isLoading.value = true;
    try {
      const { data: shop, error } = await supabase
        .from('shops')
        .select('owner_id')
        .eq('id', shopId)
        .single();

      if (error || !shop || shop.owner_id !== authStore.currentUser.id) {
        router.push(`/shops/${shopId}`);
        return;
      }

      // Fetch product categories
      await fetchCategories();

      if (isEditMode.value) {
        await loadProductData();
      }
    } finally {
      isLoading.value = false;

      setTimeout(() => {
        initializeQuillEditor();
      }, 100)
    }
  });

  // Custom image upload handler for Quill
  const handleImageUpload = async () => {
    try {
      // Use the photo gallery to take/select an image
      const photo = await takePhoto();
      if (!photo || !photo.base64Data) {
        return;
      }

      // Show loading state
      const range = quillEditor?.getSelection(true);
      if (range) {
        quillEditor?.insertText(range.index, '上傳中...', 'user');
      }

      // Upload image to Supabase storage
      const uploadedUrl = await uploadImageToSupabase({
        base64Data: photo.base64Data,
        filename: photo.filepath || `description_${Date.now()}.jpg`,
        mimeType: photo.mimeType || 'image/jpeg'
      }, 'products', 'descriptions');

      // Remove loading text and insert image
      if (range && quillEditor) {
        quillEditor.deleteText(range.index, 6); // Remove "上傳中..." text
        quillEditor.insertEmbed(range.index, 'image', uploadedUrl, 'user');
        quillEditor.setSelection(range.index + 1, 0);
      }

      console.log('Image uploaded and inserted:', uploadedUrl);
    } catch (error) {
      console.error('Error uploading image:', error);
      toastMessage.value = '圖片上傳失敗，請稍後再試';

      // Remove loading text if it exists
      const range = quillEditor?.getSelection();
      if (range && quillEditor) {
        const text = quillEditor.getText(range.index - 6, 6);
        if (text === '上傳中...') {
          quillEditor.deleteText(range.index - 6, 6);
        }
      }
    }
  };

  // Initialize Quill editor with custom image handler
  const initializeQuillEditor = () => {
    if (quillEditor) {
      return; // Already initialized
    }

    quillEditor = new Quill('#editor', {
      theme: 'snow',
      placeholder: '請輸入產品描述...',
      modules: {
        resize: {},
        toolbar: {
          container: [
            ['bold', 'italic', 'underline'],
            [{ 'header': [1, 2, false] }],
            ['link', 'blockquote', 'code-block', 'image', 'video'],
            [{ list: 'ordered' }, { list: 'bullet' }],
          ],
          handlers: {
            image: handleImageUpload
          }
        },
      },
    });

    // Set initial content if editing existing product
    if (formData.value.description) {
      quillEditor.root.innerHTML = formData.value.description;
    }

    // Listen for content changes and update formData
    quillEditor.on('text-change', () => {
      formData.value.description = quillEditor?.root.innerHTML || '';
    });

    console.log('Quill editor initialized');
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('id, title')
        .order('title');

      if (error) throw error;

      // Move "其他" category to the bottom
      if (data) {
        const otherCategory = data.find((cat: { id: number, title: string }) => cat.title === '其他');
        const filteredCategories = data.filter((cat: { id: number, title: string }) => cat.title !== '其他');

        // Sort the categories and add "其他" at the end if it exists
        categories.value = [
          ...filteredCategories,
          ...(otherCategory ? [otherCategory] : [])
        ];
      } else {
        categories.value = [];
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toastMessage.value = '載入產品類別時發生錯誤';
    }
  };

  const loadProductData = async () => {
    try {
      // Load product details
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', productId)
        .single();

      if (productError) throw productError;

      // Update form data
      formData.value = {
        title: product.title,
        description: product.description,
        price: product.price,
        is_in_stock: product.is_in_stock,
        profit_sharing_rate: product.profit_sharing_rate,
        cover_image: product.cover_image,
        category_id: product.category_id
      };

      // Update Quill editor content if it's already initialized
      if (quillEditor && product.description) {
        quillEditor.root.innerHTML = product.description;
      }

      // Load product photos
      const { data: photos, error: photosError } = await supabase
        .from('product_photos')
        .select('*')
        .eq('product_id', productId)
        .order('order', { ascending: true });

      if (photosError) throw photosError;
      existingPhotos.value = photos || [];
    } catch (error) {
      console.error('Error loading product:', error);
      toastMessage.value = '載入產品資料時發生錯誤';
    }
  };

  const takeCoverPhoto = async () => {
    try {
      const photo = await takePhoto();
      if (photo) {
        coverImagePhoto.value = photo;
        coverImagePreview.value = photo.base64Data || null;
      }
    } catch (error) {
      console.error('Error taking cover photo:', error);
      toastMessage.value = '無法獲取相片，請稍後再試';
    }
  };

  const addProductPhoto = async () => {
    try {
      const photo = await takePhoto();
      if (photo) {
        productPhotos.value.push({
          base64Data: photo.base64Data,
          preview: photo.base64Data || '',
          caption: ''
        });
      }
    } catch (error) {
      console.error('Error taking product photo:', error);
      toastMessage.value = '無法獲取相片，請稍後再試';
    }
  };

  const removePhoto = (index: number) => {
    const photo = productPhotos.value[index];
    URL.revokeObjectURL(photo.preview);
    productPhotos.value.splice(index, 1);
  };

  const removeExistingPhoto = (photoId: string, index: number) => {
    photosToDelete.value.push(photoId);
    existingPhotos.value.splice(index, 1);
  };

  const handleReorder = (event: CustomEvent) => {
    const { from, to } = event.detail;

    // Create a combined array of both existing and new photos
    const existingWithPreview = existingPhotos.value.map(photo => ({
      ...photo,
      preview: photo.photo_url,
      isExisting: true
    }));

    const newWithId = productPhotos.value.map((photo, idx) => ({
      ...photo,
      tempId: `new-${idx}`,
      isExisting: false
    }));

    // Combine arrays and handle reordering
    const allPhotos = [...existingWithPreview, ...newWithId];
    const itemMove = allPhotos.splice(from, 1)[0];
    allPhotos.splice(to, 0, itemMove);

    // Split back into separate arrays
    const existingFiltered = allPhotos
      .filter(photo => photo.isExisting)
      .map(({ isExisting, preview, ...rest }) => rest as ExistingPhoto);

    const newFiltered = allPhotos
      .filter(photo => !photo.isExisting)
      .map(photo => {
        // Extract only the properties we need for ProductPhoto
        return {
          file: (photo as any).file,
          preview: photo.preview,
          caption: photo.caption,
          base64Data: (photo as any).base64Data
        } as ProductPhoto;
      });

    existingPhotos.value = existingFiltered;
    productPhotos.value = newFiltered;

    event.detail.complete();
  };

  const handleSubmit = async () => {
    try {
      isSubmitting.value = true;
      console.log('Starting product submission process');

      // Upload cover image if changed
      let coverImageUrl = formData.value.cover_image;
      if (coverImagePhoto.value && coverImagePhoto.value.base64Data) {
        console.log('Uploading cover image from photo gallery');
        try {
          // Use the improved uploadImages function that handles base64 data directly
          const [uploadedUrl] = await uploadImages([{
            base64Data: coverImagePhoto.value.base64Data,
            filename: coverImagePhoto.value.filepath || `cover_${new Date().getTime()}.jpg`,
            mimeType: coverImagePhoto.value.mimeType || 'image/jpeg'
          }]);

          coverImageUrl = uploadedUrl;
          console.log('Cover image uploaded successfully:', coverImageUrl);
        } catch (uploadError) {
          console.error('Cover image upload failed:', uploadError);
          toastMessage.value = '封面圖片上傳失敗，請稍後再試';
          throw uploadError;
        }
      }

      const productData = {
        shop_id: shopId,
        title: formData.value.title,
        description: formData.value.description,
        price: formData.value.price,
        is_in_stock: formData.value.is_in_stock,
        profit_sharing_rate: formData.value.profit_sharing_rate,
        cover_image: coverImageUrl,
        category_id: formData.value.category_id,
        status: 'active',
        created_by: authStore.currentUser?.id
      };

      let updatedProductId = productId;

      if (isEditMode.value) {
        console.log('Updating existing product:', productId);
        // Update existing product
        const { error: updateError } = await supabase
          .from('products')
          .update(productData)
          .eq('id', productId);

        if (updateError) {
          console.error('Product update error:', updateError);
          throw updateError;
        }

        // Delete removed photos from database (trigger will handle Cloudflare deletion)
        if (photosToDelete.value.length > 0) {
          console.log("Deleting Photos:", photosToDelete.value);
          const { error: deleteError } = await supabase
            .from('product_photos')
            .delete()
            .in('id', photosToDelete.value);

          if (deleteError) {
            console.error('Photo deletion error:', deleteError);
            throw deleteError;
          }
        }

        console.log("Updating Photos...");

        // Update existing photos order and captions
        const updatePromises = existingPhotos.value.map((photo, index) =>
          supabase
            .from('product_photos')
            .update({ caption: photo.caption, order: index })
            .eq('id', photo.id)
        );

        try {
          await Promise.all(updatePromises);
          console.log('Existing photos updated successfully');
        } catch (updateError) {
          console.error('Error updating existing photos:', updateError);
          throw updateError;
        }
      } else {
        console.log('Creating new product');
        // Create new product
        const { data: product, error: productError } = await supabase
          .from('products')
          .insert(productData)
          .select()
          .single();

        if (productError) {
          console.error('Product creation error:', productError);
          throw productError;
        }

        updatedProductId = product.id;
        console.log('New product created with ID:', updatedProductId);
      }

      // Upload new photos if any
      if (productPhotos.value.length > 0) {
        console.log(`Uploading ${productPhotos.value.length} product photos`);

        try {
          // Prepare image inputs for upload
          const imageInputs = productPhotos.value
            .filter(photo => photo.base64Data)
            .map((photo, index) => ({
              base64Data: photo.base64Data,
              filename: `product_${new Date().getTime()}_${index}.jpg`,
              mimeType: 'image/jpeg'
            }));

          console.log(`Prepared ${imageInputs.length} photos for upload`);

          // Upload all photos at once with a single token
          const uploadedUrls = await uploadImages(imageInputs);
          console.log('Photos uploaded successfully:', uploadedUrls);

          // Insert all photo records in parallel
          const photoInserts = uploadedUrls.map((url: string, index: number) =>
            supabase
              .from('product_photos')
              .insert({
                product_id: updatedProductId,
                photo_url: url,
                caption: productPhotos.value[index].caption,
                order: existingPhotos.value.length + index
              })
          );

          await Promise.all(photoInserts);
          console.log('Photo records inserted successfully');
        } catch (photoError) {
          console.error('Error uploading or saving photos:', photoError);
          toastMessage.value = '產品相片上傳失敗，但產品基本資料已保存';
          // Continue with navigation despite photo upload failure
          router.replace(`/shops/${shopId}`);
          return;
        }
      }

      console.log('Product submission completed successfully');
      router.replace(`/shops/${shopId}`);
    } catch (error: any) {
      console.error('Error saving product:', error);
      // Provide more specific error message if available
      if (error.message) {
        toastMessage.value = `${isEditMode.value ? '更新' : '新增'}產品時發生錯誤: ${error.message}`;
      } else {
        toastMessage.value = isEditMode.value ? '更新產品時發生錯誤' : '新增產品時發生錯誤';
      }
    } finally {
      isSubmitting.value = false;
    }
  };

  // Cleanup Quill editor when component unmounts
  onUnmounted(() => {
    if (quillEditor) {
      quillEditor = null;
    }
  });
</script>

<style scoped>
.product-form {
  max-width: 800px;
  margin: 0 auto;
}

.editor-container {
  margin: 16px;
}

.editor-container ion-label {
  margin-bottom: 8px;
  display: block;
  color: var(--ion-color-dark);
  font-weight: 500;
}

.mobile-editor {
  margin-bottom: 20px;
  background: white;
}

.image-preview {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.image-preview img {
  max-width: 200px;
  height: auto;
  border-radius: 8px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-title h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.photo-list {
  background: var(--ion-color-light);
  border-radius: 8px;
}

.photo-item {
  --background: var(--ion-color-light);
  --border-radius: 8px;
  margin: 4px 0;
}

.photo-item:active {
  --background: var(--ion-color-light-shade);
}

ion-thumbnail {
  --size: 80px;
  margin-right: 1rem;
}

/* Thumbnail image styling now handled by global .square-thumbnail class */

ion-reorder {
  cursor: grab;
}

.photo-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.file-name {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin: 0;
}

.caption-input {
  --padding-start: 0;
  --padding-end: 0;
  font-size: 0.9rem;
}

:deep(.ql-container) {
  min-height: 200px;
  max-height: 400px;
}

:deep(.ql-editor) {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .product-form {
    margin: 0;
  }

  .editor-container {
    margin: 12px;
  }

  :deep(.ql-container) {
    min-height: 150px;
    max-height: 300px;
  }

  :deep(.ql-editor) {
    min-height: 150px;
    max-height: 300px;
  }

  .image-preview img {
    max-width: 150px;
  }

  ion-thumbnail {
    --size: 60px;
  }
}
</style>