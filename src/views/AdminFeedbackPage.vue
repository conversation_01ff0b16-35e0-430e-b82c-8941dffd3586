<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>用戶反饋管理</ion-title>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile"></ion-back-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <!-- Statistics Cards -->
        <div class="stats-container ion-padding" v-if="stats">
          <div class="stats-grid">
            <div class="stat-card">
              <h3>{{ stats.total_feedback }}</h3>
              <p>總反饋數</p>
            </div>
            <div class="stat-card pending">
              <h3>{{ stats.pending_feedback }}</h3>
              <p>待處理</p>
            </div>
            <div class="stat-card resolved">
              <h3>{{ stats.resolved_feedback }}</h3>
              <p>已解決</p>
            </div>
          </div>
        </div>

        <!-- Filter Toolbar -->
        <ion-toolbar class="filter-toolbar">
          <ion-segment v-model="selectedStatus" @ionChange="handleStatusFilter">
            <ion-segment-button value="all">
              <ion-label>全部</ion-label>
            </ion-segment-button>
            <ion-segment-button value="pending">
              <ion-label>待處理</ion-label>
            </ion-segment-button>
            <ion-segment-button value="in_progress">
              <ion-label>處理中</ion-label>
            </ion-segment-button>
            <ion-segment-button value="resolved">
              <ion-label>已解決</ion-label>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>

        <!-- Feedback List -->
        <div v-if="isLoading" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入反饋中...</p>
        </div>

        <ion-list v-else-if="filteredFeedback.length > 0">
          <ion-item-group v-for="feedback in filteredFeedback" :key="feedback.id">
            <ion-item button @click="openFeedbackDetail(feedback)">
              <div class="feedback-item">
                <div class="feedback-header">
                  <h3>{{ feedback.subject }}</h3>
                  <ion-badge :color="getStatusColor(feedback.status)">
                    {{ getStatusLabel(feedback.status) }}
                  </ion-badge>
                </div>
                <div class="feedback-meta">
                  <span class="category">{{ getCategoryLabel(feedback.category) }}</span>
                  <span class="date">{{ formatDate(feedback.created_at) }}</span>
                  <span v-if="!feedback.is_anonymous" class="user">
                    用戶ID: {{ feedback.user_id?.substring(0, 8) }}...
                  </span>
                  <span v-else class="anonymous">匿名</span>
                </div>
                <p class="feedback-preview">{{ feedback.description.substring(0, 100) }}...</p>
              </div>
              <ion-icon :icon="chevronForward" slot="end"></ion-icon>
            </ion-item>
          </ion-item-group>
        </ion-list>

        <div v-else class="empty-state ion-padding">
          <ion-icon :icon="chatboxEllipsesOutline" size="large"></ion-icon>
          <h3>暫無反饋</h3>
          <p>{{ selectedStatus === 'all' ? '還沒有用戶提交反饋' : '此狀態下沒有反饋' }}</p>
        </div>

        <!-- Feedback Detail Modal -->
        <ion-modal :is-open="showDetailModal" @didDismiss="showDetailModal = false">
          <ion-header>
            <ion-toolbar>
              <ion-title>反饋詳情</ion-title>
              <ion-buttons slot="end">
                <ion-button @click="showDetailModal = false">關閉</ion-button>
              </ion-buttons>
            </ion-toolbar>
          </ion-header>
          <ion-content v-if="selectedFeedback">
            <div class="ion-padding">
              <div class="detail-section">
                <h4>基本信息</h4>
                <ion-item>
                  <ion-label>
                    <h3>主題</h3>
                    <p>{{ selectedFeedback.subject }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>類型</h3>
                    <p>{{ getCategoryLabel(selectedFeedback.category) }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>狀態</h3>
                    <ion-badge :color="getStatusColor(selectedFeedback.status)">
                      {{ getStatusLabel(selectedFeedback.status) }}
                    </ion-badge>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>提交時間</h3>
                    <p>{{ formatDate(selectedFeedback.created_at) }}</p>
                  </ion-label>
                </ion-item>
                <ion-item v-if="!selectedFeedback.is_anonymous">
                  <ion-label>
                    <h3>用戶ID</h3>
                    <p>{{ selectedFeedback.user_id }}</p>
                  </ion-label>
                </ion-item>
                <ion-item v-if="selectedFeedback.contact_email">
                  <ion-label>
                    <h3>聯絡電郵</h3>
                    <p>{{ selectedFeedback.contact_email }}</p>
                  </ion-label>
                </ion-item>
              </div>

              <div class="detail-section">
                <h4>詳細描述</h4>
                <div class="description-content">
                  <p>{{ selectedFeedback.description }}</p>
                </div>
              </div>

              <div class="detail-section">
                <h4>管理操作</h4>
                <ion-item>
                  <ion-label position="stacked">更新狀態</ion-label>
                  <ion-select v-model="selectedFeedback.status" @ionChange="updateFeedbackStatus">
                    <ion-select-option value="pending">待處理</ion-select-option>
                    <ion-select-option value="in_progress">處理中</ion-select-option>
                    <ion-select-option value="resolved">已解決</ion-select-option>
                    <ion-select-option value="closed">已關閉</ion-select-option>
                  </ion-select>
                </ion-item>
                <ion-item>
                  <ion-label position="stacked">管理員備註</ion-label>
                  <ion-textarea
                    v-model="selectedFeedback.admin_notes"
                    placeholder="添加內部備註..."
                    :rows="3"
                  ></ion-textarea>
                </ion-item>
                <ion-button expand="block" @click="saveFeedbackChanges" :disabled="isSaving">
                  <ion-spinner v-if="isSaving" name="crescent" slot="start"></ion-spinner>
                  {{ isSaving ? '保存中...' : '保存更改' }}
                </ion-button>
              </div>
            </div>
          </ion-content>
        </ion-modal>

        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonList,
  IonItemGroup,
  IonItem,
  IonLabel,
  IonIcon,
  IonSpinner,
  IonBadge,
  IonSegment,
  IonSegmentButton,
  IonModal,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonToast,
} from '@ionic/vue';
import {
  chevronForward,
  chatboxEllipsesOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';

interface Feedback {
  id: string;
  user_id: string | null;
  category: string;
  subject: string;
  description: string;
  contact_email: string | null;
  is_anonymous: boolean;
  status: string;
  admin_notes: string | null;
  admin_response: string | null;
  created_at: string;
  updated_at: string;
}

interface FeedbackStats {
  total_feedback: number;
  pending_feedback: number;
  resolved_feedback: number;
  category_stats: Record<string, number>;
}

const feedbackList = ref<Feedback[]>([]);
const stats = ref<FeedbackStats | null>(null);
const isLoading = ref(true);
const selectedStatus = ref('all');
const showDetailModal = ref(false);
const selectedFeedback = ref<Feedback | null>(null);
const isSaving = ref(false);
const toastMessage = ref('');

const filteredFeedback = computed(() => {
  if (selectedStatus.value === 'all') {
    return feedbackList.value;
  }
  return feedbackList.value.filter(feedback => feedback.status === selectedStatus.value);
});

onMounted(async () => {
  await loadFeedback();
  await loadStats();
});

const loadFeedback = async () => {
  try {
    isLoading.value = true;
    const { data, error } = await supabase
      .from('user_feedback')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    feedbackList.value = data || [];
  } catch (error) {
    console.error('Error loading feedback:', error);
    toastMessage.value = '載入反饋失敗';
  } finally {
    isLoading.value = false;
  }
};

const loadStats = async () => {
  try {
    const { data, error } = await supabase.rpc('get_feedback_stats');
    if (error) throw error;
    if (data && data.length > 0) {
      stats.value = data[0];
    }
  } catch (error) {
    console.error('Error loading stats:', error);
  }
};

const handleStatusFilter = (event: CustomEvent) => {
  selectedStatus.value = event.detail.value;
};

const openFeedbackDetail = (feedback: Feedback) => {
  selectedFeedback.value = { ...feedback };
  showDetailModal.value = true;
};

const updateFeedbackStatus = async () => {
  // Status will be updated when saving
};

const saveFeedbackChanges = async () => {
  if (!selectedFeedback.value) return;

  try {
    isSaving.value = true;
    const { error } = await supabase
      .from('user_feedback')
      .update({
        status: selectedFeedback.value.status,
        admin_notes: selectedFeedback.value.admin_notes,
        updated_at: new Date().toISOString(),
      })
      .eq('id', selectedFeedback.value.id);

    if (error) throw error;

    // Update local data
    const index = feedbackList.value.findIndex(f => f.id === selectedFeedback.value!.id);
    if (index !== -1) {
      feedbackList.value[index] = { ...selectedFeedback.value };
    }

    toastMessage.value = '更改已保存';
    showDetailModal.value = false;
    await loadStats(); // Refresh stats
  } catch (error) {
    console.error('Error saving changes:', error);
    toastMessage.value = '保存失敗';
  } finally {
    isSaving.value = false;
  }
};

const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), 'yyyy/MM/dd HH:mm');
  } catch (error) {
    return dateString;
  }
};

const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    bug: '錯誤回報',
    feature: '功能建議',
    improvement: '改進建議',
    ui: '介面問題',
    performance: '效能問題',
    other: '其他',
  };
  return labels[category] || category;
};

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待處理',
    in_progress: '處理中',
    resolved: '已解決',
    closed: '已關閉',
  };
  return labels[status] || status;
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'warning',
    in_progress: 'primary',
    resolved: 'success',
    closed: 'medium',
  };
  return colors[status] || 'medium';
};
</script>

<style scoped>
.stats-container {
  background: var(--ion-color-light);
  margin-bottom: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--ion-color-primary);
}

.stat-card.pending h3 {
  color: var(--ion-color-warning);
}

.stat-card.resolved h3 {
  color: var(--ion-color-success);
}

.stat-card p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.filter-toolbar {
  --background: white;
  margin-bottom: 1rem;
}

.feedback-item {
  width: 100%;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.feedback-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.feedback-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.feedback-preview {
  margin: 0;
  color: var(--ion-color-step-600);
  font-size: 0.9rem;
  line-height: 1.4;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.empty-state ion-icon {
  color: var(--ion-color-medium);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--ion-color-medium);
  margin-bottom: 0.5rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem;
  color: var(--ion-color-primary);
  font-weight: 600;
}

.description-content {
  background: var(--ion-color-light);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 0.5rem;
}

.description-content p {
  margin: 0;
  line-height: 1.5;
  white-space: pre-wrap;
}
</style>
