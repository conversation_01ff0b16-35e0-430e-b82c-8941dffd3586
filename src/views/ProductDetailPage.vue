<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button :default-href="`/home`" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>{{ product?.title || '產品詳情' }}</ion-title>
        <ion-buttons slot="end">
          <!-- TODO: linked to user_faviorite_products
          <ion-button @click="toggleFavorite">
            <ion-icon :icon="isFavorite ? heart : heartOutline"></ion-icon>
          </ion-button>
          <ion-button>
            <ion-icon :icon="shareOutline"></ion-icon>
          </ion-button>-->
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else-if="product" class="page-container">
        <!-- Mobile Layout -->
        <div class="mobile-layout mobile-content">
          <!-- Image Gallery -->
          <div class="image-gallery">
            <div class="main-image">
              <img :src="selectedImage" :alt="product.title" />
              <div class="image-counter" v-if="allProductImages.length > 1">
                {{ currentImageIndex + 1 }}/{{ allProductImages.length }}
              </div>
            </div>
            <div class="thumbnails" v-if="allProductImages.length > 1">
              <div
                v-for="(image, index) in allProductImages"
                :key="index"
                class="thumbnail"
                :class="{ active: image.url === selectedImage }"
                @click="selectImage(image, index)"
              >
                <img :src="image.url" :alt="`${product.title} - ${image.caption || `Image ${index + 1}`}`" />
                <div class="caption-overlay" v-if="image.caption">{{ image.caption }}</div>
              </div>
            </div>
          </div>

          <!-- Quick Info -->
          <div class="quick-info">
            <div class="price-section">
              <div class="price">HK$ {{ product.price }}</div>
              <div class="stock-status" :class="{ 'out-of-stock': !product.is_in_stock }">
                {{ product.is_in_stock ? '有貨' : '缺貨' }}
              </div>
            </div>
            <h1>{{ product.title }}</h1>
            <div class="shop-info" @click="navigateToShop" role="button">
              <img :src="product.shops?.logo" alt="Shop logo" class="shop-logo"
                    v-if="product.shops?.logo">
              <span>{{ shopName }}</span>
              <ion-icon :icon="chevronForward" class="shop-link-icon"></ion-icon>
            </div>

            <!-- Product Info Details -->
            <div class="product-info-details">
              <div class="info-row">
                <span class="label">分類:</span>
                <span class="value">{{ product.category || '未分類' }}</span>
              </div>
              <div class="info-row">
                <span class="label">狀態:</span>
                <span class="value">{{ product.condition || '使用過' }}</span>
              </div>
              <div class="info-row">
                <span class="label">日期:</span>
                <span class="value">{{ formatDate(product.created_at) }}</span>
              </div>
            </div>

            <!-- Product Description -->
            <div class="product-description-mobile" v-if="product.description">
              <div v-html="product.description"></div>
            </div>

            <!-- Seller Rating Section -->
            <div class="seller-rating-section">
              <h3>賣家點評</h3>
              <div class="seller-info">
                <div class="seller-avatar">
                  <span>{{ getSellerInitial() }}</span>
                </div>
                <div class="seller-details">
                  <div class="seller-name">{{ shopName }}</div>
                  <div class="seller-rating">
                    <span class="rating-score">5.0</span>
                    <div class="rating-stars">
                      <ion-icon :icon="star" v-for="i in 5" :key="i"></ion-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Product Details -->
          <div class="product-details">
            <div class="section-title">產品詳情</div>
            <div class="product-description" v-html="product.description"></div>
          </div>

          <!-- Related Products -->
          <div class="related-products" v-if="relatedProducts.length > 0">
            <div class="section-title">相關產品</div>
            <div class="related-products-scroll">
              <ion-card 
                v-for="relatedProduct in relatedProducts" 
                :key="relatedProduct.id" 
                class="related-product-card" 
                button 
                @click="navigateToProduct(relatedProduct)"
              >
                <img :src="relatedProduct.cover_image" :alt="relatedProduct.title" />
                <ion-card-header>
                  <ion-card-title>{{ relatedProduct.title }}</ion-card-title>
                  <ion-card-subtitle>HK$ {{ relatedProduct.price }}</ion-card-subtitle>
                </ion-card-header>
              </ion-card>
            </div>
          </div>
        </div>

        <!-- Desktop Layout -->
        <div class="desktop-layout">
          <div class="desktop-gallery">
            <div class="main-image">
              <img :src="selectedImage" :alt="product.title" />
            </div>
            <div class="thumbnails-vertical" v-if="allProductImages.length > 1">
              <div
                v-for="(image, index) in allProductImages"
                :key="index"
                class="thumbnail"
                :class="{ active: image.url === selectedImage }"
                @click="selectImage(image, index)"
              >
                <img :src="image.url" :alt="`${product.title} - ${image.caption || `Image ${index + 1}`}`" />
                <div class="caption-overlay" v-if="image.caption">{{ image.caption }}</div>
              </div>
            </div>
          </div>
          
          <div class="desktop-info">
            <div class="product-header">
              <h1>{{ product.title }}</h1>
              <div class="shop-info" @click="navigateToShop" role="button">
                <img :src="product.shops?.logo" alt="Shop logo" class="shop-logo"
                      v-if="product.shops?.logo">
                <span>{{ shopName }}</span>
                <ion-icon :icon="chevronForward" class="shop-link-icon"></ion-icon>
              </div>
            </div>

            <div class="price-section">
              <div class="price">HK$ {{ product.price }}</div>
              <div class="stock-status" :class="{ 'out-of-stock': !product.is_in_stock }">
                {{ product.is_in_stock ? '有貨' : '缺貨' }}
              </div>
            </div>

            <div class="product-description" v-html="product.description"></div>

            <!--
            <div class="desktop-actions">
              <div class="quantity-selector">
                <ion-button
                  fill="clear"
                  :disabled="quantity <= 1"
                  @click="quantity = Math.max(1, quantity - 1)"
                >
                  <ion-icon :icon="removeOutline"></ion-icon>
                </ion-button>
                <ion-input
                  type="number"
                  v-model="quantity"
                  min="1"
                  class="quantity-input"
                ></ion-input>
                <ion-button
                  fill="clear"
                  @click="quantity++"
                >
                  <ion-icon :icon="addOutline"></ion-icon>
                </ion-button>
              </div>
              
              <ion-button
                expand="block"
                @click="addToCart"
                :disabled="!product.is_in_stock"
              >
                <ion-icon :icon="cartOutline" slot="start"></ion-icon>
                {{ product.is_in_stock ? '加入購物車' : '缺貨' }}
              </ion-button>
            </div>
            -->
          </div>
        </div>

        <!-- Fixed Bottom Action Bar -->
        <div class="bottom-action-bar">
          <ion-button
            fill="outline"
            expand="block"
            color="primary"
            @click="chatWithSeller"
            :disabled="!product.shops?.owner_id || product.shops?.owner_id === authStore.currentUser?.id"
            class="contact-button"
          >
            聯絡
          </ion-button>
          <ion-button
            fill="solid"
            expand="block"
            color="primary"
            @click="handlePurchase"
            :disabled="!product.is_in_stock || product.shops?.owner_id === authStore.currentUser?.id"
            class="purchase-button"
          >
            {{ product.is_free ? '索取' : '購買' }}
          </ion-button>
        </div>
      </div>

      <div v-else class="page-container">
        <div class="error-container">
          <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
          <p>找不到產品</p>
        </div>
      </div>

      <!-- Toast Messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonToast,
  IonInput,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  onIonViewWillEnter,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heartOutline,
  heart,
  cartOutline,
  alertCircleOutline,
  pencilOutline,
  trashOutline,
  menuOutline,
  eyeOutline,
  closeCircleOutline,
  addOutline,
  removeOutline,
  shareOutline,
  chevronForward,
  chatbubbleEllipsesOutline,
  star,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useCartStore } from '@/stores/cart';
import { useAuthStore } from '@/stores/auth';
import { useChatStore } from '@/stores/chat';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

interface ProductImage {
  url: string;
  caption?: string;
}

const route = useRoute();
const router = useRouter();
const cartStore = useCartStore();
const authStore = useAuthStore();
const chatStore = useChatStore();

const productId = route.params.productId as string;

const product = ref<any>(null);
const shopName = ref('');
const productPhotos = ref<any[]>([]);
const relatedProducts = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');
const quantity = ref(1);
const isFavorite = ref(false);
const selectedImage = ref<string>('');
const currentImageIndex = ref(0);

const allProductImages = computed(() => {
  if (!product.value) return [];
  const images: ProductImage[] = [{ url: product.value.cover_image }];
  productPhotos.value.forEach(photo => {
    images.push({
      url: photo.photo_url,
      caption: photo.caption
    });
  });
  return images;
});

onIonViewDidEnter(() => {
  loadProductData();
});

const loadProductData = async () => {
  try {
    isLoading.value = true;

    // Load product details
    const { data: productData, error: productError } = await supabase
      .from('products')
      .select(`
        *,
        shops (
          name,
          logo,
          owner_id
        )
      `)
      .eq('id', productId)
      .eq('status', 'active')
      .single();

    if (productError) throw productError;
    if (!productData) {
      router.push('/shop');
      return;
    }

    product.value = productData;
    shopName.value = productData.shops.name;
    selectedImage.value = productData.cover_image;

    // Load product photos with captions
    const { data: photos, error: photosError } = await supabase
      .from('product_photos')
      .select('*')
      .eq('product_id', productId)
      .order('order', { ascending: true });

    if (photosError) throw photosError;
    productPhotos.value = photos || [];

    // Load related products
    const { data: related, error: relatedError } = await supabase
      .from('products')
      .select('*')
      .eq('shop_id', product.value.shop_id)
      .eq('status', 'active')
      .neq('id', productId)
      .limit(10);

    if (relatedError) throw relatedError;
    relatedProducts.value = related || [];

  } catch (error) {
    console.error('Error loading product:', error);
    toastMessage.value = '載入產品資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

const selectImage = (image: ProductImage, index: number) => {
  selectedImage.value = image.url;
  currentImageIndex.value = index;
};

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  toastMessage.value = isFavorite.value ? '已加入收藏' : '已移除收藏';
};

const addToCart = () => {
  if (product.value && quantity.value > 0) {
    cartStore.addItem(product.value, quantity.value);
    toastMessage.value = '已加入購物車';
  }
};

const navigateToProduct = (relatedProduct: any) => {
  router.push(`/products/${relatedProduct.id}`);
};

const navigateToShop = () => {
  if (product.value?.shop_id) {
    router.push(`/shops/${product.value.shop_id}`);
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

const getSellerInitial = () => {
  if (shopName.value) {
    return shopName.value.charAt(0).toUpperCase();
  }
  return 'S';
};

const handlePurchase = async () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    router.push('/login');
    return;
  }

  if (product.value.is_free) {
    // For free items, start chat directly
    await chatWithSeller();
  } else {
    // For paid items, could implement purchase flow or also start chat
    await chatWithSeller();
  }
};

const chatWithSeller = async () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入以聊天';
    router.push('/login');
    return;
  }

  if (!product.value?.shops?.owner_id) {
    toastMessage.value = '無法找到賣家資訊';
    return;
  }

  if (product.value.shops.owner_id === authStore.currentUser?.id) {
    toastMessage.value = '這是您自己的物件';
    return;
  }

  try {
    // Create or get existing product-based conversation with the seller
    const conversation = await chatStore.getOrCreateProductConversation(
      product.value.shops.owner_id,
      product.value.id
    );

    if (conversation) {
      // Navigate to chat with pre-filled message (don't auto-send)
      const prefilledMessage = `我對您的物件「${product.value.title}」有興趣，請問還有貨嗎？`;

      // Store product context for the chat
      const productContext = {
        id: product.value.id,
        title: product.value.title,
        price: product.value.price,
        cover_image: product.value.cover_image,
        shop_id: product.value.shop_id,
        shop_name: shopName.value
      };

      // Navigate to chat with query parameters for pre-filled message and product context
      router.push({
        path: `/chat/${conversation.id}`,
        query: {
          prefill: prefilledMessage,
          productContext: JSON.stringify(productContext)
        }
      });
    } else {
      toastMessage.value = '無法建立聊天室，請稍後再試';
    }
  } catch (error) {
    console.error('Error starting chat:', error);
    toastMessage.value = '建立聊天室時發生錯誤';
  }
};
</script>

<style scoped>
.shop-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-medium);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.shop-info:hover {
  background-color: var(--ion-color-light);
}

/* Product Info Details */
.product-info-details {
  padding: 16px;
  background: var(--ion-color-light);
  margin: 16px 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--ion-color-light-shade);
}

.info-row:last-child {
  border-bottom: none;
}

.info-row .label {
  font-weight: 500;
  color: var(--ion-color-medium);
}

.info-row .value {
  color: var(--ion-color-dark);
}

/* Product Description Mobile */
.product-description-mobile {
  padding: 16px;
  margin: 16px 0;
}

.product-description-mobile h1,
.product-description-mobile h2,
.product-description-mobile h3 {
  margin-top: 16px;
  margin-bottom: 8px;
}

/* Seller Rating Section */
.seller-rating-section {
  padding: 16px;
  margin: 16px 0;
  background: var(--ion-color-light);
}

.seller-rating-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
}

.seller-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.seller-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--ion-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.seller-details {
  flex: 1;
}

.seller-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.seller-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-score {
  font-weight: 600;
  color: var(--ion-color-warning);
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars ion-icon {
  color: var(--ion-color-warning);
  font-size: 14px;
}

/* Fixed Bottom Action Bar */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid var(--ion-color-light-shade);
  display: flex;
  gap: 12px;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.contact-button {
  flex: 1;
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
}

.purchase-button {
  flex: 1;
  --background: var(--ion-color-primary);
}

/* Add bottom padding to content to account for fixed bar */
.mobile-content {
  padding-bottom: 80px;
}

.shop-link-icon {
  font-size: 16px;
  color: var(--ion-color-medium);
}

/* Mobile-first styles */
.mobile-layout {
  display: block;
}

.desktop-layout {
  display: none;
}

.image-gallery {
  position: relative;
  background: var(--ion-color-light);
}

.main-image {
  position: relative;
  width: 100%;
  height: 300px;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-counter {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.thumbnails {
  display: flex;
  gap: 8px;
  padding: 8px;
  overflow-x: auto;
  background: white;
}

.thumbnail {
  position: relative;
  flex: 0 0 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid transparent;
  cursor: pointer;
}

.thumbnail.active {
  border-color: var(--ion-color-primary);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.caption-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 4px;
  font-size: 0.7rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.quick-info {
  padding: 16px;
  background: white;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.price {
  font-size: 24px;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.stock-status {
  font-size: 14px;
  color: var(--ion-color-success);
}

.stock-status.out-of-stock {
  color: var(--ion-color-medium);
}

.quick-info h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px;
  line-height: 1.4;
}

.shop-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.product-details {
  margin-top: 8px;
  padding: 16px;
  background: white;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--ion-color-light);
}

.product-description {
  font-size: 14px;
  line-height: 1.6;
  color: var(--ion-color-dark);
}

.related-products {
  margin-top: 8px;
  padding: 16px;
  background: white;
}

.related-products-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 4px 0;
}

.related-product-card {
  flex: 0 0 140px;
  margin: 0;
  border-radius: 8px;
}

.related-product-card img {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
}

.related-product-card ion-card-header {
  padding: 8px;
}

.related-product-card ion-card-title {
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-product-card ion-card-subtitle {
  font-size: 14px;
  color: var(--ion-color-primary);
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: white;
  border-top: 1px solid var(--ion-color-light);
  z-index: 100;
}

.quantity-selector {
  display: flex;
  align-items: center;
  background: var(--ion-color-light);
  border-radius: 8px;
  padding: 0 4px;
}

.quantity-input {
  width: 40px;
  text-align: center;
  --padding-start: 0;
  --padding-end: 0;
}

ion-button[expand="block"] {
  margin: 0;
  flex: 1;
}

/* Add padding to content to account for bottom bar */
ion-content::part(scroll) {
  padding-bottom: 80px;
}

/* Desktop styles */
@media (min-width: 768px) {
  .mobile-layout {
    display: none;
  }

  .desktop-layout {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    gap: 48px;
  }

  .desktop-gallery {
    flex: 1;
    display: flex;
    gap: 16px;
  }

  .desktop-gallery .main-image {
    flex: 1;
    height: 500px;
    background: var(--ion-color-light);
    border-radius: 16px;
    overflow: hidden;
  }

  .thumbnails-vertical {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 80px;
  }

  .thumbnails-vertical .thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    cursor: pointer;
  }

  .thumbnails-vertical .thumbnail.active {
    border-color: var(--ion-color-primary);
  }

  .desktop-info {
    flex: 1;
    max-width: 500px;
    padding: 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .product-header {
    margin-bottom: 24px;
  }

  .product-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 16px;
    line-height: 1.4;
  }

  .desktop-info .price-section {
    margin-bottom: 24px;
  }

  .desktop-info .price {
    font-size: 32px;
  }

  .desktop-info .product-description {
    margin-bottom: 32px;
    font-size: 16px;
  }

  .desktop-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .desktop-actions .quantity-selector {
    align-self: flex-start;
  }

  .desktop-actions ion-button[expand="block"] {
    height: 48px;
    font-size: 16px;
  }

  /* Hide bottom bar on desktop */
  .bottom-bar {
    display: none;
  }

  /* Remove bottom padding since bottom bar is hidden */
  ion-content::part(scroll) {
    padding-bottom: 0;
  }
}

/* Hide scrollbars */
.thumbnails::-webkit-scrollbar,
.related-products-scroll::-webkit-scrollbar {
  display: none;
}

.thumbnails,
.related-products-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.loading-container,
.error-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}
</style>