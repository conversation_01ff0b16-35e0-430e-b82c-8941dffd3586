<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>通知測試</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <ion-card>
        <ion-card-header>
          <ion-card-title>OneSignal 狀態</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item lines="none">
            <ion-label>
              <h3>用戶 ID</h3>
              <p>{{ authStore.currentUser?.id || '未登入' }}</p>
            </ion-label>
          </ion-item>
          <ion-item lines="none">
            <ion-label>
              <h3>OneSignal Player ID</h3>
              <p>{{ playerIdStatus }}</p>
            </ion-label>
          </ion-item>
          <ion-button @click="checkOneSignalStatus" fill="outline" expand="block">
            檢查 OneSignal 狀態
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>通知隊列狀態</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div v-if="queueStatus">
            <ion-item lines="none">
              <ion-label>
                <h3>總計: {{ queueStatus.total }}</h3>
                <p>已處理: {{ queueStatus.processed }} | 待處理: {{ queueStatus.pending }}</p>
              </ion-label>
            </ion-item>
          </div>
          <ion-button @click="checkQueueStatus" fill="outline" expand="block">
            檢查隊列狀態
          </ion-button>
          <ion-button @click="processQueue" fill="outline" expand="block" color="secondary">
            手動處理隊列
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>測試通知</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-label position="stacked">測試訊息</ion-label>
            <ion-input v-model="testMessage" placeholder="輸入測試訊息"></ion-input>
          </ion-item>
          <ion-button 
            @click="sendTestMessage" 
            expand="block" 
            :disabled="!testMessage.trim() || isSending"
            color="primary"
          >
            <ion-spinner v-if="isSending" name="crescent"></ion-spinner>
            <span v-else>發送測試訊息</span>
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card v-if="logs.length > 0">
        <ion-card-header>
          <ion-card-title>日誌</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div v-for="(log, index) in logs" :key="index" class="log-entry">
            <small>{{ log.timestamp }}</small>
            <p :class="log.type">{{ log.message }}</p>
          </div>
          <ion-button @click="clearLogs" fill="clear" size="small">清除日誌</ion-button>
        </ion-card-content>
      </ion-card>
    </ion-content>

    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonSpinner,
  IonToast
} from '@ionic/vue';
import { useAuthStore } from '@/stores/auth';
import { useChatStore } from '@/stores/chat';
import { notificationService } from '@/services/notificationService';
import OneSignal from 'onesignal-cordova-plugin';

const authStore = useAuthStore();
const chatStore = useChatStore();

// Component state
const playerIdStatus = ref('檢查中...');
const queueStatus = ref<any>(null);
const testMessage = ref('這是一條測試訊息');
const isSending = ref(false);
const toastMessage = ref('');
const logs = ref<Array<{timestamp: string, message: string, type: string}>>([]);

// Add log entry
const addLog = (message: string, type: 'info' | 'error' | 'success' = 'info') => {
  logs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  });
  // Keep only last 10 logs
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10);
  }
};

// Check OneSignal status
const checkOneSignalStatus = async () => {
  try {
    const playerId = await OneSignal.User.getOnesignalId();
    playerIdStatus.value = playerId || '未獲取到 Player ID';
    addLog(`OneSignal Player ID: ${playerId}`, 'info');
  } catch (error) {
    console.error('Error getting OneSignal status:', error);
    playerIdStatus.value = '錯誤: ' + error;
    addLog(`OneSignal 錯誤: ${error}`, 'error');
  }
};

// Check notification queue status
const checkQueueStatus = async () => {
  try {
    const status = await notificationService.getQueueStatus();
    queueStatus.value = status;
    addLog(`隊列狀態: ${status.pending} 待處理, ${status.processed} 已處理`, 'info');
  } catch (error) {
    console.error('Error checking queue status:', error);
    addLog(`隊列狀態錯誤: ${error}`, 'error');
  }
};

// Process notification queue manually
const processQueue = async () => {
  try {
    addLog('開始處理通知隊列...', 'info');
    // This will be handled by the notification service
    await checkQueueStatus(); // Refresh status after processing
    addLog('隊列處理完成', 'success');
  } catch (error) {
    console.error('Error processing queue:', error);
    addLog(`處理隊列錯誤: ${error}`, 'error');
  }
};

// Send test message (this will trigger the notification system)
const sendTestMessage = async () => {
  if (!testMessage.value.trim() || !authStore.currentUser) {
    toastMessage.value = '請輸入測試訊息並確保已登入';
    return;
  }

  try {
    isSending.value = true;
    addLog('發送測試訊息...', 'info');

    // Create a test conversation with yourself (for testing purposes)
    // In a real scenario, you'd send to another user
    const testConversation = await chatStore.getOrCreateConversation(authStore.currentUser.id);
    
    if (testConversation) {
      const message = await chatStore.sendMessage(
        testConversation.id,
        testMessage.value,
        'text'
      );

      if (message) {
        addLog('測試訊息發送成功', 'success');
        testMessage.value = '';
        // Check queue status after sending
        setTimeout(() => checkQueueStatus(), 1000);
      } else {
        addLog('測試訊息發送失敗', 'error');
      }
    }
  } catch (error) {
    console.error('Error sending test message:', error);
    addLog(`發送錯誤: ${error}`, 'error');
  } finally {
    isSending.value = false;
  }
};

// Clear logs
const clearLogs = () => {
  logs.value = [];
};

onMounted(() => {
  checkOneSignalStatus();
  checkQueueStatus();
});
</script>

<style scoped>
.log-entry {
  margin-bottom: 8px;
  padding: 8px;
  background: var(--ion-color-light);
  border-radius: 4px;
}

.log-entry small {
  color: var(--ion-color-medium);
}

.log-entry .info {
  color: var(--ion-color-dark);
}

.log-entry .error {
  color: var(--ion-color-danger);
}

.log-entry .success {
  color: var(--ion-color-success);
}
</style>
