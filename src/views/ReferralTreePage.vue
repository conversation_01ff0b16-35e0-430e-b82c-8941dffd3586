<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile"></ion-back-button>
        </ion-buttons>
        <ion-title>我的推薦網絡</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <ion-header collapse="condense">
          <ion-toolbar>
            <ion-title size="large">我的推薦網絡</ion-title>
          </ion-toolbar>
        </ion-header>

        <!-- Referrer Information -->
        <ion-card v-if="referrerInfo" class="referrer-card">
          <ion-card-header>
            <ion-card-title>我的推薦人</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <div class="referrer-info">
              <ion-avatar>
                <img src="https://ionicframework.com/docs/img/demos/avatar.svg" alt="Avatar" />
              </ion-avatar>
              <div class="referrer-details">
                <h3>{{ referrerInfo.full_name }}</h3>
                <p>{{ referrerInfo.username }}</p>
                <ion-badge color="primary">{{ getRoleLabel(referrerInfo.role) }}</ion-badge>
              </div>
            </div>
          </ion-card-content>
        </ion-card>

        <ion-card v-else class="referrer-card">
          <ion-card-header>
            <ion-card-title>我的推薦人</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <div class="referrer-info">
              <ion-avatar>
                <img src="https://ionicframework.com/docs/img/demos/avatar.svg" alt="Avatar" />
              </ion-avatar>
              <div class="referrer-details">
                <h3>公司</h3>
                <p>您是由公司直接推薦</p>
              </div>
            </div>
          </ion-card-content>
        </ion-card>

        <!-- Statistics -->
        <referral-statistics :statistics="statistics" />

        <!-- View Selector -->
        <div class="view-selector">
          <ion-segment v-model="selectedView" mode="ios">
            <ion-segment-button value="tree">
              <ion-label>樹狀圖</ion-label>
            </ion-segment-button>
            <ion-segment-button value="table">
              <ion-label>表格</ion-label>
            </ion-segment-button>
          </ion-segment>
        </div>

        <!-- Search Bar -->
        <ion-searchbar
          v-model="searchQuery"
          placeholder="搜尋用戶名稱或用戶名"
          @ionInput="handleSearch"
          mode="ios"
          class="search-bar"
        ></ion-searchbar>

        <!-- Loading Indicator -->
        <div v-if="isLoading" class="loading-container">
          <ion-spinner></ion-spinner>
          <p>載入中...</p>
        </div>

        <!-- Tree View -->
        <referral-tree-view
          v-if="selectedView === 'tree' && !isLoading"
          :referrals="filteredReferrals"
          :current-user="currentUser"
        />

        <!-- Table View -->
        <referral-table-view
          v-if="selectedView === 'table' && !isLoading"
          :referrals="filteredReferrals"
        />

        <!-- No Referrals Message -->
        <ion-card v-if="!isLoading && referrals.length === 0" class="empty-state">
          <ion-card-content>
            <ion-icon :icon="peopleOutline" size="large"></ion-icon>
            <h3>您還沒有推薦任何人</h3>
            <p>分享您的推薦碼，邀請朋友加入！</p>
            <ion-button expand="block" router-link="/profile">
              查看我的推薦碼
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonAvatar,
  IonBadge,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonSearchbar,
  IonSpinner,
  IonToast,
  IonButton,
  IonIcon,
  onIonViewDidEnter,
} from '@ionic/vue';
import { peopleOutline } from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import ReferralTreeView from '@/components/ReferralTreeView.vue';
import ReferralTableView from '@/components/ReferralTableView.vue';
import ReferralStatistics from '@/components/ReferralStatistics.vue';
import type { UserRole } from '@/types';

const authStore = useAuthStore();
const userStore = useUserStore();
const currentUser = computed(() => userStore.currentUser);

const isLoading = ref(true);
const toastMessage = ref('');
const selectedView = ref('tree');
const searchQuery = ref('');
const referrals = ref<any[]>([]);
const referrerInfo = computed(() => userStore.referrer);
const statistics = ref({
  totalReferrals: 0,
  directReferrals: 0,
  levels: 0,
});

// Filter referrals based on search query
const filteredReferrals = computed(() => {
  if (!searchQuery.value) return referrals.value;

  const query = searchQuery.value.toLowerCase();
  return referrals.value.filter(user =>
    user.username.toLowerCase().includes(query) ||
    user.full_name.toLowerCase().includes(query)
  );
});

// Get role label
const getRoleLabel = (role?: UserRole) => {
  switch (role) {
    case 'free':
      return '普通免費會員';
    case 'merchant':
      return '商家會員';
    case 'president':
      return '分會長';
    default:
      return '未知';
  }
};

// Handle search input
const handleSearch = (event: any) => {
  searchQuery.value = event.target.value;
};

// Fetch all referrals (direct and indirect)
const fetchReferrals = async () => {
  if (!authStore.currentUser?.id) return;

  isLoading.value = true;
  try {
    // First, get all direct referrals
    const { data: directReferrals, error: directError } = await supabase
      .from('users')
      .select('id, username, full_name, role, created_at, referrer_id')
      .eq('referrer_id', authStore.currentUser.id);

    if (directError) throw directError;

    // Initialize the referrals array with direct referrals
    const allReferrals: any[] = directReferrals || [];

    // Set direct referrals count
    statistics.value.directReferrals = allReferrals.length;

    // If we have direct referrals, fetch their referrals (level 2)
    if (allReferrals.length > 0) {
      const level2Ids = allReferrals.map(user => user.id);

      // Recursively fetch up to 10 levels
      await fetchReferralsByLevel(level2Ids, allReferrals, 2, 10);
    }

    // Set the referrals and update statistics
    referrals.value = allReferrals;
    statistics.value.totalReferrals = allReferrals.length;

  } catch (error) {
    console.error('Error fetching referrals:', error);
    toastMessage.value = '載入推薦資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Recursively fetch referrals by level
const fetchReferralsByLevel = async (parentIds: string[], allReferrals: any[], currentLevel: number, maxLevel: number) => {
  if (currentLevel > maxLevel || parentIds.length === 0) {
    statistics.value.levels = currentLevel - 1;
    return;
  }

  try {
    const { data: levelReferrals, error } = await supabase
      .from('users')
      .select('id, username, full_name, role, created_at, referrer_id')
      .in('referrer_id', parentIds);

    if (error) throw error;

    if (levelReferrals && levelReferrals.length > 0) {
      // Add these referrals to the overall list
      allReferrals.push(...levelReferrals);

      // Get the IDs for the next level
      const nextLevelIds = levelReferrals.map(user => user.id);

      // Recursively fetch the next level
      await fetchReferralsByLevel(nextLevelIds, allReferrals, currentLevel + 1, maxLevel);
    } else {
      // No more referrals at this level, set the max level reached
      statistics.value.levels = currentLevel - 1;
    }
  } catch (error) {
    console.error(`Error fetching level ${currentLevel} referrals:`, error);
    statistics.value.levels = currentLevel - 1;
  }
};

// Initialize data
const initializeData = async () => {
  // Reset all data first to prevent stale data from previous user
  isLoading.value = true;
  referrals.value = [];
  statistics.value = {
    totalReferrals: 0,
    directReferrals: 0,
    levels: 0,
  };

  await fetchReferrals();
  isLoading.value = false;
};

onMounted(() => {
  if (authStore.isAuthenticated) {
    initializeData();
  }
});

onIonViewDidEnter(() => {
  if (authStore.isAuthenticated) {
    initializeData();
  }
});
</script>

<style scoped>
.page-container {
  padding: 1rem;
}

.referrer-card {
  margin-bottom: 1rem;
}

.referrer-info {
  display: flex;
  align-items: center;
}

.referrer-details {
  margin-left: 1rem;
}

.referrer-details h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.referrer-details p {
  margin: 0.25rem 0 0.5rem;
  color: var(--ion-color-medium);
}

.view-selector {
  margin: 1rem 0;
}

.search-bar {
  margin-bottom: 1rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.empty-state ion-icon {
  font-size: 4rem;
  color: var(--ion-color-medium);
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--ion-color-medium);
  margin-bottom: 1.5rem;
}
</style>
