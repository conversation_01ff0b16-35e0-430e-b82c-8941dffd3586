<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>我的反饋</ion-title>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile"></ion-back-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <!-- Loading State -->
        <div v-if="isLoading" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入反饋中...</p>
        </div>

        <!-- Feedback List -->
        <ion-list v-else-if="feedbackList.length > 0">
          <ion-item-group v-for="feedback in feedbackList" :key="feedback.id">
            <ion-item button @click="openFeedbackDetail(feedback)">
              <div class="feedback-item">
                <div class="feedback-header">
                  <h3>{{ feedback.subject }}</h3>
                  <ion-badge :color="getStatusColor(feedback.status)">
                    {{ getStatusLabel(feedback.status) }}
                  </ion-badge>
                </div>
                <div class="feedback-meta">
                  <span class="category">{{ getCategoryLabel(feedback.category) }}</span>
                  <span class="date">{{ formatDate(feedback.created_at) }}</span>
                </div>
                <p class="feedback-preview">{{ feedback.description.substring(0, 100) }}...</p>
              </div>
              <ion-icon :icon="chevronForward" slot="end"></ion-icon>
            </ion-item>
          </ion-item-group>
        </ion-list>

        <!-- Empty State -->
        <div v-else class="empty-state ion-padding">
          <ion-icon :icon="chatboxEllipsesOutline" size="large"></ion-icon>
          <h3>暫無反饋記錄</h3>
          <p>您還沒有提交任何反饋</p>
          <ion-button @click="$router.push('/profile')" fill="outline">
            返回個人資料頁面
          </ion-button>
        </div>

        <!-- Feedback Detail Modal -->
        <ion-modal :is-open="showDetailModal" @didDismiss="showDetailModal = false">
          <ion-header>
            <ion-toolbar>
              <ion-title>反饋詳情</ion-title>
              <ion-buttons slot="end">
                <ion-button @click="showDetailModal = false">關閉</ion-button>
              </ion-buttons>
            </ion-toolbar>
          </ion-header>
          <ion-content v-if="selectedFeedback">
            <div class="ion-padding">
              <div class="detail-section">
                <h4>基本信息</h4>
                <ion-item>
                  <ion-label>
                    <h3>主題</h3>
                    <p>{{ selectedFeedback.subject }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>類型</h3>
                    <p>{{ getCategoryLabel(selectedFeedback.category) }}</p>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>狀態</h3>
                    <ion-badge :color="getStatusColor(selectedFeedback.status)">
                      {{ getStatusLabel(selectedFeedback.status) }}
                    </ion-badge>
                  </ion-label>
                </ion-item>
                <ion-item>
                  <ion-label>
                    <h3>提交時間</h3>
                    <p>{{ formatDate(selectedFeedback.created_at) }}</p>
                  </ion-label>
                </ion-item>
                <ion-item v-if="selectedFeedback.updated_at !== selectedFeedback.created_at">
                  <ion-label>
                    <h3>最後更新</h3>
                    <p>{{ formatDate(selectedFeedback.updated_at) }}</p>
                  </ion-label>
                </ion-item>
              </div>

              <div class="detail-section">
                <h4>詳細描述</h4>
                <div class="description-content">
                  <p>{{ selectedFeedback.description }}</p>
                </div>
              </div>

              <div class="detail-section" v-if="selectedFeedback.admin_response">
                <h4>管理員回覆</h4>
                <div class="response-content">
                  <p>{{ selectedFeedback.admin_response }}</p>
                </div>
              </div>
            </div>
          </ion-content>
        </ion-modal>

        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonList,
  IonItemGroup,
  IonItem,
  IonLabel,
  IonIcon,
  IonSpinner,
  IonBadge,
  IonModal,
  IonButton,
  IonToast,
} from '@ionic/vue';
import {
  chevronForward,
  chatboxEllipsesOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { format } from 'date-fns';

interface Feedback {
  id: string;
  user_id: string | null;
  category: string;
  subject: string;
  description: string;
  contact_email: string | null;
  is_anonymous: boolean;
  status: string;
  admin_notes: string | null;
  admin_response: string | null;
  created_at: string;
  updated_at: string;
}

const authStore = useAuthStore();
const feedbackList = ref<Feedback[]>([]);
const isLoading = ref(true);
const showDetailModal = ref(false);
const selectedFeedback = ref<Feedback | null>(null);
const toastMessage = ref('');

onMounted(async () => {
  await loadMyFeedback();
});

const loadMyFeedback = async () => {
  try {
    isLoading.value = true;
    
    if (!authStore.currentUser?.id) {
      toastMessage.value = '請先登入';
      return;
    }

    const { data, error } = await supabase
      .from('user_feedback')
      .select('*')
      .eq('user_id', authStore.currentUser.id)
      .eq('is_anonymous', false)
      .order('created_at', { ascending: false });

    if (error) throw error;
    feedbackList.value = data || [];
  } catch (error) {
    console.error('Error loading feedback:', error);
    toastMessage.value = '載入反饋失敗';
  } finally {
    isLoading.value = false;
  }
};

const openFeedbackDetail = (feedback: Feedback) => {
  selectedFeedback.value = feedback;
  showDetailModal.value = true;
};

const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), 'yyyy/MM/dd HH:mm');
  } catch (error) {
    return dateString;
  }
};

const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    bug: '錯誤回報',
    feature: '功能建議',
    improvement: '改進建議',
    ui: '介面問題',
    performance: '效能問題',
    other: '其他',
  };
  return labels[category] || category;
};

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待處理',
    in_progress: '處理中',
    resolved: '已解決',
    closed: '已關閉',
  };
  return labels[status] || status;
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'warning',
    in_progress: 'primary',
    resolved: 'success',
    closed: 'medium',
  };
  return colors[status] || 'medium';
};
</script>

<style scoped>
.feedback-item {
  width: 100%;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.feedback-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.feedback-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
}

.feedback-preview {
  margin: 0;
  color: var(--ion-color-step-600);
  font-size: 0.9rem;
  line-height: 1.4;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

.empty-state ion-icon {
  color: var(--ion-color-medium);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: var(--ion-color-medium);
  margin-bottom: 0.5rem;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem;
  color: var(--ion-color-primary);
  font-weight: 600;
}

.description-content,
.response-content {
  background: var(--ion-color-light);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 0.5rem;
}

.description-content p,
.response-content p {
  margin: 0;
  line-height: 1.5;
  white-space: pre-wrap;
}

.response-content {
  background: var(--ion-color-success-tint);
  border-left: 4px solid var(--ion-color-success);
}
</style>
