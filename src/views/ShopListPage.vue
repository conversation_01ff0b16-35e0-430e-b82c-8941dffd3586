<template>
  <ion-page>
    <ion-header>
      <!-- Compact Header with Search and Filters -->
      <ion-toolbar>
        <ion-searchbar
          v-model="searchQuery"
          placeholder="搜尋商家"
          @ionInput="handleSearch"
          class="custom-searchbar"
        ></ion-searchbar>

        <ion-buttons slot="end">
          <ion-button v-if="authStore.isAuthenticated" router-link="/liked-shops">
            <ion-icon :icon="heart" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button id="shop-list-sort-trigger" class="sort-button">
            <ion-icon :icon="options" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>

      <ion-toolbar>
        <ion-segment v-model="selectedCategory" scrollable @ionChange="handleCategoryChange" class="category-segment">
          <ion-segment-button value="all">
            <ion-label>全部</ion-label>
          </ion-segment-button>
          <ion-segment-button v-for="category in shopCategories" :key="category.id" :value="category.id.toString()">
            <ion-label>{{ category.title }}</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-toolbar>

      <ion-popover trigger="shop-list-sort-trigger" trigger-action="click">
        <ion-content>
          <ion-list lines="full">
            <ion-item button @click="setSortOption('newest')">
              <ion-label>最新</ion-label>
              <ion-icon v-if="sortOption === 'newest'" :icon="checkmark" slot="end"></ion-icon>
            </ion-item>
            <ion-item button @click="setSortOption('rating')">
              <ion-label>評分最高</ion-label>
              <ion-icon v-if="sortOption === 'rating'" :icon="checkmark" slot="end"></ion-icon>
            </ion-item>
            <ion-item button @click="setSortOption('popular')">
              <ion-label>最受歡迎</ion-label>
              <ion-icon v-if="sortOption === 'popular'" :icon="checkmark" slot="end"></ion-icon>
            </ion-item>
            <ion-item button @click="setSortOption('products')">
              <ion-label>產品最多</ion-label>
              <ion-icon v-if="sortOption === 'products'" :icon="checkmark" slot="end"></ion-icon>
            </ion-item>
          </ion-list>
        </ion-content>
      </ion-popover>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <div class="shops-container ion-padding">
          <!-- Featured Shops -->
          <div v-if="featuredShops.length > 0" class="featured-shops">
            <h2 class="section-title">精選商家</h2>
            <div class="featured-shops-grid">
              <ShopCard v-for="shop in featuredShops" :key="shop.id" :shop="shop" />
            </div>
          </div>

          <!-- All Shops -->
          <div class="all-shops">
            <div class="shops-grid">
              <ShopCard v-for="shop in filteredShops" :key="shop.id" :shop="shop" />
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="!isLoading && filteredShops.length === 0" class="empty-state">
            <ion-icon :icon="searchQuery ? search : storefront" color="medium"></ion-icon>
            <p>{{ searchQuery ? '找不到相關商家' : '暫無商家' }}</p>
          </div>
        </div>

        <!-- Toast Messages -->
        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonIcon,
  IonSearchbar,
  IonButtons,
  IonButton,
  IonToast,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonPopover,
  IonList,
  IonItem,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  star,
  storefront,
  search,
  heart,
  heartOutline,
  time,
  cube,
  pricetag,
  options,
  checkmark,
  ribbon
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import ShopCard from '@/components/ShopCard.vue';

const authStore = useAuthStore();
const userStore = useUserStore();
const shops = ref<any[]>([]);
const isLoading = ref(true);
const searchQuery = ref('');
const toastMessage = ref('');
const selectedCategory = ref('all');
const sortOption = ref('newest');
const shopCategories = ref<any[]>([]);

const featuredShops = computed(() => {
  return shops.value.filter(shop => shop.is_featured);
});

const filteredShops = computed(() => {
  // First filter by featured status
  let filtered = shops.value.filter(shop => !shop.is_featured);

  // Then filter by search query if present
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(shop =>
      shop.name.toLowerCase().includes(query) ||
      (shop.description && shop.description.toLowerCase().includes(query))
    );
  }

  // Then filter by category if not 'all'
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(shop =>
      shop.shop_category_id?.toString() === selectedCategory.value
    );
  }

  // Finally sort according to selected sort option
  return filtered.sort((a, b) => {
    switch (sortOption.value) {
      case 'rating':
        return (b.rating || 0) - (a.rating || 0);
      case 'popular':
        return (b.like_count || 0) - (a.like_count || 0);
      case 'products':
        return (b.product_count || 0) - (a.product_count || 0);
      case 'newest':
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
  });
});

const handleSearch = (event: CustomEvent) => {
  searchQuery.value = event.detail.value || '';
};

const handleCategoryChange = (event: CustomEvent) => {
  selectedCategory.value = event.detail.value;
};

const setSortOption = (option: string) => {
  sortOption.value = option;
};

// Load shop categories on mount
onMounted(async () => {
  try {
    const { data, error } = await supabase
      .from('shop_categories')
      .select('*')
      .order('is_featured', { ascending: false });

    if (error) throw error;
    shopCategories.value = data || [];
  } catch (error) {
    console.error('Error loading shop categories:', error);
  }
});

onIonViewDidEnter(async () => {
  try {
    isLoading.value = true;

    // Load shops with shop categories
    const { data, error } = await supabase
      .from('shops')
      .select(`
        *,
        shop_categories:shop_category_id(id, title)
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;
    shops.value = data || [];
  } catch (error) {
    console.error('Error loading data:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
.shops-container {
  width: 100%;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
  color: var(--ion-text-color);
}

.featured-shops {
  margin-bottom: 3rem;
}

.featured-shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.shop-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.shop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.banner-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.featured .banner-container {
  height: 200px;
}

.shop-banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.shop-card:hover .shop-banner {
  transform: scale(1.05);
}

.featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(var(--ion-color-warning-rgb), 0.9);
  color: var(--ion-color-warning-contrast);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.shop-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 0.25rem;
  opacity: 1;
  transform: translateX(0);
  z-index: 10;
}

.action-button {
  --background: transparent;
  --border-radius: 4px;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  margin: 0;
  width: 36px;
  height: 36px;
}

.action-button ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-primary);
}

.featured-badge ion-icon {
  font-size: 1.1rem;
}

ion-card-header {
  padding: 1rem;
}

.custom-searchbar {
  flex: 1;
}

.sort-button {
  margin: 0;
  --padding-start: 8px;
  --padding-end: 8px;
}

.category-segment {
  padding: 0 8px;
}

.shop-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.shop-details {
  flex: 1;
  min-width: 0;
}

.shop-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.shop-age-badge {
  background: var(--ion-color-warning);
  color: var(--ion-color-warning-contrast);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.badge-icon {
  font-size: 0.8rem;
}

.shop-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
  flex-shrink: 0;
}

ion-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-description {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin: 0.5rem 0;
}

.empty-state {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.custom-searchbar {
  --background: var(--ion-color-light);
  --border-radius: 8px;
  --box-shadow: none;
  --placeholder-color: var(--ion-color-medium);
  padding: 0 8px;
}

.filter-sort-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  width: 100%;
}

.shop-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.25rem;
  flex-wrap: wrap;
}

.shop-category, .shop-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  background: var(--ion-color-light);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.shop-rating {
  color: var(--ion-color-warning);
}

.shop-stats {
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
  background: var(--ion-color-light-shade);
  border-radius: 8px;
  padding: 0.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  flex: 1;
  justify-content: center;
}

.stat-divider {
  width: 1px;
  height: 16px;
  background-color: var(--ion-color-medium);
  opacity: 0.3;
}

.stat-item ion-icon {
  font-size: 1rem;
  color: var(--ion-color-primary);
}

@media (max-width: 768px) {
  .section-title {
    font-size: 1.5rem;
    margin: 1.5rem 0 1rem;
  }

  .featured-shops-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .shops-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .banner-container {
    height: 140px;
  }

  .featured .banner-container {
    height: 160px;
  }

  .shop-logo {
    width: 32px;
    height: 32px;
  }

  ion-card-title {
    font-size: 1rem;
  }

  .shop-meta {
    gap: 0.5rem;
  }

  .shop-stats {
    padding: 0.4rem;
  }

  .stat-item {
    font-size: 0.7rem;
  }

  .shop-category, .shop-rating {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }

  ion-segment-button {
    font-size: 0.8rem;
    min-width: 60px;
  }

  .shop-age-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .shop-description {
    font-size: 0.8rem;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }
}
</style>