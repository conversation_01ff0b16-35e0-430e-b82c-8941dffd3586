<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/my-events" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>活動簽到</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="event" class="check-in-container">
        <!-- Event Info -->
        <div class="event-info">
          <h1>{{ event.title }}</h1>
          <div class="event-meta">
            <p>
              <ion-icon :icon="calendarOutline"></ion-icon>
              {{ formatDateTime(event.start_datetime) }}
            </p>
            <p>
              <ion-icon :icon="timeOutline"></ion-icon>
              至 {{ formatDateTime(event.end_datetime) }}
            </p>
            <p>
              <ion-icon :icon="locationOutline"></ion-icon>
              {{ event.address }}
            </p>
          </div>
        </div>

        <!-- Check-in Stats -->
        <div class="check-in-stats">
          <div class="stat-card">
            <h3>已報到</h3>
            <p class="stat-number">{{ attendedCount }}</p>
          </div>
          <div class="stat-card">
            <h3>已報名</h3>
            <p class="stat-number">{{ registeredCount }}</p>
          </div>
        </div>

        <!-- Scanner Section -->
        <div v-if="true" class="scanner-section">
          <div v-if="!isScanning" class="start-scan">
            <ion-button expand="block" @click="startScanning">
              <ion-icon :icon="scanOutline" slot="start"></ion-icon>
              開始掃描
            </ion-button>
          </div>

          <div v-else class="scanner-container">
            <QrCodeScanner :active="isScanning" @codeScanned="handleScan" />
            <div class="scanner-actions">
              <ion-button expand="block" color="medium" @click="stopScanning">
                停止掃描
              </ion-button>
              <ion-button expand="block" color="primary" @click="restartScanning" v-if="isScanning">
                重新啟動掃描
              </ion-button>
            </div>
          </div>
        </div>

        <!-- Time Restriction Notice -->
        <div v-else class="time-notice">
          <ion-icon :icon="timeOutline" color="warning"></ion-icon>
          <p>{{ timeRestrictionMessage }}</p>
        </div>

        <!-- Manual Entry -->
        <div class="manual-entry">
          <h2>手動輸入</h2>
          <p class="manual-entry-info">如果QR碼掃描無法使用，請輸入6位簽到碼</p>
          <div class="manual-input">
            <ion-input
              v-model="manualCode"
              placeholder="輸入6位簽到碼"
              class="code-input"
              maxlength="6"
              type="text"
            ></ion-input>
            <ion-button @click="handleManualEntry" :disabled="!isValidManualCode">
              確認
            </ion-button>
          </div>
        </div>

        <!-- Recent Check-ins -->
        <div class="recent-checkins">
          <h2>最近簽到記錄</h2>
          <ion-list>
            <ion-item v-for="record in recentCheckins" :key="record.id">
              <ion-label>
                <h3>{{ record.full_name }}</h3>
                <p>{{ formatTime(record.updated_at) }}</p>
              </ion-label>
              <ion-badge slot="end" color="success">已簽到</ion-badge>
            </ion-item>
          </ion-list>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
        <p>找不到活動</p>
      </div>

      <!-- Alert for success and errors -->
      <ion-alert
        :is-open="!!alertMessage"
        :header="alertHeader"
        :message="alertMessage"
        :buttons="['確定']"
        @didDismiss="alertMessage = ''"
      ></ion-alert>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonBadge,
  IonSpinner,
  IonInput,
  IonAlert,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  calendarOutline,
  timeOutline,
  locationOutline,
  scanOutline,
  alertCircleOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import QrCodeScanner from '@/components/QrCodeScanner.vue';
import { extractCheckInCode } from '@/utils/qrCodeUtils';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const eventId = route.params.id as string;

const event = ref<any>(null);
const isLoading = ref(true);
const isScanning = ref(false);
const manualCode = ref('');
const attendedCount = ref(0);
const registeredCount = ref(0);
const recentCheckins = ref<any[]>([]);

// Alert variables
const alertMessage = ref('');
const alertHeader = ref('');

// Validate manual code (must be 6 characters)
const isValidManualCode = computed(() => {
  return manualCode.value.length === 6;
});

// Time restriction check
const canScan = computed(() => {
  if (!event.value) return false;

  const now = new Date();

  // Get event start time from start_datetime (already in HKT)
  const eventDateTime = new Date(event.value.start_datetime);

  // Allow scanning from 2 hours before event start
  const scanStartTime = new Date(eventDateTime);
  scanStartTime.setHours(eventDateTime.getHours() - 2);

  // Get event end time from end_datetime (already in HKT)
  const eventEndTime = new Date(event.value.end_datetime);

  return now >= scanStartTime && now <= eventEndTime;
});

const timeRestrictionMessage = computed(() => {
  if (!event.value) return '';

  const now = new Date();

  // Get event start time from start_datetime (already in HKT)
  const eventDateTime = new Date(event.value.start_datetime);

  // Allow scanning from 2 hours before event start
  const scanStartTime = new Date(eventDateTime);
  scanStartTime.setHours(eventDateTime.getHours() - 2);

  if (now < scanStartTime) {
    return '簽到將於活動開始前2小時開放';
  }

  // Get event end time from end_datetime (already in HKT)
  const eventEndTime = new Date(event.value.end_datetime);

  if (now > eventEndTime) {
    return '活動已結束';
  }

  return '';
});

onIonViewDidEnter(async () => {
  await loadEventData();
});

const loadEventData = async () => {
  try {
    isLoading.value = true;

    // Load event details
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (eventError) throw eventError;
    if (!eventData) throw new Error('Event not found');

    // Verify user is event owner
    if (eventData.user_id !== authStore.currentUser?.id) {
      router.replace('/my-events');
      return;
    }

    event.value = eventData;

    // Load check-in stats
    await loadStats();
    await loadRecentCheckins();
  } catch (error) {
    console.error('Error loading event:', error);
    showAlert('錯誤', '載入活動資料時發生錯誤');
  } finally {
    isLoading.value = false;
  }
};

const loadStats = async () => {
  try {
    // Get total registrations
    const { count: registered } = await supabase
      .from('event_applications')
      .select('id', { count: 'exact' })
      .eq('event_id', eventId)
      .eq('status', 'confirmed');

    registeredCount.value = registered || 0;

    // Get attended count
    const { count: attended } = await supabase
      .from('event_applications')
      .select('id', { count: 'exact' })
      .eq('event_id', eventId)
      .eq('status', 'attended');

    attendedCount.value = attended || 0;
  } catch (error) {
    console.error('Error loading stats:', error);
  }
};

const loadRecentCheckins = async () => {
  try {
    const { data, error } = await supabase
      .from('event_applications')
      .select('*')
      .eq('event_id', eventId)
      .eq('status', 'attended')
      .order('updated_at', { ascending: false })
      .limit(10);

    if (error) throw error;
    recentCheckins.value = data || [];
  } catch (error) {
    console.error('Error loading recent check-ins:', error);
  }
};

const startScanning = () => {
  isScanning.value = true;
};

const stopScanning = () => {
  isScanning.value = false;
};

const restartScanning = () => {
  // First stop, then start again
  isScanning.value = false;
  setTimeout(() => {
    isScanning.value = true;
  }, 500);
};

const handleScan = async (code: string) => {
  // Stop scanning immediately after detecting a QR code
  stopScanning();
  await processCheckIn(code);
};

const handleManualEntry = async () => {
  if (!manualCode.value || !isValidManualCode.value) return;

  try {
    // Get all applications for this event
    const { data: applications, error: lookupError } = await supabase
      .from('event_applications')
      .select('*')
      .eq('event_id', eventId);

    if (lookupError) throw lookupError;

    if (!applications || applications.length === 0) {
      showAlert('錯誤', '找不到報名記錄');
      return;
    }

    // Find application with matching check-in code
    const matchingApp = applications.find((app: any) => {
      const checkInCode = extractCheckInCode(app.qr_code);
      return checkInCode === manualCode.value.toUpperCase();
    });

    if (!matchingApp) {
      showAlert('錯誤', '找不到此簽到碼');
      return;
    }

    // Check if already checked in
    if (matchingApp.status === 'attended') {
      showAlert('提示', '此簽到碼已使用');
      return;
    }

    // Process the check-in
    await processCheckIn(matchingApp.qr_code);
    manualCode.value = '';
  } catch (error) {
    console.error('Error processing manual check-in:', error);
    showAlert('錯誤', '簽到失敗，請稍後再試');
  }
};

const processCheckIn = async (code: string) => {
  try {
    // Parse QR code data (format: app_id|event_id|timestamp)
    const [applicationId, scannedEventId] = code.split('|');

    // Verify event ID
    if (scannedEventId !== eventId) {
      showAlert('錯誤', '無效的QR碼');
      return;
    }

    // Get application details
    const { data: application, error: applicationError } = await supabase
      .from('event_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (applicationError || !application) {
      showAlert('錯誤', '找不到報名記錄');
      return;
    }

    // Check if already checked in
    if (application.status === 'attended') {
      showAlert('提示', '此QR碼已使用');
      return;
    }

    // Update status to attended
    const { error: updateError } = await supabase
      .from('event_applications')
      .update({
        status: 'attended',
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId);

    if (updateError) throw updateError;

    // Update stats and recent check-ins
    attendedCount.value++;
    await loadRecentCheckins();

    // Show success alert with user info
    const userName = application.full_name || '用戶';
    const checkInTime = formatTime(new Date().toISOString());
    showAlert('簽到成功', `${userName} 已於 ${checkInTime} 成功簽到`);
  } catch (error) {
    console.error('Error processing check-in:', error);
    showAlert('錯誤', '簽到失敗，請稍後再試');
  }
};

// Helper function to show alerts
const showAlert = (header: string, message: string) => {
  alertHeader.value = header;
  alertMessage.value = message;
};

// Format datetime for display (assuming input is already in HKT)
const formatDateTime = (datetimeStr: string) => {
  if (!datetimeStr) return '';

  // Create a date object from the datetime string
  const date = new Date(datetimeStr);

  // Format the date
  return date.toLocaleString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-HK', {
    hour: '2-digit',
    minute: '2-digit',
  });
};
</script>

<style scoped>
.check-in-container {
  max-width: 800px;
  margin: 0 auto;
}

.event-info {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.event-info h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: var(--ion-color-dark);
}

.event-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.event-meta p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: var(--ion-color-medium);
}

.event-meta ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-primary);
}

.check-in-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-card h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-medium);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin: 0.5rem 0 0;
  color: var(--ion-color-primary);
}

.scanner-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.scanner-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scanner-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.time-notice {
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.time-notice ion-icon {
  font-size: 1.5rem;
}

.time-notice p {
  margin: 0;
  color: var(--ion-color-warning-shade);
}

.manual-entry {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.manual-entry h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--ion-color-dark);
}

.manual-entry-info {
  margin: 0 0 1rem;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.manual-input {
  display: flex;
  gap: 0.5rem;
}

.code-input {
  flex: 1;
  --background: var(--ion-color-light);
  --border-radius: 8px;
  --padding-start: 1rem;
  --padding-end: 1rem;
}

.recent-checkins {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.recent-checkins h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: var(--ion-color-dark);
}

.loading-container,
.error-container {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .event-info,
  .scanner-section,
  .manual-entry,
  .recent-checkins {
    padding: 1rem;
  }

  .event-info h1 {
    font-size: 1.25rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}
</style>