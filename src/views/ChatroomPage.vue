<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/conversations" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title
          v-if="chatStore.currentConversation"
          @click="navigateToUserProfile"
          class="clickable-title"
        >
          {{ chatStore.currentConversation.other_participant?.full_name || '聊天室' }}
        </ion-title>
        <ion-title v-else>聊天室</ion-title>
        <ion-buttons slot="end">
          <ion-button v-if="chatStore.connectionStatus === 'connected'" color="success" fill="clear">
            <ion-icon :icon="wifiOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button v-else-if="chatStore.connectionStatus === 'connecting'" color="warning" fill="clear">
            <ion-icon :icon="syncOutline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button v-else color="danger" fill="clear">
            <ion-icon :icon="wifiOutline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content ref="contentEl" class="ion-padding">
      <!-- Product Context Header (for product-based conversations) -->
      <div v-if="chatStore.currentConversation?.conversation_type === 'product' && chatStore.currentConversation?.product" class="product-context-header">
        <ion-card class="product-header-card">
          <ion-card-content>
            <div class="product-header-content">
              <div class="product-image">
                <img
                  :src="chatStore.currentConversation.product.cover_image || '/placeholder-image.jpg'"
                  :alt="chatStore.currentConversation.product.title"
                  @click="navigateToProduct"
                />
              </div>
              <div class="product-info">
                <h3 @click="navigateToProduct" class="product-title">{{ chatStore.currentConversation.product.title }}</h3>
                <p class="product-price">
                  {{ chatStore.currentConversation.product.price === 0 ? '免費' : `$${chatStore.currentConversation.product.price}` }}
                </p>
                <p class="product-shop">{{ chatStore.currentConversation.product.shop_name }}</p>
              </div>
              <ion-button
                fill="clear"
                size="small"
                @click="navigateToProduct"
                class="view-product-btn"
              >
                查看
              </ion-button>
            </div>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Loading indicator -->
      <div class="spin ion-text-center" v-if="chatStore.isLoadingMessages">
        <ion-spinner></ion-spinner>
      </div>

      <!-- Messages list -->
      <ion-list v-else-if="chatStore.messages.length > 0">
        <ion-item
          lines="none"
          v-for="msg in chatStore.messages"
          :key="msg.id"
          class="message-item"
        >
          <ion-card
            :color="isFromCurrentUser(msg) ? 'tertiary' : 'light'"
            :slot="isFromCurrentUser(msg) ? 'end' : 'start'"
            class="message-card"
          >
            <!-- Product reference (if message has product context) -->
            <ProductReferenceCard
              v-if="msg.metadata?.product"
              :product="msg.metadata.product"
              @product-click="handleProductClick"
              class="message-product-ref"
            />

            <!-- Image message -->
            <template v-if="msg.message_type === 'image'">
              <img
                :src="msg.content"
                class="message-image"
                @click="openImageModal(msg.content)"
              />
            </template>

            <!-- Text message -->
            <div v-else class="message-content" v-html="parseMessage(msg.content)"></div>

            <!-- Message timestamp and read status -->
            <div class="message-footer">
              <span class="message-timestamp">
                {{ formatMessageTime(msg.created_at) }}
              </span>
              <!-- Read status indicators (like WhatsApp) -->
              <span v-if="isFromCurrentUser(msg)" class="read-status">
                <!-- Single tick: sent -->
                <ion-icon v-if="!msg.is_read_by_other" :icon="checkmarkOutline" class="tick-sent"></ion-icon>
                <!-- Double tick: delivered/read -->
                <ion-icon v-else :icon="checkmarkDoneOutline" class="tick-read"></ion-icon>
              </span>
            </div>
          </ion-card>
        </ion-item>
      </ion-list>

      <!-- Empty state -->
      <div v-else class="empty-state ion-text-center">
        <ion-icon :icon="chatbubblesOutline" size="large" color="medium"></ion-icon>
        <h3>開始對話</h3>
        <p>發送第一條訊息開始聊天</p>
      </div>
    </ion-content>

    <!-- Message input footer -->
    <ion-footer>
      <ion-toolbar>
        <ion-item lines="none" class="message-input-container">
          <ion-textarea
            v-model="messageInput"
            fill="outline"
            shape="round"
            placeholder="輸入訊息..."
            :disabled="isSending"
            @keyup.enter.exact.prevent="sendMessage"
            :auto-grow="true"
            class="message-textarea"
          ></ion-textarea>

          <ion-buttons slot="end">
            <!-- Image upload button -->
            <ion-button
              color="medium"
              @click="selectImage"
              :disabled="isSending"
            >
              <ion-icon slot="icon-only" :icon="imageOutline"></ion-icon>
            </ion-button>

            <!-- Send button -->
            <ion-button
              color="primary"
              @click="sendMessage"
              :disabled="!messageInput.trim() || isSending"
            >
              <ion-spinner v-if="isSending" name="crescent"></ion-spinner>
              <ion-icon v-else slot="icon-only" :icon="send"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
      </ion-toolbar>
    </ion-footer>

    <!-- Image modal -->
    <ion-modal :is-open="showImageModal" @didDismiss="showImageModal = false">
      <ion-header>
        <ion-toolbar>
          <ion-title>圖片</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="showImageModal = false">關閉</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <img v-if="selectedImageUrl" :src="selectedImageUrl" style="width: 100%; height: auto;" />
      </ion-content>
    </ion-modal>

    <!-- Toast for notifications -->
    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onUnmounted, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonFooter,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonSpinner,
  IonList,
  IonItem,
  IonCard, IonCardContent,
  IonTextarea,
  IonModal,
  IonToast,
  onIonViewDidEnter,
  onIonViewWillLeave
} from '@ionic/vue';
import {
  send,
  chatbubblesOutline,
  imageOutline,
  wifiOutline,
  syncOutline,
  checkmarkOutline,
  checkmarkDoneOutline
} from 'ionicons/icons';
import { useChatStore } from '@/stores/chat';
import { useAuthStore } from '@/stores/auth';
import { usePhotoGallery } from '@/composables/usePhotoGallery';
import { uploadImageToSupabase } from '@/lib/supabase-storage';
import { format } from 'date-fns';
import { zhTW } from 'date-fns/locale';
import ProductReferenceCard from '@/components/ProductReferenceCard.vue';

const route = useRoute();
const router = useRouter();
const chatStore = useChatStore();
const authStore = useAuthStore();
const { takePhoto } = usePhotoGallery();

// Component state
const contentEl = ref<any>(null);
const messageInput = ref('');
const isSending = ref(false);
const toastMessage = ref('');
const showImageModal = ref(false);
const selectedImageUrl = ref('');
const pendingProductContext = ref<any>(null);

// Get conversation ID and other user ID from route params
const conversationId = route.params.conversationId as string;
const otherUserId = route.params.otherUserId as string;

// Initialize conversation
const initializeConversation = async () => {
  try {
    if (!authStore.isAuthenticated) {
      toastMessage.value = '請先登入';
      router.replace('/login');
      return;
    }

    let conversation = null;

    if (conversationId && conversationId !== 'new') {
      // Load existing conversation
      await chatStore.loadConversations();
      conversation = chatStore.conversations.find((c: any) => c.id === conversationId);
    } else if (otherUserId || conversationId === 'new') {
      // Create or get conversation with specific user
      const targetUserId = otherUserId || route.params.otherUserId as string;
      conversation = await chatStore.getOrCreateConversation(targetUserId);

      // Update URL without triggering route change
      if (conversation) {
        history.replaceState(null, '', `/chat/${conversation.id}`);
      }
    }

    if (conversation) {
      chatStore.setCurrentConversation(conversation);

      // Handle pre-filled message from query parameters
      if (route.query.prefill) {
        messageInput.value = route.query.prefill as string;
      }

      // Handle product context from query parameters
      if (route.query.productContext) {
        try {
          pendingProductContext.value = JSON.parse(route.query.productContext as string);
        } catch (error) {
          console.error('Error parsing product context:', error);
        }
      }

      // Clean up query parameters after handling them
      if (route.query.prefill || route.query.productContext) {
        router.replace({ path: route.path });
      }
    } else {
      toastMessage.value = '無法載入對話';
      router.back();
    }
  } catch (error) {
    console.error('Error initializing conversation:', error);
    toastMessage.value = '載入對話時發生錯誤';
  }
};

// Check if message is from current user
const isFromCurrentUser = (message: any): boolean => {
  return message.sender_id === authStore.currentUser?.id;
};

// Parse message content (similar to your existing parseMsg function)
const parseMessage = (content: string): string => {
  // Basic HTML parsing - you can enhance this based on your needs
  return content.replace(/\n/g, '<br>');
};

// Format message timestamp
const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return format(date, 'HH:mm', { locale: zhTW });
  } else if (diffInHours < 24 * 7) {
    return format(date, 'EEE HH:mm', { locale: zhTW });
  } else {
    return format(date, 'MM/dd HH:mm', { locale: zhTW });
  }
};

// Send text message
const sendMessage = async () => {
  if (!messageInput.value.trim() || !chatStore.currentConversation || isSending.value) {
    return;
  }

  try {
    isSending.value = true;
    const content = messageInput.value.trim();
    messageInput.value = '';

    // Include product context if this is the first message with pending context
    const metadata = pendingProductContext.value ? {
      product: pendingProductContext.value
    } : undefined;

    const message = await chatStore.sendMessage(
      chatStore.currentConversation.id,
      content,
      'text',
      metadata
    );

    // Clear pending product context after first message
    if (pendingProductContext.value) {
      pendingProductContext.value = null;
    }

    if (message) {
      // Scroll to bottom after sending
      await nextTick();
      scrollToBottom();
    } else {
      toastMessage.value = '發送訊息失敗';
    }
  } catch (error) {
    console.error('Error sending message:', error);
    toastMessage.value = '發送訊息時發生錯誤';
  } finally {
    isSending.value = false;
  }
};

// Select and upload image
const selectImage = async () => {
  try {
    if (!chatStore.currentConversation) return;

    const photo = await takePhoto();
    if (!photo?.base64Data) return;

    isSending.value = true;

    // Upload image to Supabase storage
    const imageUrl = await uploadImageToSupabase({
      base64Data: photo.base64Data,
      filename: photo.filepath || `chat_${Date.now()}.jpg`,
      mimeType: photo.mimeType || 'image/jpeg'
    }, 'products', 'chat-images');

    if (imageUrl) {
      const message = await chatStore.sendMessage(
        chatStore.currentConversation.id,
        imageUrl,
        'image'
      );

      if (message) {
        await nextTick();
        scrollToBottom();
      } else {
        toastMessage.value = '發送圖片失敗';
      }
    } else {
      toastMessage.value = '上傳圖片失敗';
    }
  } catch (error) {
    console.error('Error sending image:', error);
    toastMessage.value = '發送圖片時發生錯誤';
  } finally {
    isSending.value = false;
  }
};

// Open image in modal
const openImageModal = (imageUrl: string) => {
  selectedImageUrl.value = imageUrl;
  showImageModal.value = true;
};

// Navigate to user profile
const navigateToUserProfile = () => {
  if (!chatStore.currentConversation?.other_participant?.id) {
    toastMessage.value = '無法載入用戶資料';
    return;
  }

  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入以查看用戶資料';
    router.push('/login');
    return;
  }

  // Check if user has permission to view profiles (merchant or president only)
  if (authStore.currentUser?.role === 'free') {
    toastMessage.value = '升級會員以查看用戶資料';
    return;
  }

  // Navigate to user profile
  router.push(`/users/${chatStore.currentConversation.other_participant.id}`);
};

// Handle product click from reference card
const handleProductClick = (product: any) => {
  console.log('Product clicked in chat:', product);
  // Navigation is handled by the ProductReferenceCard component
};

// Navigate to product page
const navigateToProduct = () => {
  if (!chatStore.currentConversation?.product?.id) {
    toastMessage.value = '無法載入產品資料';
    return;
  }

  router.push(`/products/${chatStore.currentConversation.product.id}`);
};

// Scroll to bottom of messages
const scrollToBottom = () => {
  if (contentEl.value) {
    contentEl.value.$el.scrollToBottom(300);
  }
};

// Watch for new messages and scroll to bottom
watch(() => chatStore.messages.length, () => {
  nextTick(() => {
    scrollToBottom();
  });
});

onIonViewDidEnter(() => {
  initializeConversation();
});

onIonViewWillLeave(() => {
  chatStore.unsubscribeFromConversation();
});

onUnmounted(() => {
  chatStore.unsubscribeFromConversation();
});
</script>

<style scoped>
/* Product Context Header */
.product-context-header {
  margin-bottom: 16px;
}

.product-header-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
}

.product-image {
  flex-shrink: 0;
}

.product-image img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.product-image img:hover {
  opacity: 0.8;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  cursor: pointer;
  color: var(--ion-color-primary);
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-title:hover {
  text-decoration: underline;
}

.product-price {
  font-size: 14px;
  font-weight: 600;
  color: var(--ion-color-success);
  margin: 0 0 4px 0;
}

.product-shop {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin: 0;
}

.view-product-btn {
  flex-shrink: 0;
  --color: var(--ion-color-primary);
}
.message-item {
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 8px;
}

.message-card {
  max-width: 80%;
  margin: 4px 8px;
  padding: 12px;
  font-size: 16px;
  border-radius: 16px;
}

.message-product-ref {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.message-content {
  word-wrap: break-word;
  line-height: 1.4;
}

.message-image {
  max-width: 100%;
  border-radius: 8px;
  cursor: pointer;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.message-timestamp {
  font-size: 12px;
  opacity: 0.7;
}

.read-status {
  margin-left: 8px;
}

.tick-sent {
  color: var(--ion-color-medium);
  font-size: 14px;
}

.tick-read {
  color: var(--ion-color-primary);
  font-size: 14px;
}

.message-input-container {
  --padding-start: 0;
  --padding-end: 0;
  padding: 8px;
}

.message-textarea {
  max-height: 120px;
  margin-right: 8px;
}

.empty-state {
  margin-top: 50%;
  transform: translateY(-50%);
}

.empty-state ion-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  color: var(--ion-color-medium);
  margin-bottom: 8px;
}

.empty-state p {
  color: var(--ion-color-medium);
  font-size: 14px;
}

.spin {
  margin-top: 50%;
  transform: translateY(-50%);
}

.clickable-title {
  cursor: pointer;
  transition: opacity 0.2s ease;
  user-select: none;
}

.clickable-title:hover {
  opacity: 0.7;
}

.clickable-title:active {
  opacity: 0.5;
  transform: scale(0.98);
}
</style>
