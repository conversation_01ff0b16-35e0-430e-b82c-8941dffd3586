<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/login" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>重設密碼</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div class="reset-container">
        <div class="reset-card">
          <div class="brand-section">
            <LogoImg className="brand-logo" />
            <h1 class="brand-title">Waste to Gold</h1>
            <p class="brand-subtitle">「搵料」</p>
          </div>
          
          <form @submit.prevent="handleResetPassword" class="reset-form">
            <div class="form-header">
              <h2>重設您的密碼</h2>
              <p>請輸入新密碼</p>
            </div>

            <ion-list>
              <ion-item class="custom-item">
                <ion-icon :icon="lockClosedOutline" slot="start"></ion-icon>
                <ion-input
                  v-model="formData.password"
                  label="新密碼"
                  label-placement="floating"
                  type="password"
                  required
                  minlength="8"
                >
                </ion-input>
              </ion-item>

              <ion-item class="custom-item">
                <ion-icon :icon="lockClosedOutline" slot="start"></ion-icon>
                <ion-input
                  v-model="formData.confirmPassword"
                  label="確認新密碼"
                  label-placement="floating"
                  type="password"
                  required
                  minlength="8"
                >
                </ion-input>
              </ion-item>
            </ion-list>

            <div v-if="passwordError" class="error-message">
              {{ passwordError }}
            </div>

            <div class="form-actions">
              <ion-button type="submit" expand="block" class="submit-button" :disabled="isLoading">
                {{ isLoading ? '處理中...' : '重設密碼' }}
                <ion-icon slot="end" :icon="checkmarkOutline"></ion-icon>
              </ion-button>
            </div>
          </form>
        </div>
      </div>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { IonPage, IonContent, IonList, IonItem, IonInput, IonButton, IonIcon, IonToast, IonHeader, IonToolbar, IonTitle, IonButtons, IonBackButton } from '@ionic/vue';
import { lockClosedOutline, checkmarkOutline } from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import LogoImg from '@/components/LogoImg.vue';

const router = useRouter();
const route = useRoute();
const toastMessage = ref('');
const isLoading = ref(false);
const passwordError = ref('');

const formData = ref({
  password: '',
  confirmPassword: ''
});

const validatePasswords = () => {
  if (formData.value.password !== formData.value.confirmPassword) {
    passwordError.value = '兩次輸入的密碼不一致';
    return false;
  }
  
  if (formData.value.password.length < 8) {
    passwordError.value = '密碼長度必須至少為8個字符';
    return false;
  }
  
  passwordError.value = '';
  return true;
};

const handleResetPassword = async () => {
  if (isLoading.value) return;
  
  // Validate passwords
  if (!validatePasswords()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    // Update the user's password
    const { error } = await supabase.auth.updateUser({
      password: formData.value.password
    });
    
    if (error) {
      throw error;
    }
    
    // Show success message and redirect to login
    toastMessage.value = '密碼已成功重設，請使用新密碼登入';
    formData.value = { password: '', confirmPassword: '' };
    
    // Redirect to login after a short delay
    setTimeout(() => {
      router.push('/login');
    }, 2000);
  } catch (error) {
    console.error('Password reset error:', error);
    toastMessage.value = '重設密碼時發生錯誤，請稍後再試';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.reset-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  padding: 0;
}

.reset-card {
  width: 100%;
  max-width: 500px;
  border-radius: 24px;
  padding: 1rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  background: white;
}

.brand-section {
  text-align: center;
  margin-bottom: 2.5rem;
}

.brand-logo {
  width: 120px;
  margin: 0 auto 1rem;
}

.brand-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--ion-color-primary);
  margin: 0;
  line-height: 1.2;
}

.brand-subtitle {
  color: var(--ion-color-medium);
  font-size: 1.1rem;
  margin-top: 0.5rem;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-text-color);
}

.form-header p {
  color: var(--ion-color-medium);
  margin: 0.5rem 0 0;
}

.custom-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  --background: transparent;
  margin-bottom: 1.5rem;
  --border-color: var(--ion-color-medium);
  --border-style: solid;
  --border-width: 1px;
  --border-radius: 8px;
  --highlight-height: 0;
}

.custom-item::part(native) {
  padding: 0.75rem 1rem;
}

ion-input {
  --padding-start: 2.5rem;
  font-size: 1rem;
}

ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-medium);
}

.form-actions {
  margin-top: 2rem;
}

.submit-button {
  --background: var(--ion-color-primary);
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
  font-weight: 600;
  font-size: 1.1rem;
  height: 48px;
  margin: 0;
  text-transform: none;
}

.submit-button:disabled {
  --background: var(--ion-color-medium);
  --box-shadow: none;
  opacity: 0.7;
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  text-align: center;
}

/* Hover effects */
.submit-button:not(:disabled):hover {
  --background: var(--ion-color-primary-shade);
}

.custom-item:hover {
  --border-color: var(--ion-color-primary);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reset-card {
  animation: fadeInUp 0.6s ease-out;
}

.brand-section {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.form-header {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.reset-form ion-item {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.form-actions {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}
</style>
