<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button :default-href="`/shops/${shopId}`" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>編輯商家檔案</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="saveDraft" fill="clear" :disabled="isSaving">
            <ion-icon :icon="saveOutline" slot="start"></ion-icon>
            儲存草稿
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="!shop" class="error-container">
        <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
        <p>找不到商店或您沒有權限編輯此商店</p>
        <ion-button :router-link="`/shops/${shopId}`" fill="outline">
          返回商店頁面
        </ion-button>
      </div>

      <div v-else class="page-container">
        <ion-header collapse="condense">
          <ion-toolbar>
            <ion-title size="large">編輯商家檔案</ion-title>
          </ion-toolbar>
        </ion-header>

        <div class="profile-form-container ion-padding">
          <div class="form-intro">
            <h2>完善您的商家檔案</h2>
            <p>詳細的商家資料有助於客戶了解您的業務，並提升在搜尋結果中的曝光率。</p>
          </div>

          <ShopProfileForm
            ref="shopProfileFormRef"
            :shop="shop"
            :is-submitting="isSubmitting"
            @submit="handleSubmit"
            @save-draft="saveDraft"
          />
        </div>
      </div>

      <!-- Success Toast -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonBackButton,
  IonIcon,
  IonSpinner,
  IonToast,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  saveOutline,
  alertCircleOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import ShopProfileForm from '@/components/ShopProfileForm.vue';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const shopId = route.params.shopId as string;
const shop = ref<any>(null);
const shopProfileFormRef = ref<any>(null);
const isLoading = ref(true);
const isSubmitting = ref(false);
const isSaving = ref(false);
const toastMessage = ref('');

const loadShop = async () => {
  try {
    const { data, error } = await supabase
      .from('shops')
      .select('*')
      .eq('id', shopId)
      .single();

    if (error) throw error;

    // Check if current user is the owner
    if (data.owner_id !== authStore.currentUser?.id) {
      shop.value = null;
      return;
    }

    shop.value = data;
  } catch (error) {
    console.error('Error loading shop:', error);
    shop.value = null;
  }
};

const handleSubmit = async (formData: any) => {
  try {
    isSubmitting.value = true;

    const { error } = await supabase
      .from('shops')
      .update({
        ...formData,
        updated_at: new Date().toISOString()
      })
      .eq('id', shopId);

    if (error) throw error;

    toastMessage.value = '商家檔案已成功更新！';

    // Redirect back to shop detail page after a short delay
    setTimeout(() => {
      router.push(`/shops/${shopId}`);
    }, 2000);
  } catch (error: any) {
    console.error('Error updating shop profile:', error);
    toastMessage.value = '更新失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};

const saveDraft = async (formData?: any) => {
  try {
    isSaving.value = true;

    // If no formData provided, get it from the form component
    if (!formData && shopProfileFormRef.value) {
      shopProfileFormRef.value.saveDraft();
      return;
    }

    if (formData) {
      // Save the draft data to the database
      const { error } = await supabase
        .from('shops')
        .update({
          ...formData,
          updated_at: new Date().toISOString()
        })
        .eq('id', shopId);

      if (error) throw error;
    }

    toastMessage.value = '草稿已儲存';
  } catch (error: any) {
    console.error('Error saving draft:', error);
    toastMessage.value = '儲存草稿失敗';
  } finally {
    isSaving.value = false;
  }
};

onIonViewDidEnter(async () => {
  isLoading.value = true;
  await loadShop();
  isLoading.value = false;
});
</script>

<style scoped>
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  text-align: center;
  padding: 2rem;
}

.error-container ion-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.form-intro {
  margin-bottom: 2rem;
  text-align: center;
}

.form-intro h2 {
  color: var(--ion-color-primary);
  margin-bottom: 0.5rem;
}

.form-intro p {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  line-height: 1.4;
}

.profile-form-container {
  max-width: 800px;
  margin: 0 auto;
}
</style>
