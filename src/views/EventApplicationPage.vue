<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button :default-href="`/events/${eventId}`" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>活動報名</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <LoadingSpinner v-if="isLoading" />

      <div v-else-if="event" class="page-container">
        <!-- Event Summary -->
        <div class="event-summary">
          <h1>{{ event.title }}</h1>
          <ion-list>
            <ion-item lines="none">
              <ion-icon :icon="calendarOutline" slot="start"></ion-icon>
              <ion-label>
                <h2>日期及時間</h2>
                <p>{{ formatDateTime(event.start_datetime) }}</p>
                <p>至 {{ formatDateTime(event.end_datetime) }}</p>
              </ion-label>
            </ion-item>

            <ion-item lines="none">
              <ion-icon :icon="locationOutline" slot="start"></ion-icon>
              <ion-label>
                <h2>地點</h2>
                <p>{{ event.address }}</p>
              </ion-label>
            </ion-item>

            <ion-item lines="none" v-if="event.max_participants">
              <ion-icon :icon="peopleOutline" slot="start"></ion-icon>
              <ion-label>
                <h2>剩餘名額</h2>
                <p>{{ remainingSpots }} / {{ event.max_participants }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- Application Form -->
        <form @submit.prevent="handleSubmit" class="application-form">
          <ion-list>
            <ion-item>
              <ion-label position="stacked">
                姓名 *
                <span v-if="authStore.currentUser && userStore.currentUser" class="prefilled-note">(已自動填入)</span>
              </ion-label>
              <ion-input
                v-model="formData.fullName"
                type="text"
                required
                :readonly="!!(authStore.currentUser && userStore.currentUser)"
                placeholder="請輸入姓名"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">
                電郵地址 *
                <span v-if="authStore.currentUser && userStore.currentUser" class="prefilled-note">(已自動填入)</span>
              </ion-label>
              <ion-input
                v-model="formData.email"
                type="email"
                required
                :readonly="!!(authStore.currentUser && userStore.currentUser)"
                placeholder="請輸入電郵地址"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">
                電話號碼 *
                <span v-if="authStore.currentUser && userStore.currentUser" class="prefilled-note">(已自動填入)</span>
              </ion-label>
              <ion-input
                v-model="formData.phone"
                type="tel"
                required
                :readonly="!!(authStore.currentUser && userStore.currentUser)"
                placeholder="請輸入電話號碼"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">性別 (可選)</ion-label>
              <ion-select
                v-model="formData.gender"
                placeholder="請選擇性別"
                interface="popover"
              >
                <ion-select-option value="male">男</ion-select-option>
                <ion-select-option value="female">女</ion-select-option>
                <ion-select-option value="other">其他</ion-select-option>
                <ion-select-option value="prefer_not_to_say">不願透露</ion-select-option>
              </ion-select>
            </ion-item>
          </ion-list>

          <div class="ion-padding">
            <ion-button
              type="submit"
              expand="block"
              :disabled="isSubmitting || !isFormValid"
            >
              <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
              <span v-else>提交報名</span>
            </ion-button>
          </div>
        </form>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline" color="danger"></ion-icon>
        <p>找不到活動</p>
      </div>

      <!-- Toast Messages -->
      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonButtons,
  IonBackButton,
  IonIcon,
  IonSpinner,
  IonToast,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  calendarOutline,
  locationOutline,
  peopleOutline,
  alertCircleOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { useAlert } from '@/composables/useAlert';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const { presentPrompt } = useAlert();
const eventId = route.params.id as string;

const event = ref<any>(null);
const isLoading = ref(true);
const isSubmitting = ref(false);
const toastMessage = ref('');
const registrationCount = ref(0);

const formData = ref({
  fullName: '',
  email: '',
  phone: '',
  gender: '',
});

const remainingSpots = computed(() => {
  if (!event.value?.max_participants) return '不限';
  return event.value.max_participants - registrationCount.value;
});

const isFormValid = computed(() => {
  return (
    formData.value.fullName.trim() !== '' &&
    formData.value.email.trim() !== '' &&
    formData.value.phone.trim() !== ''
  );
});

onIonViewDidEnter(async () => {
  await loadEventDetails();

  // Pre-fill form if user is logged in and user data is available
  if (authStore.currentUser && userStore.currentUser) {
    formData.value = {
      fullName: userStore.currentUser.full_name || '',
      email: userStore.currentUser.email || '',
      phone: userStore.currentUser.phone || '',
      gender: '', // Keep gender empty for user to choose
    };
  }
});

const loadEventDetails = async () => {
  try {
    isLoading.value = true;

    // Load event details
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (eventError) throw eventError;
    event.value = eventData;

    // Get registration count
    const { count } = await supabase
      .from('event_applications')
      .select('id', { count: 'exact' })
      .eq('event_id', eventId)
      .in('status', ['confirmed', 'attended']);

    registrationCount.value = count || 0;
  } catch (error) {
    console.error('Error loading event:', error);
    toastMessage.value = '載入活動資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Format datetime for display (assuming input is already in HKT)
const formatDateTime = (datetimeStr: string) => {
  if (!datetimeStr) return '';

  // Create a date object from the datetime string
  const date = new Date(datetimeStr);

  // Format the date
  return date.toLocaleString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const handleSubmit = async () => {
  if (isSubmitting.value) return;

  // Show confirmation prompt
  const { role } = await presentPrompt({
    header: '確認報名',
    message: `確定要報名參加「${event.value?.title}」嗎？`,
    buttons: [
      {
        text: '取消',
        role: 'cancel'
      },
      {
        text: '確定報名',
        role: 'confirm'
      }
    ]
  });

  if (role !== 'confirm') return;

  try {
    isSubmitting.value = true;

    const { data: application, error } = await supabase
      .from('event_applications')
      .insert({
        event_id: eventId,
        user_id: authStore.currentUser?.id,
        full_name: formData.value.fullName,
        email: formData.value.email,
        phone: formData.value.phone,
        gender: formData.value.gender || null,
        status: 'confirmed'
      })
      .select()
      .single();

    if (error) {
      if (error.message.includes('quota exceeded')) {
        toastMessage.value = '活動名額已滿';
      } else {
        throw error;
      }
      return;
    }

    // Send email notifications
    try {
      await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/event-notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({ applicationId: application.id }),
      });
    } catch (emailError) {
      console.error('Error sending email notifications:', emailError);
      // Don't fail the application if email fails
    }

    toastMessage.value = '報名成功！';

    // Redirect back to event detail page after a short delay
    setTimeout(() => {
      router.push(`/events/${eventId}`);
    }, 1500);
  } catch (error) {
    console.error('Error submitting application:', error);
    toastMessage.value = '報名失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.event-summary {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.event-summary h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem;
  color: var(--ion-color-dark);
}

.event-summary ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 1rem;
}

.event-summary ion-icon {
  color: var(--ion-color-primary);
  font-size: 1.5rem;
  margin-right: 1rem;
}

.event-summary h2 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--ion-color-medium);
}

.event-summary p {
  font-size: 1.1rem;
  margin: 0.25rem 0 0;
  color: var(--ion-color-dark);
}

.application-form {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.error-container {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.prefilled-note {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  font-weight: normal;
  margin-left: 0.5rem;
}

@media (max-width: 768px) {
  .event-summary,
  .application-form {
    padding: 1rem;
  }

  .event-summary h1 {
    font-size: 1.25rem;
  }

  .event-summary ion-icon {
    font-size: 1.25rem;
  }
}
</style>