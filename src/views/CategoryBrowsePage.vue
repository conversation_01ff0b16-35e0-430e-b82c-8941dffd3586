<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/products" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>產品分類</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <div class="categories-grid ion-padding">
          <ion-card
            v-for="category in categories"
            :key="category.id"
            class="category-card"
            button
            @click="navigateToCategory(category.id)"
            :style="{ 'background': `url(${category.image_link})` }"
          >
            <div class="category-overlay"></div>
            <ion-card-header>
              <ion-card-title>
                <h2>{{ category.title }}</h2>
              </ion-card-title>
              <ion-card-subtitle>
                <span>{{ categoryProductCounts[category.id] || 0 }} 件產品</span>
              </ion-card-subtitle>
            </ion-card-header>
          </ion-card>
        </div>

        <!-- Empty State -->
        <div v-if="!isLoading && categories.length === 0" class="empty-state ion-padding">
          <ion-icon :icon="gridOutline" color="medium"></ion-icon>
          <p>沒有找到產品分類</p>
        </div>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader, IonCardTitle, IonCardSubtitle,
  IonIcon,
  IonButtons,
  IonBackButton,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  gridOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

const router = useRouter();
const categories = ref<any[]>([]);
const categoryProductCounts = ref<Record<number, number>>({});
const isLoading = ref(true);

const loadData = async () => {
  try {
    isLoading.value = true;

    // Load categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('product_categories')
      .select('*')
      .order('id', { ascending: true });

    if (categoriesError) throw categoriesError;
    categories.value = categoriesData;

    // Get product counts for each category using individual counts
    // The group by approach doesn't work with the current Supabase client
    for (const category of categories.value) {
      const { count, error } = await supabase
        .from('products')
        .select('id', { count: 'exact', head: true })
        .eq('category_id', category.id.toString())
        .eq('status', 'active');

      if (!error) {
        categoryProductCounts.value[category.id] = count || 0;
      }
    }
  } catch (error) {
    console.error('Error loading categories:', error);
  } finally {
    isLoading.value = false;
  }
};

// Use Ionic lifecycle hook
onIonViewDidEnter(() => {
  loadData();
});

const navigateToCategory = (categoryId: number) => {
  router.push({
    path: '/products',
    query: { category: categoryId.toString() }
  });
};
</script>

<style scoped>
.page-container {
  max-width: 1400px;
  margin: 0 auto;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.category-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background-size: cover !important;
  background-position: center !important;
  height: 180px;
  position: relative;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.category-image-container {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.category-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4) 60%, rgba(0, 0, 0, 0.2));
  z-index: 1;
}

ion-card-header {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
  padding: 1.5rem 1rem;
  background: transparent;
}

ion-card-title h2 {
  color: white;
  margin: 0 0 0.5rem;
  font-size: 1.4rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

ion-card-subtitle {
  color: white !important;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.empty-state {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0;
  color: var(--ion-color-medium);
}

@media (max-width: 480px) {
  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .category-card {
    height: 160px;
  }

  ion-card-header {
    padding: 1rem 0.75rem;
  }

  ion-card-title h2 {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
  }

  ion-card-subtitle {
    font-size: 0.8rem;
  }
}

@media (min-width: 481px) and (max-width: 767px) {
  .categories-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.25rem;
    padding: 1.25rem;
  }
}

@media (min-width: 768px) {
  .categories-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
    padding: 2rem;
  }

  .category-card {
    height: 220px;
  }

  ion-card-title h2 {
    font-size: 1.5rem;
  }
}

@media (min-width: 1200px) {
  .categories-grid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2.5rem;
    padding: 2.5rem;
  }

  .category-card {
    height: 250px;
  }
}
</style>
