<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/shops" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>我的收藏商家</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <LoadingSpinner v-if="isLoading" />

      <div v-else class="page-container">
        <div class="shops-container ion-padding">
          <!-- Liked Shops -->
          <div class="all-shops">
            <div class="shops-grid">
              <ion-card
                v-for="shop in likedShops"
                :key="shop.id"
                class="shop-card"
                button
                :router-link="`/shops/${shop.id}`"
              >
                <div class="banner-container">
                  <img :src="shop.banner || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8'" :alt="shop.name" class="shop-banner" />
                  <div class="shop-actions">
                    <ion-button fill="clear" @click.prevent.stop="toggleFavorite(shop)" class="action-button">
                      <ion-icon :icon="heart" />
                    </ion-button>
                  </div>
                </div>
                <ion-card-header>
                  <div class="shop-info">
                    <img :src="shop.logo" :alt="shop.name" class="shop-logo" v-if="shop.logo" />
                    <ion-card-title>{{ shop.name }}</ion-card-title>
                  </div>
                  <ion-card-subtitle v-if="shop.description">{{ shop.description }}</ion-card-subtitle>
                </ion-card-header>
              </ion-card>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="!isLoading && likedShops.length === 0" class="empty-state">
            <ion-icon :icon="heartOutline" color="medium"></ion-icon>
            <p>您還沒有收藏任何商家</p>
            <ion-button router-link="/shops" expand="block" class="browse-button">
              瀏覽商家
            </ion-button>
          </div>
        </div>

        <!-- Toast Messages -->
        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonButton,
  IonIcon,
  IonToast,
  IonButtons,
  IonBackButton,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  heartOutline,
  heart,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import LoadingSpinner from '@/components/LoadingSpinner.vue';

const router = useRouter();
const authStore = useAuthStore();
const likedShops = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');

// Check if user is authenticated
onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
  }
});

const loadData = async () => {
  try {
    isLoading.value = true;

    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      router.push('/login');
      return;
    }

    // Load user's liked shops
    const { data: likedData, error: likedError } = await supabase
      .from('user_liked_shops')
      .select('shop_id')
      .eq('user_id', authStore.currentUser.id);

    if (likedError) throw likedError;

    if (likedData && likedData.length > 0) {
      // Get the shop details for liked shops
      const shopIds = likedData.map((item: { shop_id: string }) => item.shop_id);

      const { data: shopsData, error: shopsError } = await supabase
        .from('shops')
        .select('*')
        .in('id', shopIds);

      if (shopsError) throw shopsError;
      likedShops.value = shopsData;
    } else {
      likedShops.value = [];
    }
  } catch (error) {
    console.error('Error loading liked shops:', error);
    toastMessage.value = '載入資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Use Ionic lifecycle hook
onIonViewDidEnter(() => {
  loadData();
});

const toggleFavorite = async (shop: any) => {
  try {
    // Remove from favorites in database
    const { error } = await supabase
      .from('user_liked_shops')
      .delete()
      .eq('user_id', authStore.currentUser?.id)
      .eq('shop_id', shop.id);

    if (error) throw error;

    // Update like count in database (this happens automatically via trigger)
    // But we need to update the UI immediately
    if (shop.like_count > 0) {
      shop.like_count -= 1;
    }

    // Remove from local list
    likedShops.value = likedShops.value.filter(s => s.id !== shop.id);
    toastMessage.value = '已從收藏移除';
  } catch (error) {
    console.error('Error removing from favorites:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  }
};
</script>

<style scoped>
.shops-container {
  width: 100%;
}

.shops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.shop-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.shop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.banner-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.shop-banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.shop-card:hover .shop-banner {
  transform: scale(1.05);
}

.shop-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 0.25rem;
  opacity: 1;
  transform: translateX(0);
  z-index: 10;
}

.action-button {
  --background: transparent;
  --border-radius: 4px;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  margin: 0;
  width: 36px;
  height: 36px;
}

.action-button ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-primary);
}

ion-card-header {
  padding: 1rem;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.shop-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
}

ion-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

ion-card-subtitle {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin: 0;
}

.empty-state {
  height: 50vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1.5rem;
  color: var(--ion-color-medium);
}

.browse-button {
  max-width: 200px;
}

@media (max-width: 768px) {
  .shops-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .banner-container {
    height: 140px;
  }

  .shop-logo {
    width: 32px;
    height: 32px;
  }

  ion-card-title {
    font-size: 1.1rem;
  }
}
</style>
