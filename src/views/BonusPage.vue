<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile" />
        </ion-buttons>
        <ion-title>獎金記錄</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <ion-card>
          <ion-card-header>
            <ion-card-subtitle>當前餘額</ion-card-subtitle>
            <ion-card-title class="balance">HK$ {{ totalBalance }}</ion-card-title>
          </ion-card-header>
        </ion-card>

        <ion-list>
          <ion-item-group>
            <ion-item-divider>
              <ion-label>最近記錄</ion-label>
            </ion-item-divider>
            <ion-item v-for="record in bonusRecords" :key="record.id">
              <ion-label>
                <h2>{{ record.source }}</h2>
                <p>{{ formatDate(record.date) }}</p>
              </ion-label>
              <ion-note slot="end" :color="record.amount >= 0 ? 'success' : 'danger'">
                {{ record.amount >= 0 ? '+' : '' }}HK$ {{ record.amount }}
              </ion-note>
            </ion-item>
          </ion-item-group>
        </ion-list>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonList,
  IonItemGroup,
  IonItemDivider,
  IonItem,
  IonLabel,
  IonNote,
  IonBackButton,
} from '@ionic/vue';
import { useAuthStore } from '@/stores/auth';
import { api } from '@/services/api';
import type { BonusRecord } from '@/services/schema';

const authStore = useAuthStore();
const bonusRecords = ref<BonusRecord[]>([]);

const totalBalance = computed(() => {
  return bonusRecords.value.reduce((sum, record) => sum + record.amount, 0);
});

onMounted(async () => {
  if (authStore.currentUser) {
    try {
      bonusRecords.value = await api.getBonusRecords(authStore.currentUser.id);
    } catch (error) {
      console.error('Error fetching bonus records:', error);
    }
  }
});

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
</script>

<style scoped>
.balance {
  font-size: 2rem;
  color: var(--ion-color-success);
}
</style>