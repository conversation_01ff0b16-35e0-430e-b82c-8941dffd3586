<template>
  <form @submit.prevent="handleSubmit">
    <!-- Required Section -->
    <ion-card class="form-section">
      <ion-card-header>
        <ion-card-title>
          <ion-icon :icon="businessOutline" class="section-icon"></ion-icon>
          必填資料
        </ion-card-title>
        <ion-card-subtitle>這些是每個商家必須提供的核心資料</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <!-- Shop Name -->
          <ion-item>
            <ion-label position="stacked">商家名稱 *</ion-label>
            <ion-input
              v-model="formData.name"
              placeholder="請提供您的商家或公司全名"
              required
            ></ion-input>
            <ion-note v-if="errors.name" color="danger">{{ errors.name }}</ion-note>
          </ion-item>

          <!-- Business Type -->
          <ion-item>
            <ion-label position="stacked">業務類型 *</ion-label>
            <ion-select v-model="formData.business_type" placeholder="請選擇您的業務類型">
              <ion-select-option value="零售(實體店)">零售(實體店)</ion-select-option>
              <ion-select-option value="服務業(諮詢)">服務業(諮詢)</ion-select-option>
              <ion-select-option value="製造業">製造業</ion-select-option>
              <ion-select-option value="餐飲業">餐飲業</ion-select-option>
              <ion-select-option value="線上商店">線上商店</ion-select-option>
              <ion-select-option value="其他">其他</ion-select-option>
            </ion-select>
            <ion-note v-if="errors.business_type" color="danger">{{ errors.business_type }}</ion-note>
          </ion-item>

          <!-- Main Products/Services -->
          <ion-item>
            <ion-label position="stacked">主要產品或服務 * (最多5項)</ion-label>
            <div class="multi-input-container">
              <div v-for="(item, index) in formData.main_products_services" :key="index" class="input-row">
                <ion-input
                  v-model="formData.main_products_services[index]"
                  :placeholder="`產品/服務 ${index + 1}`"
                ></ion-input>
                <ion-button
                  v-if="formData.main_products_services.length > 1"
                  fill="clear"
                  color="danger"
                  @click="removeProductService(index)"
                >
                  <ion-icon :icon="removeOutline"></ion-icon>
                </ion-button>
              </div>
              <ion-button
                v-if="formData.main_products_services.length < 5"
                fill="outline"
                size="small"
                @click="addProductService"
              >
                <ion-icon :icon="addOutline" slot="start"></ion-icon>
                新增項目
              </ion-button>
            </div>
            <ion-note v-if="errors.main_products_services" color="danger">{{ errors.main_products_services }}</ion-note>
          </ion-item>

          <!-- Service Area -->
          <ion-item>
            <ion-label position="stacked">服務範圍 *</ion-label>
            <ion-input
              v-model="formData.service_area"
              placeholder="例如：本地(旺角)、全港、全國、國際(日本、美國)"
              required
            ></ion-input>
            <ion-note v-if="errors.service_area" color="danger">{{ errors.service_area }}</ion-note>
          </ion-item>

          <!-- Contact Information -->
          <ion-item>
            <ion-label position="stacked">聯絡電話 *</ion-label>
            <ion-input
              v-model="formData.contact_phone"
              type="tel"
              placeholder="+852 12345678"
              required
            ></ion-input>
            <ion-note v-if="errors.contact_phone" color="danger">{{ errors.contact_phone }}</ion-note>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">聯絡電郵 *</ion-label>
            <ion-input
              v-model="formData.contact_email"
              type="email"
              placeholder="<EMAIL>"
              required
            ></ion-input>
            <ion-note v-if="errors.contact_email" color="danger">{{ errors.contact_email }}</ion-note>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">地址/網址 *</ion-label>
            <ion-textarea
              v-model="formData.physical_address"
              placeholder="實體店地址或線上店網址"
              :rows="2"
              required
            ></ion-textarea>
            <ion-note v-if="errors.physical_address" color="danger">{{ errors.physical_address }}</ion-note>
          </ion-item>

          <!-- Operating Hours -->
          <ion-item>
            <ion-label position="stacked">營業時間 *</ion-label>
            <ion-textarea
              v-model="formData.operating_hours"
              placeholder="例如：星期一至五 9:00-18:00、星期六 10:00-14:00、星期日休息"
              :rows="3"
              required
            ></ion-textarea>
            <ion-note v-if="errors.operating_hours" color="danger">{{ errors.operating_hours }}</ion-note>
          </ion-item>

          <!-- Languages Supported -->
          <ion-item>
            <ion-label position="stacked">語言支持 *</ion-label>
            <ion-select v-model="formData.languages_supported" multiple placeholder="選擇支持的語言">
              <ion-select-option value="粵語">粵語</ion-select-option>
              <ion-select-option value="英語">英語</ion-select-option>
              <ion-select-option value="普通話">普通話</ion-select-option>
              <ion-select-option value="日語">日語</ion-select-option>
              <ion-select-option value="韓語">韓語</ion-select-option>
              <ion-select-option value="其他">其他</ion-select-option>
            </ion-select>
            <ion-note v-if="errors.languages_supported" color="danger">{{ errors.languages_supported }}</ion-note>
          </ion-item>

          <!-- Payment Methods -->
          <ion-item>
            <ion-label position="stacked">支付方式 *</ion-label>
            <ion-select v-model="formData.payment_methods" multiple placeholder="選擇接受的支付方式">
              <ion-select-option value="現金">現金</ion-select-option>
              <ion-select-option value="Visa">Visa</ion-select-option>
              <ion-select-option value="Mastercard">Mastercard</ion-select-option>
              <ion-select-option value="PayMe">PayMe</ion-select-option>
              <ion-select-option value="轉數快">轉數快</ion-select-option>
              <ion-select-option value="支付寶">支付寶</ion-select-option>
              <ion-select-option value="微信支付">微信支付</ion-select-option>
              <ion-select-option value="其他">其他</ion-select-option>
            </ion-select>
            <ion-note v-if="errors.payment_methods" color="danger">{{ errors.payment_methods }}</ion-note>
          </ion-item>

          <!-- Business Years -->
          <ion-item>
            <ion-label position="stacked">業務年限 *</ion-label>
            <ion-select v-model="formData.business_years" placeholder="請選擇業務年限">
              <ion-select-option value="少於1年">少於1年</ion-select-option>
              <ion-select-option value="1-2年">1-2年</ion-select-option>
              <ion-select-option value="3-5年">3-5年</ion-select-option>
              <ion-select-option value="6-10年">6-10年</ion-select-option>
              <ion-select-option value="10年以上">10年以上</ion-select-option>
            </ion-select>
            <ion-note v-if="errors.business_years" color="danger">{{ errors.business_years }}</ion-note>
          </ion-item>

          <!-- Product/Service Specifications -->
          <ion-item>
            <ion-label position="stacked">產品或服務規格 *</ion-label>
            <ion-textarea
              v-model="formData.product_service_specs"
              placeholder="例如：產品(T恤: M號, 100%棉, 200克)、服務(清潔: 2小時, 吸塵+抹窗)"
              :rows="3"
              required
            ></ion-textarea>
            <ion-note v-if="errors.product_service_specs" color="danger">{{ errors.product_service_specs }}</ion-note>
          </ion-item>

          <!-- Return Policy -->
          <ion-item>
            <ion-label position="stacked">退貨政策 *</ion-label>
            <ion-textarea
              v-model="formData.return_policy"
              placeholder="例如：7天內未使用可退貨、30天內有質量問題可換貨"
              :rows="2"
              required
            ></ion-textarea>
            <ion-note v-if="errors.return_policy" color="danger">{{ errors.return_policy }}</ion-note>
          </ion-item>

          <!-- Business Registration Number -->
          <ion-item>
            <ion-label position="stacked">商家註冊號碼 *</ion-label>
            <ion-input
              v-model="formData.business_registration_number"
              placeholder="例如：香港BR12345678"
              required
            ></ion-input>
            <ion-note v-if="errors.business_registration_number" color="danger">{{ errors.business_registration_number }}</ion-note>
          </ion-item>

          <!-- Emergency Contact -->
          <ion-item>
            <ion-label position="stacked">緊急聯絡人姓名 *</ion-label>
            <ion-input
              v-model="formData.emergency_contact_name"
              placeholder="例如：張先生"
              required
            ></ion-input>
            <ion-note v-if="errors.emergency_contact_name" color="danger">{{ errors.emergency_contact_name }}</ion-note>
          </ion-item>

          <ion-item>
            <ion-label position="stacked">緊急聯絡人電話 *</ion-label>
            <ion-input
              v-model="formData.emergency_contact_phone"
              type="tel"
              placeholder="+852 98765432"
              required
            ></ion-input>
            <ion-note v-if="errors.emergency_contact_phone" color="danger">{{ errors.emergency_contact_phone }}</ion-note>
          </ion-item>

          <!-- Shipping/Delivery Info -->
          <ion-item>
            <ion-label position="stacked">運輸/送貨信息 *</ion-label>
            <ion-textarea
              v-model="formData.shipping_delivery_info"
              placeholder="例如：全港外送, 滿$500免運費; 國際運費$100起"
              :rows="2"
              required
            ></ion-textarea>
            <ion-note v-if="errors.shipping_delivery_info" color="danger">{{ errors.shipping_delivery_info }}</ion-note>
          </ion-item>

          <!-- Target Market -->
          <ion-item>
            <ion-label position="stacked">目標市場 *</ion-label>
            <ion-input
              v-model="formData.target_market"
              placeholder="例如：香港本地、台灣、日本"
              required
            ></ion-input>
            <ion-note v-if="errors.target_market" color="danger">{{ errors.target_market }}</ion-note>
          </ion-item>

          <!-- Keywords -->
          <ion-item>
            <ion-label position="stacked">關鍵字標籤 * (至少3個)</ion-label>
            <div class="multi-input-container">
              <div v-for="(keyword, index) in formData.keywords" :key="index" class="input-row">
                <ion-input
                  v-model="formData.keywords[index]"
                  :placeholder="`關鍵字 ${index + 1}`"
                ></ion-input>
                <ion-button
                  v-if="formData.keywords.length > 3"
                  fill="clear"
                  color="danger"
                  @click="removeKeyword(index)"
                >
                  <ion-icon :icon="removeOutline"></ion-icon>
                </ion-button>
              </div>
              <ion-button
                v-if="formData.keywords.length < 10"
                fill="outline"
                size="small"
                @click="addKeyword"
              >
                <ion-icon :icon="addOutline" slot="start"></ion-icon>
                新增關鍵字
              </ion-button>
            </div>
            <ion-note v-if="errors.keywords" color="danger">{{ errors.keywords }}</ion-note>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Optional Sections -->
    <ion-accordion-group>
      <!-- Product/Service Related -->
      <ion-accordion value="product-service">
        <ion-item slot="header" color="light">
          <ion-icon :icon="cubeOutline" slot="start"></ion-icon>
          <ion-label>產品或服務相關 (可選填)</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-list>
            <!-- Detailed Description -->
            <ion-item>
              <ion-label position="stacked">產品或服務詳細描述</ion-label>
              <ion-textarea
                v-model="formData.detailed_description"
                placeholder="例如：產品(有機蜂蜜: 100%天然, 澳洲產)、服務(瑜伽班: 60分鐘, 專注減壓)"
                :rows="3"
              ></ion-textarea>
            </ion-item>

            <!-- Customization Options -->
            <ion-item>
              <ion-label position="stacked">客製化選項</ion-label>
              <ion-input
                v-model="formData.customization_options"
                placeholder="例如：可訂製蛋糕款式(+$100起)"
              ></ion-input>
            </ion-item>

            <!-- Trial/Samples -->
            <ion-item>
              <ion-label position="stacked">試用或樣品</ion-label>
              <ion-input
                v-model="formData.trial_samples"
                placeholder="例如：免費試用軟件7天、樣品咖啡包$10"
              ></ion-input>
            </ion-item>

            <!-- Maintenance Service -->
            <ion-item>
              <ion-label position="stacked">保養或售後服務</ion-label>
              <ion-textarea
                v-model="formData.maintenance_service"
                placeholder="例如：電子產品1年保養、免費上門維修"
                :rows="2"
              ></ion-textarea>
            </ion-item>

            <!-- Delivery/Logistics -->
            <ion-item>
              <ion-label position="stacked">外送或物流選項</ion-label>
              <ion-input
                v-model="formData.delivery_logistics"
                placeholder="例如：全港外送, 滿$500免運費"
              ></ion-input>
            </ion-item>

            <!-- Product Language Labels -->
            <ion-item>
              <ion-label position="stacked">產品語言標籤</ion-label>
              <ion-input
                v-model="formData.product_language_labels"
                placeholder="例如：有日文同英語標籤"
              ></ion-input>
            </ion-item>

            <!-- Installation Service -->
            <ion-item>
              <ion-label position="stacked">安裝服務</ion-label>
              <ion-input
                v-model="formData.installation_service"
                placeholder="例如：家具提供免費安裝"
              ></ion-input>
            </ion-item>

            <!-- Wholesale Pricing -->
            <ion-item>
              <ion-label position="stacked">批發價及最低訂單量</ion-label>
              <ion-input
                v-model="formData.wholesale_pricing"
                placeholder="例如：批發價$50/件, 最低訂單100件"
              ></ion-input>
            </ion-item>
          </ion-list>
        </div>
      </ion-accordion>

      <!-- Target Audience -->
      <ion-accordion value="target-audience">
        <ion-item slot="header" color="light">
          <ion-icon :icon="peopleOutline" slot="start"></ion-icon>
          <ion-label>目標客戶群 (可選填)</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-list>
            <!-- Target Customer Description -->
            <ion-item>
              <ion-label position="stacked">目標客戶群描述</ion-label>
              <ion-textarea
                v-model="formData.target_customers"
                placeholder="例如：25-40歲女性, 中高收入, 關注有機產品"
                :rows="2"
              ></ion-textarea>
            </ion-item>

            <!-- Customer Sources -->
            <ion-item>
              <ion-label position="stacked">客戶來源</ion-label>
              <ion-input
                v-model="formData.customer_sources"
                placeholder="例如：Instagram(50%)、實體店(30%)"
              ></ion-input>
            </ion-item>
          </ion-list>
        </div>
      </ion-accordion>

      <!-- Collaboration Related -->
      <ion-accordion value="collaboration">
        <ion-item slot="header" color="light">
          <ion-icon :icon="peopleOutline" slot="start"></ion-icon>
          <ion-label>合作相關 (可選填)</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-list>
            <!-- Collaboration Willingness -->
            <ion-item>
              <ion-label position="stacked">合作意願</ion-label>
              <ion-textarea
                v-model="formData.collaboration_willingness"
                placeholder="例如：提供批發價, 最低訂單$3000、代理、加盟"
                :rows="2"
              ></ion-textarea>
            </ion-item>

            <!-- Revenue Sharing Model -->
            <ion-item>
              <ion-label position="stacked">收益分配模式</ion-label>
              <ion-input
                v-model="formData.revenue_sharing_model"
                placeholder="例如：銷售佣金15%, 每月結算"
              ></ion-input>
            </ion-item>

            <!-- Collaboration Review Process -->
            <ion-item>
              <ion-label position="stacked">合作審核流程</ion-label>
              <ion-input
                v-model="formData.collaboration_review_process"
                placeholder="例如：需提交營業執照, 審核3-5天"
              ></ion-input>
            </ion-item>

            <!-- Agency/Franchise Opportunities -->
            <ion-item>
              <ion-label position="stacked">代理或加盟機會</ion-label>
              <ion-input
                v-model="formData.agency_franchise_opportunities"
                placeholder="例如：加盟費$50000, 提供培訓"
              ></ion-input>
            </ion-item>
          </ion-list>
        </div>
      </ion-accordion>

      <!-- Qualifications & Reputation -->
      <ion-accordion value="qualifications">
        <ion-item slot="header" color="light">
          <ion-icon :icon="shieldCheckmarkOutline" slot="start"></ion-icon>
          <ion-label>資質與信譽 (可選填)</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-list>
            <!-- Customer Reviews/Cases -->
            <ion-item>
              <ion-label position="stacked">客戶評價或案例</ion-label>
              <ion-textarea
                v-model="formData.customer_reviews_cases"
                placeholder="例如：客戶A: 服務很好, 5星; 案例: 提升30%銷售"
                :rows="3"
              ></ion-textarea>
            </ion-item>

            <!-- Industry Certifications/Awards -->
            <ion-item>
              <ion-label position="stacked">行業認證或獎項</ion-label>
              <ion-input
                v-model="formData.industry_certifications_awards"
                placeholder="例如：ISO 9001認證、2023年最佳中小企獎"
              ></ion-input>
            </ion-item>

            <!-- Third Party Certifications -->
            <ion-item>
              <ion-label position="stacked">第三方認證</ion-label>
              <ion-input
                v-model="formData.third_party_certifications"
                placeholder="例如：Halal認證"
              ></ion-input>
            </ion-item>

            <!-- Company Insurance -->
            <ion-item>
              <ion-label position="stacked">公司保險保障</ion-label>
              <ion-input
                v-model="formData.company_insurance"
                placeholder="例如：有產品責任保險"
              ></ion-input>
            </ion-item>
          </ion-list>
        </div>
      </ion-accordion>

      <!-- Promotion & Resources -->
      <ion-accordion value="promotion">
        <ion-item slot="header" color="light">
          <ion-icon :icon="megaphoneOutline" slot="start"></ion-icon>
          <ion-label>推廣與資源 (可選填)</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-list>
            <!-- Social Media Links -->
            <ion-item>
              <ion-label position="stacked">Facebook 連結</ion-label>
              <ion-input
                v-model="formData.social_media_links.facebook"
                placeholder="https://facebook.com/yourpage"
                type="url"
              ></ion-input>
            </ion-item>

            <ion-item>
              <ion-label position="stacked">Instagram 連結</ion-label>
              <ion-input
                v-model="formData.social_media_links.instagram"
                placeholder="https://instagram.com/yourpage"
                type="url"
              ></ion-input>
            </ion-item>

            <!-- Promotions/Offers -->
            <ion-item>
              <ion-label position="stacked">優惠或促銷信息</ion-label>
              <ion-textarea
                v-model="formData.promotions_offers"
                placeholder="例如：新客首單9折, 至2025年12月31日"
                :rows="2"
              ></ion-textarea>
            </ion-item>

            <!-- Shareable Resources -->
            <ion-item>
              <ion-label position="stacked">可共享資源</ion-label>
              <ion-input
                v-model="formData.shareable_resources"
                placeholder="例如：場地租借, 容納30人, $300/小時"
              ></ion-input>
            </ion-item>

            <!-- Professional Consultation/Training -->
            <ion-item>
              <ion-label position="stacked">專業諮詢或培訓課程</ion-label>
              <ion-textarea
                v-model="formData.professional_consultation_training"
                placeholder="例如：營養諮詢, $500/小時; 銷售培訓, 2天課程"
                :rows="2"
              ></ion-textarea>
            </ion-item>
          </ion-list>
        </div>
      </ion-accordion>

      <!-- Background & Story -->
      <ion-accordion value="background">
        <ion-item slot="header" color="light">
          <ion-icon :icon="libraryOutline" slot="start"></ion-icon>
          <ion-label>背景與故事 (可選填)</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-list>
            <!-- Company Story -->
            <ion-item>
              <ion-label position="stacked">商家故事</ion-label>
              <ion-textarea
                v-model="formData.company_story"
                placeholder="例如：2015年創立, 致力推廣健康飲食"
                :rows="4"
              ></ion-textarea>
            </ion-item>

            <!-- Management Introduction -->
            <ion-item>
              <ion-label position="stacked">負責人介紹</ion-label>
              <ion-textarea
                v-model="formData.management_introduction"
                placeholder="例如：李小姐, 8年餐飲經驗, 持有食品安全證書"
                :rows="3"
              ></ion-textarea>
            </ion-item>

            <!-- Company Mission & Values -->
            <ion-item>
              <ion-label position="stacked">公司使命與價值觀</ion-label>
              <ion-textarea
                v-model="formData.company_mission_values"
                placeholder="例如：使命: 提供優質有機食品; 價值觀: 環保、誠信"
                :rows="3"
              ></ion-textarea>
            </ion-item>

            <!-- Transportation Guide -->
            <ion-item>
              <ion-label position="stacked">交通指南(實體商家)</ion-label>
              <ion-textarea
                v-model="formData.transportation_guide"
                placeholder="例如：旺角站A2出口, 步行5分鐘; 巴士路線68X"
                :rows="2"
              ></ion-textarea>
            </ion-item>

            <!-- Environmental/Social Responsibility -->
            <ion-item>
              <ion-label position="stacked">環保或社會責任承諾</ion-label>
              <ion-textarea
                v-model="formData.environmental_social_responsibility"
                placeholder="例如：使用可回收包裝、每年捐款慈善機構"
                :rows="2"
              ></ion-textarea>
            </ion-item>

            <!-- Long-term Development Plan -->
            <ion-item>
              <ion-label position="stacked">長期發展計劃</ion-label>
              <ion-textarea
                v-model="formData.long_term_development_plan"
                placeholder="例如：計劃3年內開設5間分店"
                :rows="2"
              ></ion-textarea>
            </ion-item>
          </ion-list>
        </div>
      </ion-accordion>
    </ion-accordion-group>

    <!-- Submit Button -->
    <div class="submit-section">
      <ion-button
        type="submit"
        expand="block"
        size="large"
        :disabled="isSubmitting"
      >
        <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
        <span v-else>更新商家檔案</span>
      </ion-button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonIcon,
  IonNote,
  IonSpinner,
  IonAccordionGroup,
  IonAccordion,
} from '@ionic/vue';
import {
  businessOutline,
  addOutline,
  removeOutline,
  cubeOutline,
  peopleOutline,
  shieldCheckmarkOutline,
  megaphoneOutline,
  libraryOutline,
} from 'ionicons/icons';

const props = defineProps<{
  shop: any;
  isSubmitting: boolean;
}>();

const emit = defineEmits<{
  (e: 'submit', formData: any): void;
  (e: 'save-draft', formData: any): void;
}>();

const formData = reactive({
  // Required fields
  name: '',
  business_type: '',
  main_products_services: [''],
  service_area: '',
  contact_phone: '',
  contact_email: '',
  physical_address: '',
  operating_hours: '',
  languages_supported: [],
  payment_methods: [],
  business_years: '',
  product_service_specs: '',
  return_policy: '',
  business_registration_number: '',
  emergency_contact_name: '',
  emergency_contact_phone: '',
  shipping_delivery_info: '',
  target_market: '',
  keywords: ['', '', ''], // Start with 3 empty keywords

  // Optional fields - Product/Service Related
  detailed_description: '',
  customization_options: '',
  trial_samples: '',
  maintenance_service: '',
  delivery_logistics: '',
  product_language_labels: '',
  installation_service: '',
  wholesale_pricing: '',

  // Optional fields - Target Audience
  target_customers: '',
  customer_sources: '',

  // Optional fields - Collaboration
  collaboration_willingness: '',
  revenue_sharing_model: '',
  collaboration_review_process: '',
  agency_franchise_opportunities: '',

  // Optional fields - Qualifications & Reputation
  customer_reviews_cases: '',
  industry_certifications_awards: '',
  third_party_certifications: '',
  company_insurance: '',

  // Optional fields - Promotion & Resources
  social_media_links: {
    facebook: '',
    instagram: '',
  },
  promotions_offers: '',
  shareable_resources: '',
  professional_consultation_training: '',

  // Optional fields - Background & Story
  company_story: '',
  management_introduction: '',
  company_mission_values: '',
  transportation_guide: '',
  environmental_social_responsibility: '',
  long_term_development_plan: '',
});

const errors = ref<Record<string, string>>({});

// Initialize form data when shop prop changes
watch(() => props.shop, (newShop) => {
  if (newShop) {
    Object.assign(formData, {
      // Required fields
      name: newShop.name || '',
      business_type: newShop.business_type || '',
      main_products_services: newShop.main_products_services || [''],
      service_area: newShop.service_area || '',
      contact_phone: newShop.contact_phone || '',
      contact_email: newShop.contact_email || '',
      physical_address: newShop.physical_address || '',
      operating_hours: newShop.operating_hours || '',
      languages_supported: newShop.languages_supported || [],
      payment_methods: newShop.payment_methods || [],
      business_years: newShop.business_years || '',
      product_service_specs: newShop.product_service_specs || '',
      return_policy: newShop.return_policy || '',
      business_registration_number: newShop.business_registration_number || '',
      emergency_contact_name: newShop.emergency_contact_name || '',
      emergency_contact_phone: newShop.emergency_contact_phone || '',
      shipping_delivery_info: newShop.shipping_delivery_info || '',
      target_market: newShop.target_market || '',
      keywords: newShop.keywords || ['', '', ''],

      // Optional fields
      detailed_description: newShop.detailed_description || '',
      customization_options: newShop.customization_options || '',
      trial_samples: newShop.trial_samples || '',
      maintenance_service: newShop.maintenance_service || '',
      delivery_logistics: newShop.delivery_logistics || '',
      product_language_labels: newShop.product_language_labels || '',
      installation_service: newShop.installation_service || '',
      wholesale_pricing: newShop.wholesale_pricing || '',
      target_customers: newShop.target_customers || '',
      customer_sources: newShop.customer_sources || '',
      collaboration_willingness: newShop.collaboration_willingness || '',
      revenue_sharing_model: newShop.revenue_sharing_model || '',
      collaboration_review_process: newShop.collaboration_review_process || '',
      agency_franchise_opportunities: newShop.agency_franchise_opportunities || '',

      // Qualifications & Reputation
      customer_reviews_cases: newShop.customer_reviews_cases || '',
      industry_certifications_awards: newShop.industry_certifications_awards || '',
      third_party_certifications: newShop.third_party_certifications || '',
      company_insurance: newShop.company_insurance || '',

      // Promotion & Resources
      social_media_links: {
        facebook: newShop.social_media_links?.facebook || '',
        instagram: newShop.social_media_links?.instagram || '',
      },
      promotions_offers: newShop.promotions_offers || '',
      shareable_resources: newShop.shareable_resources || '',
      professional_consultation_training: newShop.professional_consultation_training || '',

      // Background & Story
      company_story: newShop.company_story || '',
      management_introduction: newShop.management_introduction || '',
      company_mission_values: newShop.company_mission_values || '',
      transportation_guide: newShop.transportation_guide || '',
      environmental_social_responsibility: newShop.environmental_social_responsibility || '',
      long_term_development_plan: newShop.long_term_development_plan || '',
    });
  }
}, { immediate: true });

const addProductService = () => {
  if (formData.main_products_services.length < 5) {
    formData.main_products_services.push('');
  }
};

const removeProductService = (index: number) => {
  if (formData.main_products_services.length > 1) {
    formData.main_products_services.splice(index, 1);
  }
};

const addKeyword = () => {
  if (formData.keywords.length < 10) {
    formData.keywords.push('');
  }
};

const removeKeyword = (index: number) => {
  if (formData.keywords.length > 3) {
    formData.keywords.splice(index, 1);
  }
};

const validateForm = () => {
  errors.value = {};

  // Required field validations
  if (!formData.name.trim()) {
    errors.value.name = '商家名稱為必填項目';
  }

  if (!formData.business_type) {
    errors.value.business_type = '請選擇業務類型';
  }

  if (!formData.service_area.trim()) {
    errors.value.service_area = '服務範圍為必填項目';
  }

  if (!formData.contact_phone.trim()) {
    errors.value.contact_phone = '聯絡電話為必填項目';
  }

  if (!formData.contact_email.trim()) {
    errors.value.contact_email = '聯絡電郵為必填項目';
  }

  if (!formData.physical_address.trim()) {
    errors.value.physical_address = '地址/網址為必填項目';
  }

  if (!formData.operating_hours.trim()) {
    errors.value.operating_hours = '營業時間為必填項目';
  }

  if (!formData.languages_supported.length) {
    errors.value.languages_supported = '請選擇至少一種語言支持';
  }

  if (!formData.payment_methods.length) {
    errors.value.payment_methods = '請選擇至少一種支付方式';
  }

  if (!formData.business_years) {
    errors.value.business_years = '請選擇業務年限';
  }

  if (!formData.product_service_specs.trim()) {
    errors.value.product_service_specs = '產品或服務規格為必填項目';
  }

  if (!formData.return_policy.trim()) {
    errors.value.return_policy = '退貨政策為必填項目';
  }

  if (!formData.business_registration_number.trim()) {
    errors.value.business_registration_number = '商家註冊號碼為必填項目';
  }

  if (!formData.emergency_contact_name.trim()) {
    errors.value.emergency_contact_name = '緊急聯絡人姓名為必填項目';
  }

  if (!formData.emergency_contact_phone.trim()) {
    errors.value.emergency_contact_phone = '緊急聯絡人電話為必填項目';
  }

  if (!formData.shipping_delivery_info.trim()) {
    errors.value.shipping_delivery_info = '運輸/送貨信息為必填項目';
  }

  if (!formData.target_market.trim()) {
    errors.value.target_market = '目標市場為必填項目';
  }

  // Validate keywords (at least 3 non-empty)
  const validKeywords = formData.keywords.filter(k => k.trim() !== '');
  if (validKeywords.length < 3) {
    errors.value.keywords = '請提供至少3個關鍵字';
  }

  // Validate main products/services (at least 1 non-empty)
  const validProducts = formData.main_products_services.filter(p => p.trim() !== '');
  if (validProducts.length === 0) {
    errors.value.main_products_services = '請提供至少一項主要產品或服務';
  }

  return Object.keys(errors.value).length === 0;
};

const handleSubmit = () => {
  if (validateForm()) {
    // Filter out empty entries
    const cleanedData = {
      ...formData,
      main_products_services: formData.main_products_services.filter(item => item.trim() !== ''),
      keywords: formData.keywords.filter(item => item.trim() !== '')
    };
    emit('submit', cleanedData);
  }
};

const handleSaveDraft = () => {
  // Save draft without validation
  const cleanedData = {
    ...formData,
    main_products_services: formData.main_products_services.filter(item => item.trim() !== ''),
    keywords: formData.keywords.filter(item => item.trim() !== '')
  };
  emit('save-draft', cleanedData);
};

// Expose the save draft function so parent can call it
defineExpose({
  saveDraft: handleSaveDraft
});
</script>

<style scoped>
.form-section {
  margin-bottom: 1.5rem;
}

.section-icon {
  margin-right: 0.5rem;
  color: var(--ion-color-primary);
}

.multi-input-container {
  width: 100%;
  padding: 0.5rem 0;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.input-row ion-input {
  flex: 1;
  margin-right: 0.5rem;
}

.submit-section {
  padding: 1rem 0 2rem 0;
}
</style>
