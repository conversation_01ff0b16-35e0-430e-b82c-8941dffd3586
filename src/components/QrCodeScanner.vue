<template>
  <div class="qr-scanner">
    <div v-if="!hasError" id="qr-reader" class="reader-container"></div>
    <div v-else class="error-container">
      <p>{{ errorMessage }}</p>
      <ion-button @click="requestCameraPermission">
        允許使用相機
      </ion-button>
    </div>
    <div class="scanner-overlay">
      <div class="scanner-frame"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { Html5Qrcode } from 'html5-qrcode';
import { Camera } from '@capacitor/camera';
import { isPlatform, IonButton } from '@ionic/vue';

const props = defineProps<{
  active: boolean;
}>();

const emit = defineEmits<{
  (e: 'codeScanned', code: string): void;
}>();

let html5QrCode: Html5Qrcode | null = null;
const isInitialized = ref(false);
const hasCamera = ref(true);
const hasError = ref(false);
const errorMessage = ref('無法啟動相機，請檢查相機權限');

// Initialize scanner on mount
onMounted(async () => {
  // Request camera permissions first
  if (props.active) {
    await requestCameraPermission();
  }
});

// Watch for changes to active prop
watch(() => props.active, async (newValue) => {
  if (newValue) {
    if (!isInitialized.value) {
      await requestCameraPermission();
    } else {
      await startScanner();
    }
  } else if (isInitialized.value && html5QrCode) {
    await stopScanner();
  }
});

// Request camera permission
const requestCameraPermission = async () => {
  try {
    hasError.value = false;
    
    // For native mobile apps, use Capacitor Camera API
    if (isPlatform('capacitor')) {
      const permission = await Camera.requestPermissions({ permissions: ['camera'] });
      if (permission.camera !== 'granted') {
        throw new Error('Camera permission not granted');
      }
    }
    
    // Initialize QR scanner
    if (!isInitialized.value) {
      html5QrCode = new Html5Qrcode('qr-reader');
      isInitialized.value = true;
    }
    
    // Start scanning
    if (props.active) {
      await startScanner();
    }
  } catch (error) {
    console.error('Error requesting camera permission:', error);
    hasError.value = true;
    errorMessage.value = '無法啟動相機，請檢查相機權限';
  }
};

// Start the scanner
const startScanner = async () => {
  if (!isInitialized.value || !html5QrCode) return;
  
  try {
    // Check if camera is already running
    if (html5QrCode.isScanning) {
      await stopScanner();
    }
    
    const config = {
      fps: 10,
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0,
    };

    await html5QrCode.start(
      { facingMode: 'environment' },
      config,
      (decodedText) => {
        emit('codeScanned', decodedText);
        // Don't stop scanner here - let parent component decide
      },
      (errorMessage) => {
        // Ignore errors during scanning
        console.debug('QR Scan Error:', errorMessage);
      }
    );
    
    hasCamera.value = true;
    hasError.value = false;
  } catch (error) {
    console.error('Error starting QR scanner:', error);
    hasCamera.value = false;
    hasError.value = true;
    errorMessage.value = '無法啟動相機，請檢查相機權限';
  }
};

// Stop the scanner
const stopScanner = async () => {
  if (!isInitialized.value || !html5QrCode || !html5QrCode.isScanning) return;
  
  try {
    await html5QrCode.stop();
  } catch (error) {
    console.error('Error stopping QR scanner:', error);
  }
};

// Clean up on unmount
onUnmounted(() => {
  if (html5QrCode && html5QrCode.isScanning) {
    html5QrCode.stop().catch(error => {
      console.error('Error stopping QR scanner:', error);
    });
  }
});
</script>

<style scoped>
.qr-scanner {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 16px;
}

.reader-container {
  width: 100%;
  height: 100%;
}

#qr-reader {
  width: 100%;
  height: 100%;
  background: black;
}

#qr-reader video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.error-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  padding: 1rem;
}

.error-container p {
  margin-bottom: 1rem;
}

/* Hide default HTML5-QRCode elements */
#qr-reader__status_span,
#qr-reader__dashboard_section_swaplink {
  display: none !important;
}

#qr-reader__dashboard_section_csr button {
  background: var(--ion-color-primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
}

.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.scanner-frame {
  width: 250px;
  height: 250px;
  border: 2px solid var(--ion-color-primary);
  border-radius: 16px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}
</style>
