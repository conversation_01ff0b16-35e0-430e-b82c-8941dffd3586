<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>設置頭像</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div class="avatar-container">
        <!-- Image Preview -->
        <div v-if="imagePreview" class="image-preview-container">
          <!-- Avatar Preview Circle -->
          <div class="avatar-preview">
            <div class="avatar-circle">
              <img :src="imagePreview" alt="Avatar Preview" class="avatar-image" />
            </div>
          </div>

          <!-- Full Image Preview -->
          <div class="full-image-preview">
            <img :src="imagePreview" alt="Full Preview" class="preview-image" />
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <ion-button fill="outline" @click="takeAvatarPhoto">
              <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
              選擇其他照片
            </ion-button>
          </div>
        </div>

        <!-- Upload Button (when no image is selected) -->
        <div v-else class="upload-placeholder">
          <div class="camera-circle">
            <ion-icon :icon="cameraOutline"></ion-icon>
          </div>
          <h3>選擇頭像</h3>
          <p>上傳您的個人照片作為頭像</p>
          <div class="photo-button" @click="takeAvatarPhoto">
            <ion-icon :icon="cameraOutline"></ion-icon>
            <span>選擇照片</span>
          </div>
        </div>
      </div>
    </ion-content>

    <ion-footer v-if="imagePreview">
      <ion-toolbar>
        <div class="footer-buttons">
          <ion-button fill="outline" @click="resetImage">
            重置
          </ion-button>
          <ion-button @click="saveImage" :disabled="isSubmitting">
            <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
            <span v-else>保存</span>
          </ion-button>
        </div>
      </ion-toolbar>
    </ion-footer>

    <ion-toast
      :is-open="!!toastMessage"
      :message="toastMessage"
      :duration="3000"
      @didDismiss="toastMessage = ''"
    ></ion-toast>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonFooter,
  IonButtons,
  IonButton,
  IonIcon,
  IonSpinner,
  IonToast,
} from '@ionic/vue';
import { cameraOutline } from 'ionicons/icons';
import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';
import { uploadImageToSupabase } from '@/lib/supabase-storage';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { utils } from '@/composables/utils';

const props = defineProps<{
  isOpen: boolean;
  currentAvatar?: string | null;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'updated', url: string): void;
}>();

const authStore = useAuthStore();
const { takePhoto } = usePhotoGallery();
const { compressImage, uniqueId } = utils();
const imagePreview = ref<string | null>(null);
const avatarPhoto = ref<Photo | null>(null);
const toastMessage = ref('');
const isSubmitting = ref(false);

// Take a new photo
const takeAvatarPhoto = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      avatarPhoto.value = photo;
      imagePreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking photo:', error);
    toastMessage.value = '無法獲取相片，請稍後再試';
  }
};

// Reset image
const resetImage = () => {
  imagePreview.value = null;
  avatarPhoto.value = null;
};

// Save the image
const saveImage = async () => {
  if (!imagePreview.value || !avatarPhoto.value) return;

  try {
    isSubmitting.value = true;

    // Compress the image first
    const compressedBlob = await compressImage(avatarPhoto.value.base64Data!, 1000, 0.8);

    // Generate unique filename
    const filename = `avatar_${uniqueId()}_${new Date().getTime()}.jpg`;

    // Upload to Supabase storage (using products bucket with avatars folder)
    const uploadedUrl = await uploadImageToSupabase({
      blob: compressedBlob,
      filename,
      mimeType: 'image/jpeg'
    }, 'products', 'avatars');

    // Update user avatar in database
    const { error } = await supabase
      .from('users')
      .update({ avatar: uploadedUrl })
      .eq('id', authStore.currentUser?.id);

    if (error) throw error;

    // Refresh user data
    await authStore.refreshUserData();

    // Emit success event
    emit('updated', uploadedUrl);
    toastMessage.value = '頭像已更新';

    // Close modal after a short delay
    setTimeout(() => {
      closeModal();
    }, 1000);
  } catch (error) {
    console.error('Error saving avatar:', error);
    toastMessage.value = '保存頭像時發生錯誤';
  } finally {
    isSubmitting.value = false;
  }
};

// Close the modal
const closeModal = () => {
  emit('close');
};

// Initialize with current avatar if available
watch(() => props.currentAvatar, (newValue) => {
  if (newValue) {
    imagePreview.value = newValue;
  } else {
    resetImage();
  }
}, { immediate: true });
</script>

<style scoped>
.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.image-preview-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Avatar Preview */
.avatar-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.avatar-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f4f5f8;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Full Image Preview */
.full-image-preview {
  width: 100%;
  max-width: 300px;
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: auto;
  display: block;
}

/* Action Buttons */
.action-buttons {
  margin-bottom: 1rem;
}

/* Upload Placeholder */
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.camera-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: var(--ion-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.camera-circle ion-icon {
  font-size: 48px;
  color: var(--ion-color-medium);
}

.upload-placeholder h3 {
  margin: 0 0 0.5rem;
  font-weight: 500;
  font-size: 1.2rem;
}

.upload-placeholder p {
  margin: 0 0 1.5rem;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.photo-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ion-color-primary);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(var(--ion-color-primary-rgb), 0.3);
  transition: background-color 0.2s, transform 0.1s;
}

.photo-button:active {
  transform: scale(0.98);
  background-color: var(--ion-color-primary-shade);
}

.photo-button ion-icon {
  margin-right: 0.5rem;
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
}
</style>
