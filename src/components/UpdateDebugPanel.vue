<template>
  <ion-card v-if="showDebugPanel">
    <ion-card-header>
      <ion-card-title>Update Debug Panel</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item>
        <ion-label>
          <h3>Current Version</h3>
          <p>{{ currentVersion }}</p>
        </ion-label>
      </ion-item>
      
      <ion-item>
        <ion-label>
          <h3>Platform</h3>
          <p>{{ platform }}</p>
        </ion-label>
      </ion-item>
      
      <ion-item>
        <ion-label>
          <h3>Update Status</h3>
          <p>{{ updateStatus }}</p>
        </ion-label>
      </ion-item>
      
      <ion-button 
        expand="block" 
        @click="checkForUpdates"
        :disabled="isChecking"
      >
        <ion-spinner v-if="isChecking" name="crescent"></ion-spinner>
        {{ isChecking ? 'Checking...' : 'Check for Updates' }}
      </ion-button>
      
      <ion-button 
        expand="block" 
        color="warning"
        @click="forceReload"
      >
        Force Reload
      </ion-button>
      
      <ion-item>
        <ion-label>
          <h3>Debug Logs</h3>
        </ion-label>
      </ion-item>
      
      <div class="debug-logs">
        <div 
          v-for="(log, index) in debugLogs" 
          :key="index"
          class="log-entry"
          :class="log.type"
        >
          <small>{{ log.timestamp }}</small><br>
          {{ log.message }}
        </div>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { 
  IonCard, 
  IonCardHeader, 
  IonCardTitle, 
  IonCardContent, 
  IonItem, 
  IonLabel, 
  IonButton,
  IonSpinner
} from '@ionic/vue';
import { Capacitor } from '@capacitor/core';
import { updateService } from '@/services/updateService';

interface DebugLog {
  timestamp: string;
  message: string;
  type: 'info' | 'error' | 'success' | 'warning';
}

const showDebugPanel = ref(false);
const currentVersion = ref('Unknown');
const platform = ref('Unknown');
const updateStatus = ref('Not checked');
const isChecking = ref(false);
const debugLogs = ref<DebugLog[]>([]);

// Show debug panel only in development or when explicitly enabled
onMounted(async () => {
  // Show in development or when URL contains debug=true
  const urlParams = new URLSearchParams(window.location.search);
  showDebugPanel.value = import.meta.env.DEV || urlParams.get('debug') === 'true';
  
  if (showDebugPanel.value) {
    await loadDebugInfo();
    addLog('Debug panel initialized', 'info');
  }
});

const addLog = (message: string, type: DebugLog['type'] = 'info') => {
  const timestamp = new Date().toLocaleTimeString();
  debugLogs.value.unshift({ timestamp, message, type });
  
  // Keep only last 10 logs
  if (debugLogs.value.length > 10) {
    debugLogs.value = debugLogs.value.slice(0, 10);
  }
};

const loadDebugInfo = async () => {
  try {
    currentVersion.value = await updateService.getCurrentVersion();
    platform.value = Capacitor.getPlatform();
    updateStatus.value = Capacitor.isNativePlatform() ? 'Ready' : 'Web (updates disabled)';
  } catch (error) {
    addLog(`Failed to load debug info: ${error}`, 'error');
  }
};

const checkForUpdates = async () => {
  if (!Capacitor.isNativePlatform()) {
    addLog('Updates only work on native platforms', 'warning');
    return;
  }
  
  isChecking.value = true;
  addLog('Checking for updates...', 'info');
  
  try {
    await updateService.checkForUpdates();
    addLog('Update check completed', 'success');
    updateStatus.value = 'Check completed';
  } catch (error) {
    addLog(`Update check failed: ${error}`, 'error');
    updateStatus.value = 'Check failed';
  } finally {
    isChecking.value = false;
  }
};

const forceReload = async () => {
  addLog('Force reloading app...', 'warning');
  try {
    await updateService.forceReload();
  } catch (error) {
    addLog(`Force reload failed: ${error}`, 'error');
  }
};
</script>

<style scoped>
.debug-logs {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

.log-entry {
  padding: 4px 0;
  border-bottom: 1px solid #ddd;
  font-size: 12px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.error {
  color: #d32f2f;
}

.log-entry.success {
  color: #388e3c;
}

.log-entry.warning {
  color: #f57c00;
}

.log-entry.info {
  color: #1976d2;
}
</style>
