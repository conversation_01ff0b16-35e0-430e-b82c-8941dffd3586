<template>
  <ion-card
    class="shop-card"
    :class="{ featured: shop.is_featured }"
    button
    :router-link="shopLink"
  >
    <div class="banner-image-container">
      <img :src="shop.banner || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8'" :alt="shop.name" class="shop-banner" />
      <div v-if="shop.is_featured" class="featured-badge">
        <ion-icon :icon="star"></ion-icon>
        精選
      </div>
      <div v-if="authStore.isAuthenticated" class="shop-actions">
        <ion-button fill="clear" @click.prevent.stop="handleToggleFavorite" class="action-button">
          <ion-icon :icon="isLiked ? heart : heartOutline" />
        </ion-button>
      </div>
    </div>
    <ion-card-header>
      <div class="shop-info">
        <img :src="shop.logo" :alt="shop.name" class="square-logo" v-if="shop.logo" />
        <div class="shop-details">
          <div class="shop-title-row">
            <ion-card-title>{{ shop.name }}</ion-card-title>
            <div v-if="getShopAge(shop.created_at) >= 1" class="shop-age-badge">
              <ion-icon :icon="ribbon" class="badge-icon"></ion-icon>
              {{ getShopAge(shop.created_at) }}年會員
            </div>
          </div>

          <div class="shop-meta">
            <div v-if="shop.shop_categories" class="shop-category">
              <ion-icon :icon="pricetag"></ion-icon>
              <span>{{ shop.shop_categories.title }}</span>
            </div>
            <div class="shop-rating" v-if="shop.rating && shop.rating > 0">
              <ion-icon :icon="star"></ion-icon>
              <span>{{ shop.rating.toFixed(1) }} ({{ shop.rating_count || 0 }})</span>
            </div>
          </div>
        </div>
      </div>

      <ion-card-subtitle v-if="shop.description" class="shop-description">
        {{ shop.description }}
      </ion-card-subtitle>

      <div class="shop-stats">
        <div class="stat-item">
          <ion-icon :icon="cube"></ion-icon>
          <span>{{ shop.product_count || 0 }} 產品</span>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <ion-icon :icon="heart"></ion-icon>
          <span>{{ shop.like_count || 0 }} 收藏</span>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <ion-icon :icon="time"></ion-icon>
          <span>{{ formatEstablishmentDate(shop.created_at) }}</span>
        </div>
      </div>
    </ion-card-header>
  </ion-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonIcon,
  IonButton,
} from '@ionic/vue';
import {
  star,
  heart,
  heartOutline,
  time,
  cube,
  pricetag,
  ribbon,
  personOutline,
} from 'ionicons/icons';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';

import { utils } from '@/composables/utils';
const { presentToast } = utils();

interface ShopCardProps {
  shop: {
    id: string;
    name: string;
    logo?: string;
    banner?: string;
    description?: string;
    is_featured?: boolean;
    rating?: number;
    rating_count?: number;
    product_count?: number;
    like_count?: number;
    created_at: string;
    shop_categories?: {
      id: string;
      title: string;
    };
  };
  routerLink?: string;
}

const props = defineProps<ShopCardProps>();

// Set default router link if not provided
const shopLink = computed(() => {
  return props.routerLink || `/shops/${props.shop.id}`;
});

const emit = defineEmits([]);

const authStore = useAuthStore();
const userStore = useUserStore();

const isLiked = computed(() => {
  return userStore.isShopLiked(props.shop.id);
});

const handleToggleFavorite = async (event: Event) => {
  event.preventDefault();
  event.stopPropagation();

  if (!authStore.isAuthenticated) {
    presentToast('請先登入以收藏商家');
    return;
  }

  const result = await userStore.toggleFavorite(props.shop.id);

  // Update like count in UI immediately
  if (result.success) {
    if (isLiked.value) {
      // Just added to favorites
      props.shop.like_count = (props.shop.like_count || 0) + 1;
    } else {
      // Just removed from favorites
      if (props.shop.like_count && props.shop.like_count > 0) {
        props.shop.like_count -= 1;
      }
    }
  }

  presentToast(result.message);
};

const getShopAge = (dateString: string) => {
  if (!dateString) return 0;
  const creationDate = new Date(dateString);
  const currentDate = new Date();
  const diffYears = currentDate.getFullYear() - creationDate.getFullYear();

  // Adjust for months and days to ensure a full year has passed
  if (
    currentDate.getMonth() < creationDate.getMonth() ||
    (currentDate.getMonth() === creationDate.getMonth() &&
     currentDate.getDate() < creationDate.getDate())
  ) {
    return diffYears - 1;
  }

  return diffYears;
};

const formatEstablishmentDate = (dateString: string) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天加入';
  } else if (diffDays < 7) {
    return `${diffDays}天前加入`;
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}週前加入`;
  } else if (diffDays < 365) {
    return `${Math.floor(diffDays / 30)}個月前加入`;
  } else {
    return `${Math.floor(diffDays / 365)}年前加入`;
  }
};
</script>

<style scoped>
.shop-card {
  margin: 0;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.shop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* Banner styling now handled by global .banner-image-container class */

.featured .banner-image-container {
  height: 200px;
}

.featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(var(--ion-color-warning-rgb), 0.9);
  color: var(--ion-color-warning-contrast);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.shop-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 0.25rem;
  opacity: 1;
  transform: translateX(0);
  z-index: 10;
}

.action-button {
  --background: transparent;
  --border-radius: 4px;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  --padding-top: 0.5rem;
  --padding-bottom: 0.5rem;
  margin: 0;
  width: 36px;
  height: 36px;
}

.action-button ion-icon {
  font-size: 1.25rem;
  color: var(--ion-color-primary);
}

.featured-badge ion-icon {
  font-size: 1.1rem;
}

ion-card-header {
  padding: 1rem;
}

.shop-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.shop-details {
  flex: 1;
  min-width: 0;
}

.shop-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.shop-age-badge {
  background: var(--ion-color-warning);
  color: var(--ion-color-warning-contrast);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.badge-icon {
  font-size: 0.8rem;
}

/* Shop logo styling now handled by global .square-logo class */

ion-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-description {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin: 0.5rem 0;
}

.shop-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.25rem;
  flex-wrap: wrap;
}

.shop-category, .shop-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  background: var(--ion-color-light);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.shop-rating {
  color: var(--ion-color-warning);
}

.shop-stats {
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
  background: var(--ion-color-light-shade);
  border-radius: 8px;
  padding: 0.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  flex: 1;
  justify-content: center;
}

.stat-divider {
  width: 1px;
  height: 16px;
  background-color: var(--ion-color-medium);
  opacity: 0.3;
}

.stat-item ion-icon {
  font-size: 1rem;
  color: var(--ion-color-primary);
}

@media (max-width: 768px) {
  .banner-container {
    height: 140px;
  }

  .featured .banner-container {
    height: 160px;
  }

  .shop-logo {
    width: 32px;
    height: 32px;
  }

  ion-card-title {
    font-size: 1rem;
  }

  .shop-meta {
    gap: 0.5rem;
  }

  .shop-stats {
    padding: 0.4rem;
  }

  .stat-item {
    font-size: 0.7rem;
  }

  .shop-category, .shop-rating {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }

  .shop-age-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .shop-description {
    font-size: 0.8rem;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }
}
</style>
