<template>
  <div class="description-section">
    <h2 class="section-subtitle">{{ sectionTitle }}</h2>
    <p class="description" :class="{ 'truncated': !showFullDescription && description && description.length > truncateLength }">
      {{ description || '暫無介紹' }}
    </p>
    <ion-button
      v-if="description && description.length > truncateLength"
      fill="clear"
      size="small"
      class="view-more-btn"
      @click="showFullDescription = !showFullDescription"
    >
      {{ showFullDescription ? '收起' : '查看更多' }}
    </ion-button>
  </div>
</template>
  
<script setup lang="ts">
import { ref, computed } from 'vue';
import {
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonIcon,
    IonButton,
    IonToast,
    alertController,
    loadingController,
} from '@ionic/vue';
import {
    peopleOutline,
    personOutline,
    timeOutline,
    personAddOutline,
    star,
} from 'ionicons/icons';

const showFullDescription = ref(false);

const props = defineProps({
  sectionTitle: {
    type: String,
    required: false
  },
  description: {
    type: String,
    required: true
  },
  truncateLength: {
    type: Number,
    required: false,
    default: 150,
  }
});

</script>
  
<style scoped>
.description-section {
  padding-top: 1rem;
  border-top: 1px solid var(--ion-color-light-shade);
  white-space: pre-line;
}
.section-subtitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.75rem;
  color: var(--ion-color-dark);
}
.description {
  color: var(--ion-color-medium);
  font-size: 1rem;
  margin: 0;
  line-height: 1.6;
}
.description.truncated {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.view-more-btn {
  --color: var(--ion-color-primary);
  --padding-start: 0;
  --padding-end: 0;
  height: 30px;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}
</style>
  