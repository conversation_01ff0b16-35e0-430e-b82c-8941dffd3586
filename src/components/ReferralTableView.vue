<template>
  <div class="table-container">
    <ion-card>
      <ion-card-content>
        <table class="referral-table">
          <thead>
            <tr>
              <!--<th>用戶名稱</th>-->
              <th>姓名</th>
              <th>推薦層級</th>
              <th>會員類型</th>
              <th>註冊日期</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(user, index) in referrals" :key="index">
              <!--<td>{{ user.username }}</td>-->
              <td>{{ user.full_name }}</td>
              <td>{{ calculateLevel(user.referrer_id) }}</td>
              <td>
                <ion-badge :color="getRoleBadgeColor(user.role)">
                  {{ getRoleLabel(user.role) }}
                </ion-badge>
              </td>
              <td>{{ formatDate(user.created_at) }}</td>
            </tr>
          </tbody>
        </table>

        <div v-if="referrals.length === 0" class="no-data">
          <p>沒有符合條件的推薦記錄</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { IonCard, IonCardContent, IonBadge } from '@ionic/vue';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import type { UserRole } from '@/types';
import { format } from 'date-fns';
import { utils } from '@/composables/utils';

const props = defineProps<{
  referrals: any[];
}>();

const authStore = useAuthStore();
const userStore = useUserStore();
const currentUser = computed(() => userStore.currentUser);
const { getRoleLabel } = utils();

// Format date
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  try {
    return format(new Date(dateString), 'yyyy/MM/dd');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

// Get role badge color
const getRoleBadgeColor = (role?: UserRole) => {
  switch (role) {
    case 'free':
      return 'primary';
    case 'merchant':
      return 'success';
    case 'president':
      return 'tertiary';
    default:
      return 'medium';
  }
};

// Calculate the level of a referral
const calculateLevel = (referrerId: string) => {
  if (!referrerId || !currentUser.value) return '-';

  // Level 1: Direct referral (referrer is current user)
  if (referrerId === currentUser.value.id) {
    return '1 (直接)';
  }

  // Level 1+: Find the chain of referrers
  let level = 1; // start from 1 (bug fixes)
  let currentReferrerId = referrerId;

  // Create a map of referrers for quick lookup
  const referrerMap = new Map();
  props.referrals.forEach(user => {
    referrerMap.set(user.id, user.referrer_id);
  });

  // Follow the chain up to 10 levels
  while (level <= 10) {
    // If we've reached a user who was directly referred by the current user
    if (currentReferrerId === currentUser.value.id) {
      return level.toString();
    }

    // Move up to the next referrer in the chain
    currentReferrerId = referrerMap.get(currentReferrerId);

    // If we can't find the next referrer, or we've reached the top
    if (!currentReferrerId) {
      return '-';
    }

    level++;
  }

  return '10+';
};
</script>

<style scoped>
.table-container {
  width: 100%;
  overflow-x: auto;
}

.referral-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.referral-table th,
.referral-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--ion-color-light);
}

.referral-table th {
  font-weight: 600;
  color: var(--ion-color-medium);
  background-color: var(--ion-color-light-shade);
}

.referral-table tr:hover {
  background-color: var(--ion-color-light-tint);
}

.no-data {
  text-align: center;
  padding: 2rem;
  color: var(--ion-color-medium);
}

@media (max-width: 768px) {
  .referral-table {
    font-size: 0.8rem;
  }

  .referral-table th,
  .referral-table td {
    padding: 0.5rem;
  }
}
</style>
