<template>
  <ion-card class="product-reference-card" @click="navigateToProduct">
    <div class="product-content">
      <div class="product-image">
        <img
          :src="product.cover_image || 'https://placehold.co/400x300/f8f9fa/6c757d/png?text=Product+Image'"
          :alt="product.title"
          @error="handleImageError"
        />
      </div>
      <div class="product-info">
        <h4 class="product-title">{{ product.title }}</h4>
        <p class="product-price" v-if="product.price > 0">
          HK${{ product.price.toFixed(2) }}
        </p>
        <p class="product-price free" v-else>
          免費
        </p>
        <p class="product-seller" v-if="product.shop_name">
          {{ product.shop_name }}
        </p>
      </div>
      <div class="product-actions">
        <ion-icon :icon="chevronForwardOutline" color="medium"></ion-icon>
      </div>
    </div>
  </ion-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonCard,
  IonIcon,
} from '@ionic/vue';
import { chevronForwardOutline } from 'ionicons/icons';

interface Product {
  id: string;
  title: string;
  price: number;
  cover_image?: string;
  shop_id: string;
  shop_name?: string;
}

const props = defineProps<{
  product: Product;
}>();

const emit = defineEmits<{
  productClick: [product: Product];
}>();

const router = useRouter();

const navigateToProduct = () => {
  emit('productClick', props.product);
  router.push(`/products/${props.product.id}`);
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = 'https://placehold.co/400x300/f8f9fa/6c757d/png?text=No+Image';
};
</script>

<style scoped>
.product-reference-card {
  margin: 8px 0;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.product-reference-card:hover {
  transform: translateY(-2px);
}

.product-content {
  display: flex;
  align-items: center;
  padding: 12px;
  gap: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--ion-color-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 700;
  color: var(--ion-color-primary);
}

.product-price.free {
  color: var(--ion-color-success);
}

.product-seller {
  margin: 0;
  font-size: 12px;
  color: var(--ion-color-medium);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-actions {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .product-content {
    padding: 10px;
    gap: 10px;
  }

  .product-image {
    width: 50px;
    height: 50px;
  }

  .product-title {
    font-size: 13px;
  }

  .product-price {
    font-size: 14px;
  }

  .product-seller {
    font-size: 11px;
  }
}
</style>
