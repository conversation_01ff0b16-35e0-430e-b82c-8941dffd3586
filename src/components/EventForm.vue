<template>
  <form @submit.prevent="handleSubmit">
    <ion-list>
      <ion-item>
        <ion-label position="stacked">活動標題</ion-label>
        <ion-input
          v-model="eventData.title"
          type="text"
          required
          placeholder="請輸入活動標題"
        ></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="stacked">開始日期及時間</ion-label>
        <ion-datetime-button style="padding-top: 8px" :datetime="'start-datetime-' + formId"></ion-datetime-button>
        <ion-modal :keep-contents-mounted="true">
          <ion-datetime
            :id="'start-datetime-' + formId"
            v-model="eventData.start_datetime"
            :min="minStartDate"
            presentation="date-time"
            locale="zh-HK"
            @ionChange="handleStartDateChange"
          ></ion-datetime>
        </ion-modal>
        <ion-note v-if="dateTimeError.start" color="danger">{{ dateTimeError.start }}</ion-note>
      </ion-item>

      <ion-item>
        <ion-label position="stacked">結束日期及時間</ion-label>
        <ion-datetime-button style="padding-top: 8px" :datetime="'end-datetime-' + formId"></ion-datetime-button>
        <ion-modal :keep-contents-mounted="true">
          <ion-datetime
            :id="'end-datetime-' + formId"
            v-model="eventData.end_datetime"
            :min="eventData.start_datetime"
            presentation="date-time"
            locale="zh-HK"
            @ionChange="validateDateTimes"
          ></ion-datetime>
        </ion-modal>
        <ion-note v-if="dateTimeError.end" color="danger">{{ dateTimeError.end }}</ion-note>
      </ion-item>

      <ion-item>
        <ion-label position="stacked">地址</ion-label>
        <ion-input
          v-model="eventData.address"
          type="text"
          required
          placeholder="請輸入活動地址"
        ></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="stacked">活動描述</ion-label>
        <ion-textarea
          v-model="eventData.description"
          required
          placeholder="請輸入活動描述"
          :rows="4"
        ></ion-textarea>
      </ion-item>

      <!-- Banner Photo Upload (only for create mode) -->
      <ion-item v-if="mode === 'create'">
        <ion-label position="stacked">橫幅圖片</ion-label>
        <div class="image-upload-container">
          <div class="preview-container" v-if="bannerPreview || eventData.banner_photo">
            <img :src="bannerPreview || eventData.banner_photo" alt="Banner preview" class="banner-preview" />
            <ion-button fill="clear" color="danger" @click="$emit('remove-banner')">
              <ion-icon :icon="trashOutline"></ion-icon>
            </ion-button>
          </div>
          <ion-button v-else expand="block" @click="$emit('take-banner')" fill="outline">
            <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
            選擇橫幅
          </ion-button>
        </div>
      </ion-item>

      <!-- Event Photos Section (only for create mode) -->
      <div v-if="mode === 'create'" class="photos-section">
        <ion-item>
          <ion-label position="stacked">活動相片</ion-label>
        </ion-item>

        <div class="ion-padding-horizontal">
          <ion-button
            expand="block"
            fill="outline"
            @click="$emit('add-photo')"
            class="add-photo-button"
          >
            <ion-icon :icon="cameraOutline" slot="start"></ion-icon>
            新增相片
          </ion-button>

          <ion-list v-if="eventPhotos && eventPhotos.length > 0" class="photo-list">
            <ion-reorder-group @ionItemReorder="handlePhotoReorder($event)" :disabled="false">
              <ion-item v-for="(photo, index) in eventPhotos" :key="index" class="photo-item">
                <ion-thumbnail slot="start">
                  <img :src="photo.preview" :alt="`Event photo ${index + 1}`" class="square-thumbnail" />
                </ion-thumbnail>
                <ion-label>
                  <ion-input
                    v-model="photo.caption"
                    placeholder="輸入相片說明"
                    class="caption-input"
                    @ionInput="updatePhotoCaption(index, $event)"
                  ></ion-input>
                </ion-label>
                <ion-button
                  fill="clear"
                  color="danger"
                  slot="end"
                  @click="$emit('remove-photo', index)"
                >
                  <ion-icon :icon="trashOutline"></ion-icon>
                </ion-button>
                <ion-reorder slot="end">
                  <ion-icon :icon="menuOutline"></ion-icon>
                </ion-reorder>
              </ion-item>
            </ion-reorder-group>
          </ion-list>
        </div>
      </div>

      <ion-item>
        <ion-label position="stacked">人數上限</ion-label>
        <ion-input
          v-model="eventData.max_participants"
          type="number"
          min="1"
          placeholder="可選填"
        ></ion-input>
      </ion-item>
    </ion-list>

    <div class="ion-padding">
      <ion-button type="submit" expand="block" :disabled="isSubmitting">
        {{ submitButtonText }}
      </ion-button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonButton,
  IonIcon,
  IonDatetime,
  IonDatetimeButton,
  IonNote,
  IonModal,
  IonThumbnail,
  IonReorderGroup,
  IonReorder,
} from '@ionic/vue';
import { trashOutline, cameraOutline, menuOutline } from 'ionicons/icons';

// Simple date formatting utility
const formatDate = (date: Date, format: string) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

const props = defineProps({
  initialData: {
    type: Object,
    required: true
  },
  mode: {
    type: String,
    default: 'create', // 'create' or 'edit'
    validator: (value: string) => ['create', 'edit'].includes(value)
  },
  isSubmitting: {
    type: Boolean,
    default: false
  },
  bannerPreview: {
    type: String,
    default: null
  },
  minStartDate: {
    type: String,
    default: ''
  },
  eventPhotos: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:initialData', 'submit', 'take-banner', 'remove-banner', 'add-photo', 'remove-photo', 'reorder-photos']);

// Generate a unique ID for this form instance to avoid conflicts with multiple forms
const formId = Math.random().toString(36).substring(2, 9);

// Create a local copy of the event data
const eventData = ref({ ...props.initialData });

// Track validation errors
const dateTimeError = ref({
  start: '',
  end: ''
});

// Track the duration between start and end times in milliseconds
const eventDuration = ref(0);

// Update local data when props change
watch(() => props.initialData, (newValue) => {
  eventData.value = { ...newValue };
  calculateEventDuration();
}, { deep: true });

// Calculate and store the duration between start and end times
const calculateEventDuration = () => {
  if (eventData.value.start_datetime && eventData.value.end_datetime) {
    const startDate = new Date(eventData.value.start_datetime);
    const endDate = new Date(eventData.value.end_datetime);
    eventDuration.value = endDate.getTime() - startDate.getTime();
  }
};

// Handle start date change - update end date to maintain the same duration
const handleStartDateChange = () => {
  // Clear any previous errors
  dateTimeError.value.start = '';

  if (eventData.value.start_datetime && eventData.value.end_datetime) {
    const startDate = new Date(eventData.value.start_datetime);

    // If this is the first change, calculate the initial duration
    if (eventDuration.value === 0) {
      calculateEventDuration();
    }

    // Update end date to maintain the same duration
    const newEndDate = new Date(startDate.getTime() + eventDuration.value);
    eventData.value.end_datetime = formatDate(newEndDate, 'YYYY-MM-DDTHH:mm:ss');

    // Validate the dates
    validateDateTimes();

    // Emit the updated data
    emit('update:initialData', eventData.value);
  }
};

// Validate that end date is after start date
const validateDateTimes = () => {
  dateTimeError.value.start = '';
  dateTimeError.value.end = '';

  if (eventData.value.start_datetime && eventData.value.end_datetime) {
    const startDate = new Date(eventData.value.start_datetime);
    const endDate = new Date(eventData.value.end_datetime);

    if (endDate <= startDate) {
      dateTimeError.value.end = '結束時間必須在開始時間之後';
      return false;
    }

    // Update the duration when end date is manually changed
    eventDuration.value = endDate.getTime() - startDate.getTime();

    // Emit the updated data
    emit('update:initialData', eventData.value);
  }

  return true;
};

// Compute the submit button text based on mode and submission state
const submitButtonText = computed(() => {
  if (props.isSubmitting) {
    return props.mode === 'create' ? '建立中...' : '更新中...';
  }
  return props.mode === 'create' ? '建立活動' : '更新活動';
});

// Handle form submission
const handleSubmit = () => {
  // Validate dates before submitting
  if (!validateDateTimes()) {
    return; // Stop if validation fails
  }

  emit('submit', eventData.value);
};

// Handle photo reordering
const handlePhotoReorder = (event: any) => {
  emit('reorder-photos', event);
};

// Update photo caption
const updatePhotoCaption = (_index: number, _event: any) => {
  // This will be handled by the parent component
  // The v-model will automatically update the photo object
};

// Calculate initial duration on mount
onMounted(() => {
  calculateEventDuration();
});
</script>

<style scoped>
/* Image upload styles */
.image-upload-container {
  width: 100%;
  padding: 1rem 0;
}

.preview-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.preview-container ion-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
}

.banner-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
}

/* Photo section styles */
.photos-section {
  margin-top: 1rem;
}

.add-photo-button {
  margin-bottom: 1rem;
}

.photo-list {
  margin-top: 1rem;
}

.photo-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
}

.square-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.caption-input {
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
}
</style>
