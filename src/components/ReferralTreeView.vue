<template>
  <div class="tree-container">
    <div id="referralTreeChart" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as am5 from '@amcharts/amcharts5';
import * as am5hierarchy from '@amcharts/amcharts5/hierarchy';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { alertController } from '@ionic/vue';
import { utils } from '@/composables/utils';

const props = defineProps<{
  referrals: any[];
  currentUser: any;
}>();

const { getRoleLabel, formatDate, } = utils();

let root: am5.Root | null = null;
let chart: any = null;

// Show user details modal
const showUserDetails = async (userData: any) => {
  console.log('User details:', userData);


  /*<div class="detail-item">
    <div class="detail-label">用戶名</div>
    <div class="detail-value">${userData.username || '未提供'}</div>
  </div>*/

  // Create alert controller for user details
  const alert = await alertController.create({
    cssClass: 'user-details-alert',
    header: '用戶詳情',
    message: `
      <div class="user-details-content">
        <div class="detail-item">
          <div class="detail-label">姓名</div>
          <div class="detail-value">${userData.name || '未提供'}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">角色</div>
          <div class="detail-value">${userData.roleLabel || getRoleLabel(userData.role) || '未提供'}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">加入日期</div>
          <div class="detail-value">${userData.joinDate || '未提供'}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">層級</div>
          <div class="detail-value">第 ${userData.level || 0} 層</div>
        </div>
      </div>
    `,
    buttons: [
      {
        text: '關閉',
        role: 'cancel'
      }
    ]
  });

  // Present the alert
  await alert.present();
};

// Process data for the tree chart
const processTreeData = () => {
  if (!props.currentUser) return null;

  // Create the root node (current user)
  const rootNode = {
    name: props.currentUser.full_name,
    username: props.currentUser.username,
    role: props.currentUser.role,
    roleLabel: getRoleLabel(props.currentUser.role),
    joinDate: formatDate(props.currentUser.created_at),
    value: 100,
    level: 0,
    children: [] as any[]
  };

  // Create a map of users by ID for quick lookup
  const userMap = new Map();

  // Add all referrals to the map
  props.referrals.forEach(user => {
    userMap.set(user.id, {
      id: user.id,
      name: user.full_name,
      username: user.username,
      role: user.role,
      roleLabel: getRoleLabel(user.role),
      joinDate: formatDate(user.created_at),
      value: 50,
      referrer_id: user.referrer_id,
      level: 0, // Will be calculated later
      children: [] as any[]
    });
  });

  // Build the tree structure
  props.referrals.forEach(user => {
    const userNode = userMap.get(user.id);

    if (user.referrer_id === props.currentUser.id) {
      // Direct referral of the current user
      userNode.level = 1;
      rootNode.children.push(userNode);
    } else if (userMap.has(user.referrer_id)) {
      // Referral of another user in our list
      const parentNode = userMap.get(user.referrer_id);
      userNode.level = (parentNode.level || 0) + 1;
      parentNode.children.push(userNode);
    }
  });

  return rootNode;
};

// Initialize the chart
const initializeChart = () => {
  // Dispose of previous chart if it exists
  if (root) {
    root.dispose();
  }

  // Create root element
  root = am5.Root.new("referralTreeChart");

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Zoom in / out
  const zoomableContainer = root.container.children.push(
    am5.ZoomableContainer.new(root, {
      width: am5.p100,
      height: am5.p100,
      wheelable: true,
      pinchZoom: true,
    })
  );
  zoomableContainer.children.push(am5.ZoomTools.new(root, {
    target: zoomableContainer
  }));

  // Create series
  const series = zoomableContainer.contents.children.push(am5hierarchy.Tree.new(root, {
    downDepth: 1,
    initialDepth: 10,
    topDepth: 0,
    valueField: "value",
    categoryField: "name",
    childDataField: "children",
    orientation: "vertical",
    paddingTop: 100,
    paddingBottom: 100,
    draggable: false,
  }));

  chart = series;

  // Configure nodes
  series.nodes.template.setAll({
    draggable: false,
    toggleKey: "none",
    cursorOverStyle: "pointer",
    tooltipText: "{name} ({username})\n角色: {roleLabel}\n{joinDate} 加入"
  });

  // Make circles larger to fit more text
  series.circles.template.setAll({
    radius: 38,  // Increased radius for more space
    strokeWidth: 2,
    stroke: am5.color(0xFFFFFF),
  });
  series.outerCircles.template.setAll({
    radius: 38,
  });

  // Set different colors based on level
  series.circles.template.adapters.add("fill", function(fill, target) {
    const dataItem = target.dataItem;
    if (dataItem && dataItem.dataContext) {
      // Get the level of the node (safely access with type assertion)
      const userData = dataItem.dataContext as any;
      const level = userData.level || 0;

      // Colors for different levels
      const levelColors = [
        0x6B4593, // Level 0 (Root) - Purple
        0x4CAF50, // Level 1 - Green
        0x2196F3, // Level 2 - Blue
        0xFF9800, // Level 3 - Orange
        0xF44336  // Level 4+ - Red
      ];

      // Use the appropriate color based on level
      const colorIndex = Math.min(level, levelColors.length - 1);
      return am5.color(levelColors[colorIndex]);
    }
    return fill;
  });

  // Modify labels to show more information with better formatting
  series.labels.template.setAll({
    text: "[bold]{name}[/]\n{joinDate}\n加入",
    fill: am5.color(0xFFFFFF),
    fontSize: 11,
    fontWeight: "400",
    lineHeight: 1.2,
    textAlign: "center",
    oversizedBehavior: "truncate",
    maxWidth: 120,
    paddingTop: 10,
    paddingBottom: 10,
  });

  // Add click event to nodes
  series.nodes.template.events.on("click", function(ev) {
    const dataItem = ev.target.dataItem;
    if (dataItem && dataItem.dataContext) {
      const userData = dataItem.dataContext;
      showUserDetails(userData);
    }
  });

  // Configure links with fixed distance
  series.links.template.setAll({
    strokeWidth: 2,
    strokeOpacity: 0.7,
    distance: 150, // Increased distance between nodes
  });

  // Process and set data
  const treeData = processTreeData();
  if (treeData) series.data.setAll([treeData]);

  // Adjust layout to prevent nodes from being too sparse
  series.events.on("datavalidated", function() {
    // Trigger layout recalculation
    series.nodes.each(function() {
      // This will trigger a layout recalculation
    });
  });

  series.appear(100, 100);
};

// Update chart when referrals change
watch(() => props.referrals, () => {
  if (chart) {
    const treeData = processTreeData();
    if (treeData) {
      chart.data.setAll([treeData]);

      // Trigger layout recalculation after data update
      setTimeout(() => {
        if (chart && chart.nodes) {
          chart.nodes.each(function() {
            // This will trigger a layout recalculation
          });
        }
      }, 100);
    }
  }
}, { deep: true });

onMounted(() => {
  // Initialize chart after a short delay to ensure the container is ready
  setTimeout(() => {
    initializeChart();
  }, 100);
});

onUnmounted(() => {
  // Clean up
  if (root) {
    root.dispose();
  }
});
</script>

<style scoped>
.tree-container {
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-container {
  width: 100%;
  height: 700px;
}

@media (max-width: 768px) {
  .chart-container {
    height: 500px;
  }
}

/* Alert styles */
:global(.user-details-alert) {
  --width: 90%;
  --max-width: 400px;
  --border-radius: 16px;
}

:global(.user-details-alert .alert-message) {
  max-height: 300px;
  overflow-y: auto;
}

:global(.user-details-content) {
  padding: 10px 0;
}

:global(.user-details-content .detail-item) {
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 10px;
}

:global(.user-details-content .detail-label) {
  font-size: 14px;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

:global(.user-details-content .detail-value) {
  font-size: 16px;
  font-weight: 500;
  color: var(--ion-color-dark);
}
</style>
