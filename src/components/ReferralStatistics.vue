<template>
  <ion-card class="statistics-card">
    <ion-card-content>
      <div class="statistics-grid">
        <div class="statistic-item">
          <div class="statistic-value">{{ statistics.directReferrals }}</div>
          <div class="statistic-label">直接推薦</div>
        </div>
        <div class="statistic-item">
          <div class="statistic-value">{{ statistics.totalReferrals }}</div>
          <div class="statistic-label">總推薦人數</div>
        </div>
        <div class="statistic-item">
          <div class="statistic-value">{{ statistics.levels }}</div>
          <div class="statistic-label">推薦層級</div>
        </div>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import { IonCard, IonCardContent } from '@ionic/vue';

defineProps<{
  statistics: {
    directReferrals: number;
    totalReferrals: number;
    levels: number;
  };
}>();
</script>

<style scoped>
.statistics-card {
  margin-bottom: 1rem;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.statistic-item {
  text-align: center;
}

.statistic-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--ion-color-primary);
  margin-bottom: 0.25rem;
}

.statistic-label {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

@media (max-width: 768px) {
  .statistic-value {
    font-size: 1.5rem;
  }
  
  .statistic-label {
    font-size: 0.8rem;
  }
}
</style>
