<template>
  <ion-modal :is-open="isOpen" @didDismiss="closeModal">
    <ion-header>
      <ion-toolbar>
        <ion-title>意見反饋</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeModal">取消</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div class="ion-padding">
        <form @submit.prevent="submitFeedback">
          <ion-list>
            <!-- Feedback Category -->
            <ion-item>
              <ion-label position="stacked">反饋類型 *</ion-label>
              <ion-select 
                v-model="feedbackData.category" 
                placeholder="請選擇反饋類型"
                interface="popover"
              >
                <ion-select-option value="bug">錯誤回報</ion-select-option>
                <ion-select-option value="feature">功能建議</ion-select-option>
                <ion-select-option value="improvement">改進建議</ion-select-option>
                <ion-select-option value="ui">介面問題</ion-select-option>
                <ion-select-option value="performance">效能問題</ion-select-option>
                <ion-select-option value="other">其他</ion-select-option>
              </ion-select>
            </ion-item>

            <!-- Subject -->
            <ion-item>
              <ion-label position="stacked">主題 *</ion-label>
              <ion-input
                v-model="feedbackData.subject"
                placeholder="簡短描述您的反饋"
                :maxlength="100"
                counter="true"
              ></ion-input>
            </ion-item>

            <!-- Description -->
            <ion-item>
              <ion-label position="stacked">詳細描述 *</ion-label>
              <ion-textarea
                v-model="feedbackData.description"
                placeholder="請詳細描述您的意見或遇到的問題..."
                :rows="6"
                :maxlength="1000"
                counter="true"
              ></ion-textarea>
            </ion-item>

            <!-- Contact Email (optional) -->
            <ion-item>
              <ion-label position="stacked">聯絡電郵 (可選)</ion-label>
              <ion-input
                v-model="feedbackData.contact_email"
                type="email"
                placeholder="如需回覆請提供電郵地址"
              ></ion-input>
            </ion-item>

            <!-- Anonymous Option -->
            <ion-item>
              <ion-checkbox v-model="feedbackData.is_anonymous" slot="start"></ion-checkbox>
              <ion-label>匿名提交</ion-label>
            </ion-item>

            <!-- Error Messages -->
            <div v-if="errorMessage" class="error-message ion-padding">
              <ion-text color="danger">
                <p>{{ errorMessage }}</p>
              </ion-text>
            </div>
          </ion-list>

          <!-- Submit Button -->
          <div class="ion-padding">
            <ion-button 
              expand="block" 
              type="submit" 
              :disabled="!isFormValid || isSubmitting"
            >
              <ion-spinner v-if="isSubmitting" name="crescent" slot="start"></ion-spinner>
              {{ isSubmitting ? '提交中...' : '提交反饋' }}
            </ion-button>
          </div>
        </form>
      </div>
    </ion-content>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonList,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonSelect,
  IonSelectOption,
  IonCheckbox,
  IonText,
  IonSpinner,
} from '@ionic/vue';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';

const props = defineProps<{
  isOpen: boolean;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submitted'): void;
}>();

const authStore = useAuthStore();

// Form data
const feedbackData = ref({
  category: '',
  subject: '',
  description: '',
  contact_email: '',
  is_anonymous: false,
});

const isSubmitting = ref(false);
const errorMessage = ref('');

// Form validation
const isFormValid = computed(() => {
  return feedbackData.value.category && 
         feedbackData.value.subject.trim() && 
         feedbackData.value.description.trim();
});

// Reset form when modal opens
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    resetForm();
  }
});

const resetForm = () => {
  feedbackData.value = {
    category: '',
    subject: '',
    description: '',
    contact_email: '',
    is_anonymous: false,
  };
  errorMessage.value = '';
  isSubmitting.value = false;
};

const closeModal = () => {
  emit('close');
};

const submitFeedback = async () => {
  if (!isFormValid.value || isSubmitting.value) return;

  try {
    isSubmitting.value = true;
    errorMessage.value = '';

    // Prepare feedback data
    const feedbackPayload = {
      category: feedbackData.value.category,
      subject: feedbackData.value.subject,
      description: feedbackData.value.description,
      contact_email: feedbackData.value.contact_email || null,
      is_anonymous: feedbackData.value.is_anonymous,
      user_id: feedbackData.value.is_anonymous ? null : authStore.currentUser?.id,
      status: 'pending',
      created_at: new Date().toISOString(),
    };

    // Submit to database
    const { error } = await supabase
      .from('user_feedback')
      .insert([feedbackPayload]);

    if (error) {
      throw error;
    }

    // Success
    emit('submitted');
    closeModal();
  } catch (error) {
    console.error('Error submitting feedback:', error);
    errorMessage.value = '提交反饋時發生錯誤，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.error-message {
  margin-top: 1rem;
}

ion-textarea {
  --padding-top: 12px;
  --padding-bottom: 12px;
}

ion-select {
  --padding-top: 12px;
  --padding-bottom: 12px;
}

ion-input {
  --padding-top: 12px;
  --padding-bottom: 12px;
}
</style>
