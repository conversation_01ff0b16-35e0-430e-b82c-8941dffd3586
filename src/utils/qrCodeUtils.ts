/**
 * Utility functions for QR code handling
 */

/**
 * Extracts a short check-in code from a QR code string
 * 
 * The QR code format is: app_id|event_id|timestamp
 * We extract a 6-character code from the application ID
 * 
 * @param qrCode The full QR code string
 * @returns A 6-character check-in code
 */
export function extractCheckInCode(qrCode: string): string {
  if (!qrCode) return '';
  
  try {
    // Split the QR code to get the application ID
    const parts = qrCode.split('|');
    if (parts.length < 1) return '';
    
    const appId = parts[0];
    
    // Extract 6 characters from the UUID (removing hyphens)
    const cleanId = appId.replace(/-/g, '');
    
    // Take the first 6 characters and convert to uppercase
    return cleanId.substring(0, 6).toUpperCase();
  } catch (error) {
    console.error('Error extracting check-in code:', error);
    return '';
  }
}

/**
 * Formats a QR code for display
 * 
 * @param qrCode The full QR code string
 * @returns A formatted string with spaces for readability
 */
export function formatCheckInCode(code: string): string {
  if (!code || code.length !== 6) return code;
  
  // Format as XXX XXX for better readability
  return `${code.substring(0, 3)} ${code.substring(3, 6)}`;
}
