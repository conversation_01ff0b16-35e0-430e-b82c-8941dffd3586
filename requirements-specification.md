# 搵料 (Waste to Gold) - Requirements Specification

## 1. Introduction

### Purpose
搵料 (Waste to Gold) is a community-driven marketplace mobile application designed to facilitate the exchange of construction materials and second-hand goods. The platform enables users to give away, sell, and request materials, promoting sustainability and resource optimization within the construction industry and broader community.

### Technology Stack
- **Frontend**: Ionic Vue 3 with Composition API
- **State Management**: Pinia
- **Backend**: Supabase (Authentication, Database, Real-time features)
- **Mobile Platform**: Capacitor for iOS and Android deployment
- **UI Framework**: Ionic Components with custom green theme (primary color: rgb(7, 123, 71))

## 2. Core Concepts

### Items (Materials/Products)
Physical goods being exchanged on the platform, including:
- Construction materials (primary focus)
- Tools and equipment
- General second-hand items
- Can be offered for free or for sale
- Include detailed specifications, condition, and location information

### User Roles
- **Givers**: Users who list items to give away or sell
- **Takers**: Users who browse and request items
- **Requesters**: Users who post "wanted" ads for specific items
- Users can assume multiple roles simultaneously

### "吹雞" (Chui <PERSON> / Broadcast Request)
A feature allowing users to post "wanted" advertisements for specific materials or items they need. The term "吹雞" is Cantonese slang meaning "to call for gathering" or "broadcast a request."

### Direct Messaging
Private chat system enabling direct communication between users to:
- Negotiate terms and pricing
- Arrange pickup/delivery details
- Share additional item information
- Build community relationships

### User Types & Roles
Based on the existing codebase, the app supports:
- **Free Users**: Basic marketplace access
- **Merchants**: Enhanced features for business users
- **Presidents**: Administrative privileges and advanced features

## 3. User Flows & Page Specifications

### Flow 1: User Onboarding (Registration & Login)

#### User Journey
1. User opens app and sees login screen
2. New users tap "Register" to create account
3. Complete registration form with required information
4. System creates account and automatically logs in user
5. Existing users can log in with username/email and password

#### Pages Involved

##### Login Page (`/login`)
**Content**: Brand logo, welcome message, login form
**Form Fields**:
- Username/Email (Input Text) - accepts both username and email
- Password (Input Password)
**Actions**:
- Login Button (primary action)
- "Register" Link (navigation to registration)
- "Forgot Password" Link (password recovery)

##### Registration Page (`/register`)
**Content**: Brand logo, registration form with validation
**Form Fields**:
- Username (Input Text) - unique, alphanumeric, 3-20 characters
- Full Name (Input Text) - required
- Email (Input Email) - unique, validated format
- Phone Number (Input Tel) - with country code selector
- Password (Input Password) - minimum 6 characters
- Confirm Password (Input Password) - must match password
- Role (Select Dropdown) - Free/Merchant/President
- Industry (Select Dropdown) - construction-related industries
- Company Name (Input Text) - optional, shown for Merchant/President roles
- Referral Code (Input Text) - optional
**Actions**:
- Register Button (primary action)
- "Back to Login" Link
- Terms & Conditions checkbox (required)

### Flow 2: Listing an Item (The "Giving" Flow)

#### User Journey
1. User taps "Upload" tab or "+" button
2. Takes photos or selects from gallery
3. Fills in item details and specifications
4. Sets pricing (free or paid)
5. Confirms location and pickup details
6. Publishes listing to marketplace

#### Pages Involved

##### Upload Item Page (`/upload-item`)
**Content**: Photo upload area, item details form
**Form Fields**:
- Photos (Camera/Gallery Picker) - multiple images, required
- Title (Input Text) - descriptive item name
- Description (Text Area) - detailed item description
- Category (Select Dropdown) - material/product categories
- Condition (Radio Buttons) - New/Like New/Good/Fair/Poor
- Method (Radio Buttons) - "Give for Free" vs "Sell"
- Price (Input Number) - conditional, shown only if "Sell" selected
- Pickup Location (Location Picker) - address or district selection
- Contact Method (Radio Buttons) - Chat/Phone/Both
- Availability (Date Picker) - when item is available
**Actions**:
- "Add Photos" Button
- "Publish Listing" Button (primary action)
- "Save as Draft" Button
- "Cancel" Button

### Flow 3: Broadcasting a Request (The "Chui Gai" Flow)

#### User Journey
1. User navigates to "Chui Gai" tab
2. Taps "Create Request" button
3. Describes needed item/material
4. Sets budget and location preferences
5. Publishes request for community to see
6. Receives responses from potential suppliers

#### Pages Involved

##### Create Request Page (`/create-request`)
**Content**: Request form for posting "wanted" ads
**Form Fields**:
- Title (Input Text) - what user is looking for
- Description (Text Area) - detailed requirements and specifications
- Category (Select Dropdown) - material/product categories
- Budget (Input Number) - optional maximum budget
- Preferred Condition (Checkbox Group) - acceptable conditions
- Pickup Location (Location Picker) - preferred pickup area
- Urgency (Radio Buttons) - Low/Medium/High/Urgent
- Contact Method (Radio Buttons) - Chat/Phone/Both
- Deadline (Date Picker) - when item is needed by
**Actions**:
- "Post Request" Button (primary action)
- "Save as Draft" Button
- "Cancel" Button

### Flow 4: Acquiring an Item (The "Taking" Flow)

#### User Journey
1. User browses items on Home feed
2. Uses search and filters to find specific items
3. Taps on item to view details
4. Reviews item information and seller details
5. Initiates chat with seller to negotiate
6. Arranges pickup/delivery through messaging

#### Pages Involved

##### Home/Feed Page (`/`)
**Content**: Grid/list layout of available items with search and filtering
**Components**:
- Search Bar (Input Text) - keyword search
- Filter Buttons - Category, Location, Price Range, Condition
- Item Cards - Photo, Title, Price, Location, Condition
- Sort Options - Recent, Price, Distance, Popularity
**Actions**:
- Tap item card (navigate to item details)
- Pull-to-refresh
- Infinite scroll loading
- Filter/Sort controls

##### Item Details Page (`/item/:id`)
**Content**: Complete item information with seller details
**Components**:
- Image Carousel - swipeable photo gallery
- Item Information - Title, Description, Category, Condition, Price
- Seller Information - Name, Rating, Location, Join Date
- Pickup Details - Location, Availability, Contact Method
- Related Items - Similar items from same seller
**Actions**:
- "Chat with Seller" Button (primary action)
- "Share Item" Button
- "Report Item" Button
- "Save to Favorites" Button
- Image zoom/gallery view

##### Chat Page (`/chat/:userId`)
**Content**: Standard messaging interface with item context
**Components**:
- Chat Header - Other user's name and item reference
- Message History - Text messages, images, timestamps
- Message Input - Text input with send button
- Item Reference Card - Quick view of discussed item
**Actions**:
- Send Message Button
- Attach Photo Button
- View Item Details Link
- Block/Report User options

## 4. Overall App Structure & Navigation

### Mobile-Centric Navigation System
The app uses a bottom tab bar navigation optimized for mobile interaction with thumb-friendly design.

### 5-Tab Bar Structure

#### Tab 1: Home (首頁)
- **Icon**: Home outline
- **Purpose**: Main marketplace feed displaying available items
- **Features**: Search, filter, browse all listings

#### Tab 2: Chui Gai (吹雞)
- **Icon**: Megaphone or broadcast icon
- **Purpose**: Feed for material/item requests from community
- **Features**: Browse requests, create new requests, respond to requests

#### Tab 3: Upload (上傳)
- **Icon**: Plus/Add icon (prominent, different styling)
- **Purpose**: Central action button to create new listings
- **Features**: Quick access to upload item flow

#### Tab 4: Messages (訊息)
- **Icon**: Chat bubbles
- **Purpose**: List of user's conversations and chat management
- **Features**: Active chats, message notifications, chat history

#### Tab 5: Profile (個人檔案)
- **Icon**: Person outline
- **Purpose**: User's personal section and app settings
- **Features**: My listings, favorites, settings, account management

### Additional Navigation Elements
- **Header Navigation**: Back buttons, page titles, action buttons
- **Modal Navigation**: For forms, image viewers, and detailed actions
- **Deep Linking**: Support for sharing specific items and requests
- **Tab Badges**: Notification counts for messages and updates

### Responsive Design Considerations
- Optimized for mobile-first experience
- Touch-friendly button sizes (minimum 44px)
- Swipe gestures for image carousels and navigation
- Pull-to-refresh functionality
- Infinite scroll for feed pages
- Offline capability for viewing cached content

## 5. User Management & Authentication

### User Registration System
- **Multi-step Registration**: Username validation, email verification, profile completion
- **Role-based Access**: Different features available based on user role (Free/Merchant/President)
- **Referral System**: Optional referral codes for user acquisition tracking
- **Industry Classification**: Users categorized by construction industry sectors

### Authentication Features
- **Flexible Login**: Support for both username and email login
- **Password Security**: Minimum requirements and secure storage
- **Auto-login**: Seamless login after successful registration
- **Session Management**: Persistent login with secure token handling

### User Profiles
- **Basic Information**: Username, full name, email, phone, industry
- **Business Information**: Company name, role, professional details
- **Activity Tracking**: Join date, listing history, transaction records
- **Reputation System**: User ratings and feedback from transactions

## 6. Content Management

### Item/Material Listings
- **Rich Media Support**: Multiple photos, detailed descriptions
- **Categorization**: Hierarchical category system for construction materials
- **Condition Assessment**: Standardized condition ratings
- **Location Services**: District-based location system for Hong Kong
- **Pricing Options**: Free items and paid listings with flexible pricing

### Request Management ("Chui Gai")
- **Detailed Specifications**: Comprehensive requirement descriptions
- **Budget Constraints**: Optional budget ranges for cost planning
- **Urgency Levels**: Priority system for time-sensitive requests
- **Response Tracking**: Monitor and manage responses to requests

### Content Moderation
- **User Reporting**: Community-driven content flagging system
- **Automated Filtering**: Basic content validation and spam prevention
- **Admin Controls**: Moderation tools for content management

## 7. Communication System

### Real-time Messaging
- **Direct Chat**: One-on-one messaging between users
- **Item Context**: Messages linked to specific items/requests
- **Media Sharing**: Photo and document sharing in chats
- **Message History**: Persistent chat history and search

### Notification System
- **Push Notifications**: Real-time alerts for messages and updates
- **In-app Notifications**: Activity feed and notification center
- **Email Notifications**: Important updates and summaries
- **Notification Preferences**: User-configurable notification settings

## 8. Technical Requirements

### Performance Requirements
- **Load Time**: App launch under 3 seconds
- **Image Loading**: Progressive image loading with placeholders
- **Offline Support**: Basic functionality available without internet
- **Data Efficiency**: Optimized data usage for mobile networks

### Security Requirements
- **Data Protection**: Secure handling of personal information
- **Authentication Security**: JWT tokens and secure session management
- **API Security**: Rate limiting and request validation
- **Privacy Controls**: User data visibility and sharing preferences

### Platform Compatibility
- **iOS Support**: iOS 12+ compatibility
- **Android Support**: Android 8+ compatibility
- **Cross-platform**: Consistent experience across platforms
- **Device Optimization**: Support for various screen sizes and orientations

### Integration Requirements
- **Camera Integration**: Native camera access for photo capture
- **Location Services**: GPS integration for location-based features
- **Push Notifications**: Native notification system integration
- **Deep Linking**: URL scheme support for content sharing

## 9. Business Logic & Rules

### Listing Rules
- **Item Ownership**: Users can only edit/delete their own listings
- **Listing Limits**: Reasonable limits to prevent spam
- **Content Guidelines**: Community standards for appropriate content
- **Pricing Policies**: Guidelines for fair pricing and free items

### Transaction Guidelines
- **Safety Recommendations**: Best practices for safe exchanges
- **Dispute Resolution**: Basic guidelines for resolving conflicts
- **Community Standards**: Behavioral expectations for users
- **Feedback System**: Post-transaction rating and review system

### Data Retention
- **Active Listings**: Items remain active until marked as taken/sold
- **Chat History**: Messages retained for user convenience
- **User Data**: Account information maintained per privacy policy
- **Analytics Data**: Anonymized usage data for app improvement

## 10. Future Enhancements

### Planned Features
- **Advanced Search**: AI-powered search and recommendations
- **Delivery Integration**: Third-party delivery service integration
- **Payment Processing**: In-app payment system for transactions
- **Business Analytics**: Advanced insights for merchant users
- **Community Features**: User groups and specialized communities

### Scalability Considerations
- **Database Optimization**: Efficient data structure for growth
- **CDN Integration**: Global content delivery for images
- **Microservices**: Modular architecture for feature expansion
- **API Versioning**: Backward compatibility for app updates

This requirements specification provides a comprehensive foundation for the 搵料 (Waste to Gold) app development, ensuring all stakeholders understand the scope, functionality, and technical requirements for successful implementation.
