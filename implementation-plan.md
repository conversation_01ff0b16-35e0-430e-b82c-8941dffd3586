# 搵料 (Waste to Gold) Implementation Plan

## Current State Analysis

### ✅ Already Implemented
- **Authentication System**: Login/Register pages with username/email support
- **Database Structure**: All tables and relationships fixed to use `public.users`
- **Basic Navigation**: 6-tab structure (Home, Products, Material Requests, Shops, Chat, Profile)
- **Material Requests (吹雞)**: Create and browse material requests functionality
- **Chat System**: Real-time messaging with conversation list
- **Product Management**: Add/edit products with rich text editor
- **User Profiles**: Basic user profile management
- **Search & Filtering**: Basic search and category filtering

### 🔄 Partially Implemented
- **Home Page**: Has basic structure but needs marketplace focus
- **Product Listing**: Exists but needs simplification for marketplace UX
- **Navigation**: 6 tabs instead of required 5 tabs
- **Upload Flow**: Currently tied to shops, needs standalone item upload

### ❌ Missing/Needs Major Changes
- **Simplified Upload Flow**: Direct item upload without shop requirement
- **Marketplace-focused Home Feed**: Simple grid of items like Carousell
- **Item Detail Pages**: Simplified view focused on item + seller info
- **5-Tab Navigation**: Need to consolidate to match requirements
- **Direct Item-to-Chat Flow**: Easy "Chat with Seller" functionality

## Implementation Plan

### Phase 1: Core Navigation & Structure (Priority 1 - Do First)

#### Task 1.1: Update Tab Navigation Structure
**Estimated Time**: 2-3 hours
**Files to Modify**:
- `src/views/TabsPage.vue`
- `src/router/index.ts`

**Changes**:
- Remove "Shops" tab (商家)
- Rename "Products" tab to "Upload" (上傳) and change functionality
- Keep: Home (首頁), Upload (上傳), Material Requests (吹雞), Chat (對話), Profile (個人)
- Update icons and routing

#### Task 1.2: Create Simplified Upload Flow
**Estimated Time**: 4-6 hours
**New Files**:
- `src/views/UploadItemPage.vue`
- `src/components/ItemUploadForm.vue`

**Changes**:
- Create standalone item upload page (not tied to shops)
- Simple form: Photos, Title, Description, Category, Condition, Price/Free, Location
- Direct upload to products table with user as owner

### Phase 2: Marketplace Home Feed (Priority 1 - Can Do in Parallel)

#### Task 2.1: Redesign Home Page as Marketplace Feed
**Estimated Time**: 6-8 hours
**Files to Modify**:
- `src/views/HomePage.vue`
- Create `src/components/ItemCard.vue`
- Create `src/components/ItemGrid.vue`

**Changes**:
- Remove complex sections, focus on simple item grid
- Carousell-style item cards with photo, title, price, location
- Search bar at top
- Category filter buttons
- Pull-to-refresh and infinite scroll

#### Task 2.2: Create Simplified Item Detail Page
**Estimated Time**: 4-5 hours
**New Files**:
- `src/views/ItemDetailPage.vue`
- `src/components/ItemImageCarousel.vue`
- `src/components/SellerInfo.vue`

**Changes**:
- Clean item detail view with image carousel
- Item info: title, description, price, condition, location
- Seller info section
- Prominent "Chat with Seller" button
- Share and favorite buttons

### Phase 3: Enhanced Chat Integration (Priority 2)

#### Task 3.1: Improve Chat-to-Item Flow
**Estimated Time**: 3-4 hours
**Files to Modify**:
- `src/views/ChatroomPage.vue`
- `src/stores/chat.ts`

**Changes**:
- Add item context to chat messages
- Show item reference card in chat
- Easy navigation back to item from chat
- Auto-populate initial message with item interest

#### Task 3.2: Update Conversation List
**Estimated Time**: 2-3 hours
**Files to Modify**:
- `src/views/ConversationListPage.vue`

**Changes**:
- Show item thumbnails in conversation list
- Better conversation previews
- Unread message indicators

### Phase 4: Material Requests Enhancement (Priority 2 - Can Do in Parallel)

#### Task 4.1: Improve Material Requests UI
**Estimated Time**: 3-4 hours
**Files to Modify**:
- `src/views/MaterialRequestsPage.vue`
- `src/views/CreateMaterialRequestPage.vue`

**Changes**:
- Better visual design for request cards
- Improved create request form
- Response management system

### Phase 5: Search & Discovery (Priority 3)

#### Task 5.1: Enhanced Search Functionality
**Estimated Time**: 4-5 hours
**Files to Modify**:
- `src/views/HomePage.vue`
- Create `src/components/SearchFilters.vue`

**Changes**:
- Advanced search with multiple filters
- Location-based filtering
- Price range filters
- Category and condition filters
- Search history and suggestions

#### Task 5.2: Category Browse Page
**Estimated Time**: 2-3 hours
**Files to Modify**:
- `src/views/CategoryBrowsePage.vue`

**Changes**:
- Visual category grid
- Category-specific item feeds

### Phase 6: User Experience Improvements (Priority 3)

#### Task 6.1: Profile Page Enhancements
**Estimated Time**: 3-4 hours
**Files to Modify**:
- `src/views/ProfilePage.vue`

**Changes**:
- "My Listings" section
- "My Requests" section
- Favorites management
- Account settings

#### Task 6.2: Favorites System
**Estimated Time**: 2-3 hours
**New Files**:
- `src/views/FavoritesPage.vue`
- `src/stores/favorites.ts`

**Changes**:
- Save/unsave items
- Favorites list page
- Heart icons on item cards

### Phase 7: Polish & Optimization (Priority 4)

#### Task 7.1: Performance Optimization
**Estimated Time**: 2-3 hours
**Changes**:
- Image lazy loading
- Infinite scroll optimization
- Caching improvements

#### Task 7.2: UI/UX Polish
**Estimated Time**: 3-4 hours
**Changes**:
- Consistent styling with green theme
- Loading states
- Error handling
- Empty states

## Parallel Development Strategy

### Week 1: Foundation
- **Developer A**: Phase 1 (Navigation & Upload Flow)
- **Developer B**: Phase 2.1 (Home Page Redesign)

### Week 2: Core Features
- **Developer A**: Phase 2.2 (Item Detail Page)
- **Developer B**: Phase 3 (Chat Integration)

### Week 3: Enhancement
- **Developer A**: Phase 4 (Material Requests)
- **Developer B**: Phase 5 (Search & Discovery)

### Week 4: Polish
- **Both**: Phase 6 & 7 (UX Improvements & Polish)

## Technical Considerations

### Database Changes Needed
- Modify products table to support direct user ownership (not just shop-based)
- Add item status field (available, sold, given away)
- Add favorites/likes relationship table

### State Management Updates
- Update Pinia stores for new item management flow
- Add favorites store
- Update chat store for item context

### Component Reusability
- Create reusable ItemCard component
- Standardize image handling components
- Create consistent form components

## Success Metrics

### User Experience Goals
- ✅ Simple 3-tap flow: Browse → View Item → Chat with Seller
- ✅ Fast item upload (under 2 minutes)
- ✅ Intuitive navigation matching Carousell UX patterns
- ✅ Responsive design for mobile-first experience

### Technical Goals
- ✅ Page load times under 2 seconds
- ✅ Smooth scrolling and transitions
- ✅ Offline capability for browsing cached items
- ✅ Real-time chat functionality

This implementation plan prioritizes the core marketplace functionality while maintaining the existing working features. The parallel development approach allows for efficient progress while minimizing conflicts.
